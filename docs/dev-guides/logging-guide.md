# Logging Guide

This guide provides comprehensive documentation for the clinical logging system in pyrt-dicom. The logging system is designed to support clinical audit trails, debugging, and operational monitoring with a focus on patient safety and regulatory compliance.

## Table of Contents
- [Introduction](#introduction)
- [Getting Started](#getting-started)
- [Logging Components](#logging-components)
  - [ClinicalFormatter](#clinicalformatter)
  - [get_clinical_logger](#get_clinical_logger)
  - [log_dicom_creation](#log_dicom_creation)
  - [log_validation_result](#log_validation_result)
- [Log Format](#log-format)
- [Best Practices](#best-practices)
- [Troubleshooting](#troubleshooting)
- [Performance Considerations](#performance-considerations)
- [Privacy and Compliance](#privacy-and-compliance)

## Introduction

The pyrt-dicom logging system provides structured, JSON-formatted logs that are essential for clinical audit trails. The system is built on Python's standard `logging` module but extends it with clinical-specific functionality and formatting.

Key features include:
- Structured JSON output for easy parsing and analysis
- Thread-safe logging for concurrent operations
- Built-in support for clinical context (patient ID, study UID, etc.)
- Configurable log levels and output formats
- Performance-optimized for clinical workflows

## Getting Started

To start using the logging system in your module:

```python
from pyrt_dicom.utils.logging import get_clinical_logger

# Create a logger for your module
logger = get_clinical_logger(__name__)

# Basic logging
logger.info("Application started")
logger.warning("Low disk space detected")
logger.error("Failed to save DICOM file")
```

## Logging Components

### ClinicalFormatter

The `ClinicalFormatter` class formats log records as JSON with a consistent structure. It automatically includes:
- Timestamp (UTC)
- Log level
- Source module, function, and line number
- Log message
- Any additional context provided

### get_clinical_logger

Retrieves a configured logger instance with clinical formatting.

```python
def get_clinical_logger(name: str) -> logging.Logger:
    """
    Get a logger configured for clinical audit logging.
    
    Args:
        name: Logger name, typically __name__ from calling module
        
    Returns:
        Configured logger with clinical formatting
    """
```

### log_dicom_creation

Logs DICOM creation operations with clinical context.

```python
def log_dicom_creation(
    logger: logging.Logger,
    operation: str,
    patient_id: Optional[str] = None,
    study_uid: Optional[str] = None,
    **kwargs: Union[str, int, float, bool, None],
) -> None:
    """
    Log DICOM creation operation with clinical context.
    
    Args:
        logger: Logger instance
        operation: Description of the DICOM creation operation
        patient_id: Patient identifier (if not anonymized)
        study_uid: Study instance UID
        **kwargs: Additional clinical or technical context
    """
```

Example usage:
```python
log_dicom_creation(
    logger,
    "RT Plan creation",
    patient_id="PAT_123",
    study_uid="1.2.826.0.1.3680043.8.498.12345",
    modality="RTPLAN",
    plan_name="Prostate_IMRT"
)
```

### log_validation_result

Logs validation results with clinical context.

```python
def log_validation_result(
    logger: logging.Logger,
    validation_type: str,
    result: bool,
    details: Optional[Dict[str, Any]] = None,
    **kwargs: Union[str, int, float, bool, None],
) -> None:
    """
    Log validation results with clinical audit trail.
    
    Args:
        logger: Logger instance
        validation_type: Type of validation performed
        result: Validation pass/fail result
        details: Additional validation details
        **kwargs: Additional context
    """
```

Example usage:
```python
log_validation_result(
    logger,
    "dose_constraints",
    passed=True,
    details={
        "constraint": "PTV D98% > 95%",
        "actual_value": 96.5,
        "unit": "%"
    },
    patient_id="PAT_123",
    study_uid="1.2.826.0.1.3680043.8.498.12345"
)
```

## Log Format

Logs are output as compact JSON with these guaranteed fields:

```json
{
    "timestamp": "2024-01-15T14:30:22.123456+00:00",
    "level": "INFO",
    "module": "ct_series",
    "function": "create_from_array",
    "line": 45,
    "message": "DICOM creation: CT series creation",
    "operation": "CT series creation",
    "patient_id": "PAT_001",
    "study_uid": "1.2.826.0.1.3680043.8.498.12345"
}
```

## Best Practices

1. **Use Appropriate Log Levels**
   - `DEBUG`: Detailed information for debugging
   - `INFO`: Confirmation of normal operation
   - `WARNING`: Indication of potential issues
   - `ERROR`: Errors that don't prevent the application from continuing
   - `CRITICAL`: Severe errors that prevent the application from continuing

2. **Include Relevant Context**
   - Always include `patient_id` and `study_uid` when available
   - Provide sufficient technical details for debugging
   - Include operation-specific metrics and results

3. **Structured Logging**
   - Use the provided helper functions for structured logging
   - Pass additional context as keyword arguments
   - Keep log messages concise but informative

## Troubleshooting

### Duplicate Log Messages
If you see duplicate log messages, ensure you're not adding multiple handlers to the same logger. The `get_clinical_logger` function prevents this automatically.

### Missing Logs
If logs aren't appearing:
1. Check the log level (default is INFO)
2. Verify no filters are blocking the logs
3. Ensure the logger is properly configured

### Performance Issues
For high-volume logging:
1. Use appropriate log levels to filter noise
2. Consider batching related log messages
3. Use the built-in JSON formatter for efficiency

## Performance Considerations

The logging system is optimized for clinical workflows:
- JSON formatting uses compact separators
- Logging calls are non-blocking
- Minimal overhead for INFO level and above
- Thread-safe for concurrent operations

## Privacy and Compliance

### Patient Privacy
- Set `patient_id` to `None` for anonymous operations
- Avoid logging protected health information (PHI) in free-text fields
- Use structured fields for sensitive data

### Audit Compliance
- All logs include timestamps in UTC
- Structured format supports automated analysis
- Logs include sufficient context for clinical audits

### Log Retention
- Follow your organization's log retention policies
- Consider regulatory requirements for clinical data
- Ensure secure storage of logs containing PHI
