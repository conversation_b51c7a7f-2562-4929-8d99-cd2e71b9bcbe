"""
DICOM Anonymization Module for pyrt-dicom.

This module provides comprehensive DICOM anonymization capabilities specifically
designed for radiotherapy data, ensuring clinical validity is preserved while
removing patient identifiers for research and data sharing.

## Key Features

- **RT-Specific Anonymization**: Preserves dose scaling, geometric relationships,
  and clinical parameters essential for radiotherapy data validity
- **Multiple Profiles**: Basic, clinical, research, and HIPAA-compliant anonymization
- **Consistent UID Mapping**: Maintains relationships between related RT objects
- **Audit Logging**: Comprehensive logging for regulatory compliance
- **Treatment Plan Support**: Specialized handling for complete treatment plans

## Quick Start

```python
from pyrt_dicom.anonymization import DicomAnonymizer, TreatmentPlanAnonymizer

# Basic anonymization
anonymizer = DicomAnonymizer(profile='basic')
anonymized_dataset = anonymizer.anonymize_dataset(original_dataset)

# Treatment plan anonymization
plan_anonymizer = TreatmentPlanAnonymizer(profile='clinical')
anonymized_files = plan_anonymizer.anonymize_treatment_plan({
    'ct': 'planning_ct.dcm',
    'struct': 'structures.dcm', 
    'dose': 'dose.dcm',
    'plan': 'plan.dcm'
}, output_directory='anonymized/')
```

## Anonymization Profiles

- **Basic**: Removes direct identifiers, preserves all clinical data
- **Clinical**: Clinical anonymization preserving analysis capability
- **Research**: Research anonymization for multi-institutional studies  
- **HIPAA**: HIPAA Safe Harbor compliant anonymization

## Clinical Safety

All anonymization profiles preserve critical RT data:
- Dose scaling factors and units
- Geometric relationships and coordinate systems
- Structure names (configurable)
- Treatment machine parameters
- Frame of reference relationships

## Audit Compliance

Comprehensive audit logging tracks:
- Anonymization operations and timestamps
- Profile used and tags modified
- Compliance level achieved
- Risk assessment for privacy protection
"""

from .anonymizer import DicomAnonymizer, TreatmentPlanAnonymizer
from .profiles import AnonymizationProfiles
from .audit import AnonymizationAuditor

__all__ = [
    'DicomAnonymizer',
    'TreatmentPlanAnonymizer', 
    'AnonymizationProfiles',
    'AnonymizationAuditor'
]
