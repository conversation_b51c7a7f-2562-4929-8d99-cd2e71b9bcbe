"""
Core DICOM anonymization classes for radiotherapy data.

This module provides the main anonymization functionality for DICOM RT files,
with specialized handling for preserving clinical validity while removing
patient identifiers.

## Key Features

- **RT-Specific Anonymization**: Preserves dose scaling, geometric relationships
- **Consistent UID Mapping**: Maintains relationships between related RT objects
- **Treatment Plan Support**: Specialized handling for complete treatment plans
- **Profile-Based Configuration**: Multiple anonymization levels
- **Audit Integration**: Comprehensive logging for compliance

## Usage Examples

```python
from pyrt_dicom.anonymization import DicomAnonymizer, TreatmentPlanAnonymizer

# Single file anonymization
anonymizer = DicomAnonymizer(profile='clinical')
anonymized_ds = anonymizer.anonymize_dataset(original_dataset)

# Treatment plan anonymization
plan_anonymizer = TreatmentPlanAnonymizer(profile='research')
result = plan_anonymizer.anonymize_treatment_plan({
    'ct': 'planning_ct.dcm',
    'struct': 'structures.dcm',
    'dose': 'dose.dcm',
    'plan': 'plan.dcm'
}, output_directory='anonymized/')
```

## Clinical Safety

All anonymization preserves critical RT data:
- Dose grid scaling and units
- Geometric relationships and coordinate systems
- Frame of reference relationships
- Treatment machine parameters (configurable)
"""

import logging
import hashlib
from datetime import datetime, timedelta
from pathlib import Path
from typing import Dict, List, Any, Optional, Union, Tuple
from uuid import uuid4
import random

import pydicom
from pydicom import Dataset
from pydicom.uid import generate_uid

from .profiles import AnonymizationProfiles
from .audit import AnonymizationAuditor

logger = logging.getLogger(__name__)


class DicomAnonymizer:
    """
    DICOM anonymization with RT-specific handling.

    Provides comprehensive anonymization of DICOM datasets while preserving
    clinical validity of radiotherapy data through RT-specific tag handling
    and consistent UID mapping.
    """

    def __init__(
        self,
        profile: Union[str, Dict[str, Any]] = "basic",
        auditor: Optional[AnonymizationAuditor] = None,
        uid_prefix: Optional[str] = None,
    ):
        """
        Initialize DICOM anonymizer.

        Args:
            profile: Anonymization profile name or custom profile dictionary
            auditor: Optional auditor for logging operations
            uid_prefix: Optional prefix for generated UIDs

        Examples:
            >>> anonymizer = DicomAnonymizer(profile='clinical')
            >>> anonymizer = DicomAnonymizer(profile={'name': 'custom', ...})
            >>> anonymizer = DicomAnonymizer(profile='hipaa', uid_prefix='*******')
        """
        if isinstance(profile, str):
            self.profile = AnonymizationProfiles.get_profile(profile)
        else:
            self.profile = profile
            AnonymizationProfiles.validate_profile(profile)

        self.auditor = auditor or AnonymizationAuditor()
        self.uid_prefix = uid_prefix
        self._uid_mapping: Dict[str, str] = {}  # Consistent UID anonymization
        self._date_shift: Optional[int] = None  # Consistent date shifting

        logger.info(f"Initialized anonymizer with profile: {self.profile['name']}")

    def anonymize_dataset(
        self, dataset: Dataset, profile: Optional[Union[str, Dict[str, Any]]] = None
    ) -> Dataset:
        """
        Anonymize DICOM dataset with RT-specific handling.

        Args:
            dataset: pydicom.Dataset to anonymize
            profile: Override default anonymization profile

        Returns:
            Anonymized dataset with preserved RT relationships

        Examples:
            >>> anonymized_ds = anonymizer.anonymize_dataset(original_ds)
            >>> anonymized_ds = anonymizer.anonymize_dataset(ds, profile='hipaa')
        """
        # Use provided profile or default
        if profile is not None:
            if isinstance(profile, str):
                active_profile = AnonymizationProfiles.get_profile(profile)
            else:
                active_profile = profile
        else:
            active_profile = self.profile

        # Create deep copy of dataset to avoid modifying original
        import copy

        anonymized_ds = copy.deepcopy(dataset)

        # Apply anonymization steps
        self._anonymize_patient_identifiers(anonymized_ds, active_profile)
        self._anonymize_dates(anonymized_ds, active_profile)
        self._anonymize_uids(anonymized_ds, active_profile)
        self._anonymize_free_text(anonymized_ds, active_profile)
        self._preserve_rt_relationships(anonymized_ds, active_profile)
        self._validate_rt_clinical_integrity(anonymized_ds, active_profile)

        # Log anonymization operation
        audit_entry = self.auditor.log_anonymization(
            dataset, anonymized_ds, active_profile["name"]
        )

        logger.info(
            f"Anonymized dataset with profile {active_profile['name']}, "
            f"audit ID: {audit_entry['log_id']}"
        )

        return anonymized_ds

    def _anonymize_patient_identifiers(
        self, dataset: Dataset, profile: Dict[str, Any]
    ) -> None:
        """
        Anonymize patient identifying information.

        Args:
            dataset: Dataset to modify
            profile: Anonymization profile configuration
        """
        if not profile.get("remove_patient_identifiers", True):
            return

        # Core patient identifiers
        dataset.PatientName = "ANONYMOUS"
        dataset.PatientID = self._generate_anonymous_id()

        if hasattr(dataset, "PatientBirthDate"):
            if profile.get("remove_dates", False) or not profile.get(
                "preserve_dates", False
            ):
                dataset.PatientBirthDate = ""
            else:
                # Shift birth date if date shifting is enabled
                dataset.PatientBirthDate = self._shift_date(
                    dataset.PatientBirthDate, profile
                )

        # Optional patient information
        if hasattr(dataset, "PatientSex") and profile.get(
            "anonymize_patient_sex", False
        ):
            dataset.PatientSex = ""

        if hasattr(dataset, "PatientAge"):
            if profile.get("age_threshold"):
                # HIPAA requires ages >89 to be set to 90+
                try:
                    age = int(dataset.PatientAge.replace("Y", ""))
                    if age > profile["age_threshold"]:
                        dataset.PatientAge = "90Y"
                except (ValueError, AttributeError):
                    dataset.PatientAge = ""
            else:
                dataset.PatientAge = ""

    def _anonymize_dates(self, dataset: Dataset, profile: Dict[str, Any]) -> None:
        """
        Anonymize or shift dates while preserving temporal relationships.

        Args:
            dataset: Dataset to modify
            profile: Anonymization profile configuration
        """
        if profile.get("remove_dates", False):
            # Remove all dates for strict anonymization
            date_tags = ["StudyDate", "SeriesDate", "AcquisitionDate", "ContentDate"]
            for tag in date_tags:
                if hasattr(dataset, tag):
                    setattr(dataset, tag, "")
        elif profile.get("preserve_dates", False) and profile.get("date_shift_range"):
            # Shift dates by consistent amount
            if self._date_shift is None:
                min_shift, max_shift = profile["date_shift_range"]
                self._date_shift = random.randint(min_shift, max_shift)

            date_tags = ["StudyDate", "SeriesDate", "AcquisitionDate", "ContentDate"]
            for tag in date_tags:
                if hasattr(dataset, tag):
                    original_date = getattr(dataset, tag)
                    shifted_date = self._shift_date(original_date, profile)
                    setattr(dataset, tag, shifted_date)

    def _shift_date(self, date_string: str, profile: Dict[str, Any]) -> str:
        """
        Shift a date string by the consistent offset.

        Args:
            date_string: Original date in DICOM format (YYYYMMDD)
            profile: Anonymization profile

        Returns:
            Shifted date string
        """
        if not date_string or len(date_string) < 8:
            return date_string

        try:
            # Parse DICOM date format
            year = int(date_string[:4])
            month = int(date_string[4:6])
            day = int(date_string[6:8])

            original_date = datetime(year, month, day)
            shifted_date = original_date - timedelta(days=self._date_shift or 0)

            return shifted_date.strftime("%Y%m%d")
        except (ValueError, IndexError):
            logger.warning(f"Could not parse date: {date_string}")
            return ""

    def _anonymize_uids(self, dataset: Dataset, profile: Dict[str, Any]) -> None:
        """
        Anonymize UIDs while maintaining relationships.

        Args:
            dataset: Dataset to modify
            profile: Anonymization profile configuration
        """
        uid_tags = [
            "StudyInstanceUID",
            "SeriesInstanceUID",
            "SOPInstanceUID",
            "FrameOfReferenceUID",
            "ReferencedFrameOfReferenceUID",
        ]

        for tag in uid_tags:
            if hasattr(dataset, tag):
                original_uid = getattr(dataset, tag)
                if original_uid not in self._uid_mapping:
                    # Generate new UID with optional prefix
                    if self.uid_prefix:
                        new_uid = f"{self.uid_prefix}.{generate_uid()}"
                    else:
                        new_uid = generate_uid()
                    self._uid_mapping[original_uid] = new_uid

                setattr(dataset, tag, self._uid_mapping[original_uid])

    def _anonymize_free_text(self, dataset: Dataset, profile: Dict[str, Any]) -> None:
        """
        Anonymize free text fields that might contain identifying information.

        Args:
            dataset: Dataset to modify
            profile: Anonymization profile configuration
        """
        # Handle institution information based on profile
        if not profile.get("preserve_institution_info", True):
            if hasattr(dataset, "InstitutionName"):
                dataset.InstitutionName = ""
            if hasattr(dataset, "InstitutionAddress"):
                dataset.InstitutionAddress = ""

        # Handle physician information based on profile
        if not profile.get("preserve_physician_info", False):
            physician_tags = [
                "ReferringPhysicianName",
                "PerformingPhysicianName",
                "OperatorsName",
            ]
            for tag in physician_tags:
                if hasattr(dataset, tag):
                    setattr(dataset, tag, "")

        # Handle free text anonymization
        if profile.get("anonymize_free_text", False):
            text_tags = [
                "StudyDescription",
                "SeriesDescription",
                "ProtocolName",
            ]

            for tag in text_tags:
                if hasattr(dataset, tag):
                    if (
                        profile.get("anonymize_study_descriptions", False)
                        and "Description" in tag
                    ):
                        setattr(dataset, tag, "ANONYMIZED_STUDY")
                    else:
                        setattr(dataset, tag, "")

    def _preserve_rt_relationships(
        self, dataset: Dataset, profile: Dict[str, Any]
    ) -> None:
        """
        Preserve RT-specific relationships and clinical data.

        Args:
            dataset: Dataset to modify
            profile: Anonymization profile configuration
        """
        # Preserve tags specified in profile
        preserve_tags = profile.get("preserve_tags", [])

        # RT-specific preservation logic
        if hasattr(dataset, "Modality"):
            modality = dataset.Modality

            if modality == "RTSTRUCT" and profile.get("preserve_structure_names", True):
                # Preserve structure names for clinical analysis
                if hasattr(dataset, "StructureSetROISequence"):
                    for roi in dataset.StructureSetROISequence:
                        if hasattr(roi, "ROIName") and "ROIName" not in preserve_tags:
                            # Optionally anonymize structure names
                            if not profile.get("preserve_structure_names", True):
                                roi.ROIName = f"STRUCTURE_{roi.ROINumber}"

            elif modality == "RTPLAN" and profile.get("preserve_physician_info", False):
                # Handle physician information in RT plans
                pass  # Implementation depends on specific requirements

    def _validate_rt_clinical_integrity(
        self, dataset: Dataset, profile: Dict[str, Any]
    ) -> None:
        """
        Validate that critical RT data is preserved after anonymization.

        Args:
            dataset: Anonymized dataset
            profile: Anonymization profile used

        Raises:
            ValueError: If critical RT data is missing or invalid
        """
        if hasattr(dataset, "Modality"):
            modality = dataset.Modality

            if modality == "RTDOSE":
                # Validate dose-specific requirements
                if not hasattr(dataset, "DoseGridScaling"):
                    raise ValueError("Critical RT data missing: DoseGridScaling")
                if not hasattr(dataset, "DoseUnits"):
                    raise ValueError("Critical RT data missing: DoseUnits")

            elif modality in ["CT", "RTSTRUCT", "RTDOSE"]:
                # Validate geometric requirements
                if not hasattr(dataset, "PixelSpacing"):
                    logger.warning("Geometric data missing: PixelSpacing")
                if not hasattr(dataset, "ImageOrientationPatient"):
                    logger.warning("Geometric data missing: ImageOrientationPatient")

    def _generate_anonymous_id(self) -> str:
        """
        Generate anonymous patient ID.

        Returns:
            Anonymous patient identifier
        """
        return f"ANON_{uuid4().hex[:8].upper()}"


class TreatmentPlanAnonymizer(DicomAnonymizer):
    """
    Specialized anonymizer for complete treatment plans.

    Handles anonymization of related RT objects (CT, STRUCT, DOSE, PLAN)
    while preserving all clinical relationships and references.
    """

    def __init__(
        self,
        profile: Union[str, Dict[str, Any]] = "clinical",
        auditor: Optional[AnonymizationAuditor] = None,
        uid_prefix: Optional[str] = None,
    ):
        """
        Initialize treatment plan anonymizer.

        Args:
            profile: Anonymization profile optimized for treatment plans
            auditor: Optional auditor for logging operations
            uid_prefix: Optional prefix for generated UIDs

        Examples:
            >>> plan_anonymizer = TreatmentPlanAnonymizer(profile='research')
            >>> plan_anonymizer = TreatmentPlanAnonymizer(profile='hipaa')
        """
        super().__init__(profile, auditor, uid_prefix)
        self._plan_uid_mapping: Dict[str, str] = {}

    def anonymize_treatment_plan(
        self,
        plan_files: Dict[str, Union[str, Path]],
        output_directory: Union[str, Path],
        preserve_relationships: bool = True,
    ) -> Dict[str, Path]:
        """
        Anonymize complete treatment plan with consistent mapping.

        Args:
            plan_files: Dict with keys 'ct', 'struct', 'dose', 'plan' and file paths
            output_directory: Where to save anonymized files
            preserve_relationships: Whether to maintain UID relationships

        Returns:
            Dict of anonymized file paths with preserved relationships

        Examples:
            >>> files = {
            ...     'ct': 'planning_ct.dcm',
            ...     'struct': 'structures.dcm',
            ...     'dose': 'dose.dcm',
            ...     'plan': 'plan.dcm'
            ... }
            >>> result = plan_anonymizer.anonymize_treatment_plan(
            ...     files, 'anonymized_output/'
            ... )
            >>> print(f"Anonymized files: {list(result.keys())}")
        """
        output_dir = Path(output_directory)
        output_dir.mkdir(parents=True, exist_ok=True)

        # Create consistent anonymization mapping for the entire plan
        if preserve_relationships:
            self._create_plan_anonymization_map(plan_files)

        # Anonymize each component with consistent UIDs
        anonymized_files = {}
        for modality, filepath in plan_files.items():
            if filepath and Path(filepath).exists():
                anonymized_files[modality] = self._anonymize_with_mapping(
                    filepath, output_dir, modality
                )
            else:
                logger.warning(f"File not found for {modality}: {filepath}")

        # Validate all references are preserved
        if preserve_relationships:
            self._validate_plan_relationships(anonymized_files)

        # Log treatment plan anonymization
        self.auditor.log_anonymization(
            None,
            None,  # Will be enhanced to handle multiple datasets
            profile_name=self.profile["name"],
            operation_type="treatment_plan",
        )

        logger.info(f"Anonymized treatment plan with {len(anonymized_files)} files")
        return anonymized_files

    def _create_plan_anonymization_map(
        self, plan_files: Dict[str, Union[str, Path]]
    ) -> None:
        """
        Create consistent anonymization mapping for related DICOM objects.

        Args:
            plan_files: Dictionary of plan files to analyze
        """
        # Read all files to extract UIDs and relationships
        datasets = {}
        for modality, filepath in plan_files.items():
            if filepath and Path(filepath).exists():
                try:
                    datasets[modality] = pydicom.dcmread(filepath)
                except Exception as e:
                    logger.error(f"Could not read {modality} file {filepath}: {e}")

        # Extract common UIDs that need consistent mapping
        common_uids = set()
        for dataset in datasets.values():
            if hasattr(dataset, "StudyInstanceUID"):
                common_uids.add(dataset.StudyInstanceUID)
            if hasattr(dataset, "FrameOfReferenceUID"):
                common_uids.add(dataset.FrameOfReferenceUID)

        # Create consistent mapping for common UIDs
        for uid in common_uids:
            if uid not in self._uid_mapping:
                if self.uid_prefix:
                    new_uid = f"{self.uid_prefix}.{generate_uid()}"
                else:
                    new_uid = generate_uid()
                self._uid_mapping[uid] = new_uid

    def _anonymize_with_mapping(
        self, filepath: Union[str, Path], output_directory: Path, modality: str
    ) -> Path:
        """
        Anonymize single file with consistent UID mapping.

        Args:
            filepath: Input file path
            output_directory: Output directory
            modality: DICOM modality type

        Returns:
            Path to anonymized file
        """
        # Read original dataset
        dataset = pydicom.dcmread(filepath)

        # Anonymize using parent class method
        anonymized_dataset = self.anonymize_dataset(dataset)

        # Generate output filename
        output_filename = f"anonymized_{modality}.dcm"
        output_path = output_directory / output_filename

        # Save anonymized dataset
        anonymized_dataset.save_as(output_path)

        return output_path

    def _validate_plan_relationships(self, anonymized_files: Dict[str, Path]) -> None:
        """
        Validate that all references are preserved in anonymized plan.

        Args:
            anonymized_files: Dictionary of anonymized file paths

        Raises:
            ValueError: If critical relationships are broken
        """
        # Read anonymized datasets
        datasets = {}
        for modality, filepath in anonymized_files.items():
            try:
                datasets[modality] = pydicom.dcmread(filepath)
            except Exception as e:
                logger.error(f"Could not validate {modality} file {filepath}: {e}")
                continue

        # Validate Study Instance UID consistency
        study_uids = set()
        for modality, dataset in datasets.items():
            if hasattr(dataset, "StudyInstanceUID"):
                study_uids.add(dataset.StudyInstanceUID)

        if len(study_uids) > 1:
            logger.warning(f"Multiple Study Instance UIDs found: {study_uids}")

        # Validate Frame of Reference UID consistency for spatial objects
        spatial_modalities = ["ct", "struct", "dose"]
        frame_uids = set()
        for modality in spatial_modalities:
            if modality in datasets:
                dataset = datasets[modality]
                if hasattr(dataset, "FrameOfReferenceUID"):
                    frame_uids.add(dataset.FrameOfReferenceUID)

        if len(frame_uids) > 1:
            raise ValueError(f"Frame of Reference UID mismatch: {frame_uids}")

        logger.info("Treatment plan relationship validation passed")

    def anonymize_directory(
        self,
        input_directory: Union[str, Path],
        output_directory: Union[str, Path],
        file_pattern: str = "*.dcm",
    ) -> Dict[str, List[Path]]:
        """
        Batch anonymize all DICOM files in a directory.

        Args:
            input_directory: Directory containing DICOM files
            output_directory: Directory for anonymized files
            file_pattern: Glob pattern for DICOM files

        Returns:
            Dictionary mapping modalities to lists of anonymized files

        Examples:
            >>> result = plan_anonymizer.anonymize_directory(
            ...     'patient_data/', 'anonymized_data/'
            ... )
            >>> print(f"Anonymized {sum(len(files) for files in result.values())} files")
        """
        input_dir = Path(input_directory)
        output_dir = Path(output_directory)
        output_dir.mkdir(parents=True, exist_ok=True)

        # Find all DICOM files
        dicom_files = list(input_dir.glob(file_pattern))

        # Group files by modality
        modality_files = {}
        for filepath in dicom_files:
            try:
                dataset = pydicom.dcmread(filepath)
                modality = getattr(dataset, "Modality", "UNKNOWN")
                if modality not in modality_files:
                    modality_files[modality] = []
                modality_files[modality].append(filepath)
            except Exception as e:
                logger.error(f"Could not read file {filepath}: {e}")

        # Anonymize files by modality
        anonymized_files = {}
        for modality, files in modality_files.items():
            anonymized_files[modality] = []
            for filepath in files:
                try:
                    output_path = self._anonymize_with_mapping(
                        filepath, output_dir, f"{modality}_{filepath.stem}"
                    )
                    anonymized_files[modality].append(output_path)
                except Exception as e:
                    logger.error(f"Could not anonymize {filepath}: {e}")

        logger.info(f"Batch anonymized {len(dicom_files)} files from {input_dir}")
        return anonymized_files
