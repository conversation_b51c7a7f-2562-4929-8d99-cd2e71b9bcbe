"""
Anonymization profiles for different clinical and research scenarios.

This module defines predefined anonymization profiles that balance privacy
protection with clinical data utility for radiotherapy applications.

## Profile Design Philosophy

Each profile is designed for specific use cases:
- **Basic**: Quick anonymization for internal use
- **Clinical**: Preserves temporal and clinical analysis capability
- **Research**: Multi-institutional study compatibility
- **HIPAA**: Regulatory compliance for US healthcare

## RT-Specific Considerations

All profiles preserve critical radiotherapy data elements:
- Dose grid scaling and units (essential for dose accuracy)
- Geometric relationships (pixel spacing, slice thickness)
- Image orientation (critical for spatial accuracy)
- Frame of reference UIDs (maintains object relationships)

## Usage Examples

```python
from pyrt_dicom.anonymization.profiles import AnonymizationProfiles

# Get clinical profile for analysis workflows
clinical_profile = AnonymizationProfiles.get_profile('clinical')

# Create custom profile based on research profile
custom_profile = AnonymizationProfiles.RESEARCH_PROFILE.copy()
custom_profile['preserve_structure_names'] = False

# List available profiles
profiles = AnonymizationProfiles.list_profiles()
```
"""

from typing import Dict, List, Any, Optional
import logging

logger = logging.getLogger(__name__)


class AnonymizationProfiles:
    """
    Predefined anonymization profiles for different clinical scenarios.
    
    Provides standardized anonymization configurations that balance privacy
    protection with clinical data utility for radiotherapy applications.
    """
    
    # Core RT tags that must be preserved for clinical validity
    _RT_CRITICAL_TAGS = [
        'DoseGridScaling',      # Critical for dose accuracy
        'DoseUnits',            # Must preserve for clinical validity
        'PixelSpacing',         # Geometric accuracy
        'SliceThickness',       # Geometric accuracy
        'ImageOrientationPatient',  # Spatial orientation
        'ImagePositionPatient', # Spatial positioning (may be shifted)
        'FrameOfReferenceUID',  # Maintains object relationships
        'Modality',             # Required for proper interpretation
    ]
    
    BASIC_PROFILE = {
        'name': 'basic',
        'description': 'Basic anonymization removing direct identifiers',
        'remove_patient_identifiers': True,
        'preserve_dates': False,
        'preserve_geometric_data': True,
        'preserve_dose_data': True,
        'preserve_structure_names': False,
        'preserve_physician_info': False,
        'preserve_institution_info': True,
        'preserve_tags': _RT_CRITICAL_TAGS + [
            'StudyDate',  # May be shifted but preserved
            'SeriesDate',
        ],
        'date_shift_range': None,  # No date shifting
        'anonymize_free_text': True,
        'compliance_level': 'Basic'
    }
    
    CLINICAL_PROFILE = {
        'name': 'clinical',
        'description': 'Clinical anonymization preserving analysis capability',
        'remove_patient_identifiers': True,
        'preserve_dates': True,  # Preserve relative dates for temporal analysis
        'preserve_geometric_data': True,
        'preserve_dose_data': True,
        'preserve_structure_names': True,  # Keep ROI names for clinical analysis
        'preserve_physician_info': False,
        'preserve_institution_info': True,
        'preserve_tags': _RT_CRITICAL_TAGS + [
            'ROIName',              # Structure names for analysis
            'BeamName',             # Beam identification
            'TreatmentMachineName', # Machine-specific analysis
            'StudyDate',            # Temporal relationships
            'SeriesDate',
            'StudyDescription',     # Clinical context
            'SeriesDescription',
        ],
        'date_shift_range': (30, 365),  # Random shift 30-365 days
        'anonymize_free_text': False,   # Preserve clinical descriptions
        'compliance_level': 'Clinical'
    }
    
    RESEARCH_PROFILE = {
        'name': 'research', 
        'description': 'Research anonymization for multi-institutional studies',
        'remove_patient_identifiers': True,
        'preserve_dates': True,
        'preserve_geometric_data': True,
        'preserve_dose_data': True,
        'preserve_structure_names': True,
        'preserve_physician_info': False,
        'preserve_institution_info': False,  # Remove for multi-site studies
        'preserve_tags': _RT_CRITICAL_TAGS + [
            'ROIName',
            'BeamName',
            'StudyDate',
            'SeriesDate',
            'StudyDescription',
            'SeriesDescription',
        ],
        'date_shift_range': (30, 730),  # Larger shift range for research
        'anonymize_study_descriptions': True,
        'anonymize_free_text': True,
        'compliance_level': 'Research'
    }
    
    HIPAA_PROFILE = {
        'name': 'hipaa',
        'description': 'HIPAA Safe Harbor compliant anonymization',
        'remove_patient_identifiers': True,
        'remove_dates': True,  # HIPAA requires date removal
        'preserve_geometric_data': True,
        'preserve_dose_data': True,
        'preserve_structure_names': False,  # Remove for HIPAA compliance
        'preserve_physician_info': False,
        'preserve_institution_info': False,
        'preserve_tags': _RT_CRITICAL_TAGS,  # Only critical RT tags
        'remove_free_text': True,
        'age_threshold': 89,  # HIPAA age limit
        'zip_code_truncation': 3,  # Keep only first 3 digits
        'compliance_level': 'HIPAA',
        'strict_mode': True
    }
    
    @classmethod
    def get_profile(cls, profile_name: str) -> Dict[str, Any]:
        """
        Get anonymization profile by name.
        
        Args:
            profile_name: Name of the profile ('basic', 'clinical', 'research', 'hipaa')
            
        Returns:
            Dictionary containing profile configuration
            
        Raises:
            ValueError: If profile name is not recognized
            
        Examples:
            >>> profile = AnonymizationProfiles.get_profile('clinical')
            >>> print(profile['description'])
            Clinical anonymization preserving analysis capability
        """
        profiles = {
            'basic': cls.BASIC_PROFILE,
            'clinical': cls.CLINICAL_PROFILE,
            'research': cls.RESEARCH_PROFILE,
            'hipaa': cls.HIPAA_PROFILE
        }
        
        if profile_name not in profiles:
            available = ', '.join(profiles.keys())
            raise ValueError(f"Unknown profile '{profile_name}'. Available: {available}")
            
        return profiles[profile_name].copy()
    
    @classmethod
    def list_profiles(cls) -> List[Dict[str, str]]:
        """
        List all available anonymization profiles.
        
        Returns:
            List of dictionaries with profile name and description
            
        Examples:
            >>> profiles = AnonymizationProfiles.list_profiles()
            >>> for profile in profiles:
            ...     print(f"{profile['name']}: {profile['description']}")
        """
        return [
            {'name': cls.BASIC_PROFILE['name'], 'description': cls.BASIC_PROFILE['description']},
            {'name': cls.CLINICAL_PROFILE['name'], 'description': cls.CLINICAL_PROFILE['description']},
            {'name': cls.RESEARCH_PROFILE['name'], 'description': cls.RESEARCH_PROFILE['description']},
            {'name': cls.HIPAA_PROFILE['name'], 'description': cls.HIPAA_PROFILE['description']},
        ]
    
    @classmethod
    def validate_profile(cls, profile: Dict[str, Any]) -> bool:
        """
        Validate that a profile contains required fields.
        
        Args:
            profile: Profile dictionary to validate
            
        Returns:
            True if profile is valid
            
        Raises:
            ValueError: If profile is missing required fields
        """
        required_fields = [
            'name', 'description', 'remove_patient_identifiers',
            'preserve_geometric_data', 'preserve_dose_data', 'preserve_tags'
        ]
        
        missing_fields = [field for field in required_fields if field not in profile]
        if missing_fields:
            raise ValueError(f"Profile missing required fields: {missing_fields}")
            
        # Validate that critical RT tags are preserved
        critical_missing = [tag for tag in cls._RT_CRITICAL_TAGS 
                          if tag not in profile['preserve_tags']]
        if critical_missing:
            logger.warning(f"Profile missing critical RT tags: {critical_missing}")
            
        return True
