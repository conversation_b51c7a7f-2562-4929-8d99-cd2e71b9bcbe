# Copyright (C) 2024 Pirate DICOM Contributors

"""
DICOM Compliance Validation for RT Objects.

This module provides comprehensive DICOM standard compliance validation for
RT DICOM objects, ensuring IOD (Information Object Definition) conformance,
mandatory element presence, sequence structure validation, and multi-vendor
TPS compatibility. Implements validation against DICOM Part 3 specifications
for CT, RT Structure Set, RT Dose, and RT Plan IODs.

## DICOM Compliance Categories

### IOD Validation
- **Mandatory Elements**: Validates presence of required DICOM elements
- **Conditional Elements**: Checks conditional element requirements
- **Sequence Structure**: Validates sequence organization and nesting
- **Value Representation**: Ensures proper VR usage and constraints

### Standard Conformance
- **DICOM Part 3**: Information Object Definitions compliance
- **DICOM Part 5**: Data Structure and encoding validation
- **DICOM Part 6**: Data dictionary element validation
- **SOP Class Compliance**: Specific IOD requirements per modality

### TPS Compatibility
- **Multi-vendor Support**: Common TPS compatibility checks
- **Import/Export Validation**: Round-trip compatibility testing
- **Clinical Workflow**: Ensures clinical system compatibility
- **Performance Validation**: File size and complexity limits

## Clinical Context

DICOM compliance validation ensures that created RT objects meet:
- **DICOM Standard**: Full conformance with DICOM Part 3 IOD specifications
- **Regulatory Requirements**: FDA and international medical device standards
- **Clinical Interoperability**: Multi-vendor TPS system compatibility
- **Quality Assurance**: Consistent behavior across clinical systems

The validation framework provides detailed conformance reports with specific
remediation guidance for non-compliant elements, supporting both development
debugging and clinical quality assurance workflows.
"""

from typing import Dict, List, Optional, Any, Union, Set, Tuple
import pydicom
from pydicom import Dataset
from pydicom.sequence import Sequence as DicomSequence
from pydicom.tag import Tag
from pydicom.datadict import tag_for_keyword, keyword_for_tag
import logging
from dataclasses import dataclass
from enum import Enum
import datetime

from ..utils.exceptions import ValidationError, PyrtDicomError
from ..utils.logging import get_clinical_logger
from .clinical import ValidationResult, ValidationLevel


# Configure module logger
logger = get_clinical_logger(__name__)


class IODType(Enum):
    """DICOM IOD types for RT objects."""

    CT_IMAGE = "ct_image"
    RT_STRUCTURE_SET = "rt_structure_set"
    RT_DOSE = "rt_dose"
    RT_PLAN = "rt_plan"


@dataclass
class DicomElement:
    """DICOM element definition for validation."""

    tag: str
    keyword: str
    vr: str
    vm: str
    requirement: str  # M=Mandatory, C=Conditional, U=User Optional
    description: str


class DicomComplianceValidator:
    """
    DICOM standard compliance validator for RT objects.

    Validates DICOM datasets against IOD specifications, ensuring standard
    conformance, mandatory element presence, and TPS compatibility. Supports
    all major RT IODs including CT Image, RT Structure Set, RT Dose, and RT Plan.

    Clinical Notes
    --------------
    This validator ensures DICOM objects meet standard requirements for:

    **Clinical Interoperability**: Multi-vendor TPS compatibility through
    strict DICOM conformance validation and common compatibility checks.

    **Regulatory Compliance**: FDA and international standards compliance
    through comprehensive IOD validation and audit trail generation.

    **Quality Assurance**: Consistent DICOM object creation with detailed
    validation reports for clinical quality management.

    **Development Support**: Detailed conformance reporting with specific
    remediation guidance for efficient debugging and development.

    Parameters
    ----------
    strict_conformance : bool, default True
        Enforce strict DICOM conformance (recommended for clinical use)
    tps_compatibility : bool, default True
        Enable TPS-specific compatibility checks
    validate_sequences : bool, default True
        Perform deep sequence structure validation

    Examples
    --------
    CT Image IOD validation:

    >>> validator = DicomComplianceValidator()
    >>> ct_dataset = create_ct_dataset()  # Your CT dataset
    >>> results = validator.validate_ct_image_iod(ct_dataset)
    >>> error_count = len([r for r in results if r.level == ValidationLevel.ERROR])
    >>> error_count
    0

    RT Structure Set validation:

    >>> results = validator.validate_rt_structure_set_iod(struct_dataset)
    >>> compliance_report = validator.generate_compliance_report(results)
    >>> print(compliance_report)
    ✅ DICOM Compliance: PASSED (0 errors, 2 warnings)
    """

    # CT Image IOD mandatory elements (DICOM Part 3, A.3.1)
    CT_IMAGE_IOD_ELEMENTS = [
        DicomElement(
            "0008,0005",
            "SpecificCharacterSet",
            "CS",
            "1-n",
            "C",
            "Character set encoding",
        ),
        DicomElement(
            "0008,0008", "ImageType", "CS", "2-n", "M", "Image type classification"
        ),
        DicomElement("0008,0016", "SOPClassUID", "UI", "1", "M", "SOP Class UID"),
        DicomElement("0008,0018", "SOPInstanceUID", "UI", "1", "M", "SOP Instance UID"),
        DicomElement("0008,0020", "StudyDate", "DA", "1", "M", "Study date"),
        DicomElement("0008,0030", "StudyTime", "TM", "1", "M", "Study time"),
        DicomElement(
            "0008,0050", "AccessionNumber", "SH", "1", "C", "Study accession number"
        ),
        DicomElement("0008,0060", "Modality", "CS", "1", "M", "Modality (CT)"),
        DicomElement(
            "0008,0070", "Manufacturer", "LO", "1", "C", "Equipment manufacturer"
        ),
        DicomElement(
            "0008,0090", "ReferringPhysicianName", "PN", "1", "C", "Referring physician"
        ),
        DicomElement("0010,0010", "PatientName", "PN", "1", "M", "Patient name"),
        DicomElement("0010,0020", "PatientID", "LO", "1", "M", "Patient ID"),
        DicomElement(
            "0010,0030", "PatientBirthDate", "DA", "1", "C", "Patient birth date"
        ),
        DicomElement("0010,0040", "PatientSex", "CS", "1", "C", "Patient sex"),
        DicomElement("0018,0050", "SliceThickness", "DS", "1", "C", "Slice thickness"),
        DicomElement("0018,0060", "KVP", "DS", "1", "C", "Peak kilo voltage"),
        DicomElement("0018,1030", "ProtocolName", "LO", "1", "C", "Protocol name"),
        DicomElement(
            "0020,000D", "StudyInstanceUID", "UI", "1", "M", "Study Instance UID"
        ),
        DicomElement(
            "0020,000E", "SeriesInstanceUID", "UI", "1", "M", "Series Instance UID"
        ),
        DicomElement("0020,0010", "StudyID", "SH", "1", "C", "Study ID"),
        DicomElement("0020,0011", "SeriesNumber", "IS", "1", "C", "Series number"),
        DicomElement("0020,0013", "InstanceNumber", "IS", "1", "C", "Instance number"),
        DicomElement(
            "0020,0032", "ImagePositionPatient", "DS", "3", "M", "Image position"
        ),
        DicomElement(
            "0020,0037", "ImageOrientationPatient", "DS", "6", "M", "Image orientation"
        ),
        DicomElement(
            "0020,0052", "FrameOfReferenceUID", "UI", "1", "M", "Frame of reference UID"
        ),
        DicomElement(
            "0020,1040",
            "PositionReferenceIndicator",
            "LO",
            "1",
            "C",
            "Position reference",
        ),
        DicomElement(
            "0028,0002", "SamplesPerPixel", "US", "1", "M", "Samples per pixel"
        ),
        DicomElement(
            "0028,0004",
            "PhotometricInterpretation",
            "CS",
            "1",
            "M",
            "Photometric interpretation",
        ),
        DicomElement("0028,0010", "Rows", "US", "1", "M", "Number of rows"),
        DicomElement("0028,0011", "Columns", "US", "1", "M", "Number of columns"),
        DicomElement("0028,0030", "PixelSpacing", "DS", "2", "M", "Pixel spacing"),
        DicomElement("0028,0100", "BitsAllocated", "US", "1", "M", "Bits allocated"),
        DicomElement("0028,0101", "BitsStored", "US", "1", "M", "Bits stored"),
        DicomElement("0028,0102", "HighBit", "US", "1", "M", "High bit"),
        DicomElement(
            "0028,0103", "PixelRepresentation", "US", "1", "M", "Pixel representation"
        ),
        DicomElement("0028,1050", "WindowCenter", "DS", "1-n", "C", "Window center"),
        DicomElement("0028,1051", "WindowWidth", "DS", "1-n", "C", "Window width"),
        DicomElement(
            "0028,1052", "RescaleIntercept", "DS", "1", "M", "Rescale intercept"
        ),
        DicomElement("0028,1053", "RescaleSlope", "DS", "1", "M", "Rescale slope"),
        DicomElement("7FE0,0010", "PixelData", "OW", "1", "M", "Pixel data"),
    ]

    # RT Structure Set IOD mandatory elements (DICOM Part 3, A.19.1)
    RT_STRUCTURE_SET_IOD_ELEMENTS = [
        DicomElement("0008,0016", "SOPClassUID", "UI", "1", "M", "SOP Class UID"),
        DicomElement("0008,0018", "SOPInstanceUID", "UI", "1", "M", "SOP Instance UID"),
        DicomElement("0008,0020", "StudyDate", "DA", "1", "M", "Study date"),
        DicomElement("0008,0030", "StudyTime", "TM", "1", "M", "Study time"),
        DicomElement("0008,0060", "Modality", "CS", "1", "M", "Modality (RTSTRUCT)"),
        DicomElement(
            "0008,0070", "Manufacturer", "LO", "1", "C", "Equipment manufacturer"
        ),
        DicomElement("0010,0010", "PatientName", "PN", "1", "M", "Patient name"),
        DicomElement("0010,0020", "PatientID", "LO", "1", "M", "Patient ID"),
        DicomElement(
            "0020,000D", "StudyInstanceUID", "UI", "1", "M", "Study Instance UID"
        ),
        DicomElement(
            "0020,000E", "SeriesInstanceUID", "UI", "1", "M", "Series Instance UID"
        ),
        DicomElement("0020,0010", "StudyID", "SH", "1", "C", "Study ID"),
        DicomElement("0020,0011", "SeriesNumber", "IS", "1", "C", "Series number"),
        DicomElement("0020,0013", "InstanceNumber", "IS", "1", "C", "Instance number"),
        DicomElement(
            "0020,0052", "FrameOfReferenceUID", "UI", "1", "M", "Frame of reference UID"
        ),
        DicomElement(
            "3006,0002", "StructureSetLabel", "SH", "1", "M", "Structure set label"
        ),
        DicomElement(
            "3006,0004", "StructureSetName", "LO", "1", "C", "Structure set name"
        ),
        DicomElement(
            "3006,0008", "StructureSetDate", "DA", "1", "M", "Structure set date"
        ),
        DicomElement(
            "3006,0009", "StructureSetTime", "TM", "1", "M", "Structure set time"
        ),
        DicomElement(
            "3006,0010",
            "ReferencedFrameOfReferenceSequence",
            "SQ",
            "1",
            "M",
            "Referenced frame sequence",
        ),
        DicomElement(
            "3006,0020",
            "StructureSetROISequence",
            "SQ",
            "1",
            "M",
            "Structure set ROI sequence",
        ),
        DicomElement(
            "3006,0039", "ROIContourSequence", "SQ", "1", "C", "ROI contour sequence"
        ),
        DicomElement(
            "3006,0080",
            "RTROIObservationsSequence",
            "SQ",
            "1",
            "C",
            "RT ROI observations sequence",
        ),
    ]

    # RT Dose IOD mandatory elements (DICOM Part 3, A.18.1)
    RT_DOSE_IOD_ELEMENTS = [
        DicomElement("0008,0016", "SOPClassUID", "UI", "1", "M", "SOP Class UID"),
        DicomElement("0008,0018", "SOPInstanceUID", "UI", "1", "M", "SOP Instance UID"),
        DicomElement("0008,0020", "StudyDate", "DA", "1", "M", "Study date"),
        DicomElement("0008,0030", "StudyTime", "TM", "1", "M", "Study time"),
        DicomElement("0008,0060", "Modality", "CS", "1", "M", "Modality (RTDOSE)"),
        DicomElement("0008,0070", "Manufacturer", "LO", "1", "M", "Manufacturer"),
        DicomElement(
            "0008,1030", "StudyDescription", "LO", "1", "C", "Study description"
        ),
        DicomElement(
            "0008,103E", "SeriesDescription", "LO", "1", "C", "Series description"
        ),
        DicomElement("0010,0010", "PatientName", "PN", "1", "M", "Patient name"),
        DicomElement("0010,0020", "PatientID", "LO", "1", "M", "Patient ID"),
        DicomElement(
            "0010,0030", "PatientBirthDate", "DA", "1", "C", "Patient birth date"
        ),
        DicomElement("0010,0040", "PatientSex", "CS", "1", "C", "Patient sex"),
        DicomElement(
            "0018,1020", "SoftwareVersions", "LO", "1-n", "C", "Software versions"
        ),
        DicomElement(
            "0020,000D", "StudyInstanceUID", "UI", "1", "M", "Study instance UID"
        ),
        DicomElement(
            "0020,000E", "SeriesInstanceUID", "UI", "1", "M", "Series instance UID"
        ),
        DicomElement("0020,0010", "StudyID", "SH", "1", "C", "Study ID"),
        DicomElement("0020,0011", "SeriesNumber", "IS", "1", "C", "Series number"),
        DicomElement("0020,0013", "InstanceNumber", "IS", "1", "C", "Instance number"),
        DicomElement(
            "0020,0032",
            "ImagePositionPatient",
            "DS",
            "3",
            "M",
            "Image position patient",
        ),
        DicomElement(
            "0020,0037",
            "ImageOrientationPatient",
            "DS",
            "6",
            "M",
            "Image orientation patient",
        ),
        DicomElement(
            "0020,0052", "FrameOfReferenceUID", "UI", "1", "M", "Frame of reference UID"
        ),
        DicomElement(
            "0020,1040",
            "PositionReferenceIndicator",
            "LO",
            "1",
            "C",
            "Position reference indicator",
        ),
        DicomElement(
            "0028,0002", "SamplesPerPixel", "US", "1", "M", "Samples per pixel"
        ),
        DicomElement(
            "0028,0004",
            "PhotometricInterpretation",
            "CS",
            "1",
            "M",
            "Photometric interpretation",
        ),
        DicomElement("0028,0008", "NumberOfFrames", "IS", "1", "M", "Number of frames"),
        DicomElement("0028,0010", "Rows", "US", "1", "M", "Rows"),
        DicomElement("0028,0011", "Columns", "US", "1", "M", "Columns"),
        DicomElement("0028,0030", "PixelSpacing", "DS", "2", "M", "Pixel spacing"),
        DicomElement("0028,0100", "BitsAllocated", "US", "1", "M", "Bits allocated"),
        DicomElement("0028,0101", "BitsStored", "US", "1", "M", "Bits stored"),
        DicomElement("0028,0102", "HighBit", "US", "1", "M", "High bit"),
        DicomElement(
            "0028,0103", "PixelRepresentation", "US", "1", "M", "Pixel representation"
        ),
        DicomElement("3004,0002", "DoseUnits", "CS", "1", "M", "Dose units"),
        DicomElement("3004,0004", "DoseType", "CS", "1", "M", "Dose type"),
        DicomElement(
            "3004,000A", "DoseSummationType", "CS", "1", "C", "Dose summation type"
        ),
        DicomElement(
            "3004,000E", "DoseGridScaling", "DS", "1", "M", "Dose grid scaling"
        ),
        DicomElement(
            "3004,000C",
            "GridFrameOffsetVector",
            "DS",
            "1-n",
            "M",
            "Grid frame offset vector",
        ),
        DicomElement(
            "0028,0009",
            "FrameIncrementPointer",
            "AT",
            "1-n",
            "M",
            "Frame increment pointer",
        ),
        DicomElement("7FE0,0010", "PixelData", "OW", "1", "M", "Pixel data"),
    ]

    # RT Plan IOD mandatory elements (DICOM Part 3, A.17.1)
    RT_PLAN_IOD_ELEMENTS = [
        DicomElement("0008,0016", "SOPClassUID", "UI", "1", "M", "SOP Class UID"),
        DicomElement("0008,0018", "SOPInstanceUID", "UI", "1", "M", "SOP Instance UID"),
        DicomElement("0008,0020", "StudyDate", "DA", "1", "M", "Study date"),
        DicomElement("0008,0030", "StudyTime", "TM", "1", "M", "Study time"),
        DicomElement("0008,0060", "Modality", "CS", "1", "M", "Modality (RTPLAN)"),
        DicomElement("0008,0070", "Manufacturer", "LO", "1", "C", "Equipment manufacturer"),
        DicomElement("0010,0010", "PatientName", "PN", "1", "M", "Patient name"),
        DicomElement("0010,0020", "PatientID", "LO", "1", "M", "Patient ID"),
        DicomElement("0020,000D", "StudyInstanceUID", "UI", "1", "M", "Study Instance UID"),
        DicomElement("0020,000E", "SeriesInstanceUID", "UI", "1", "M", "Series Instance UID"),
        DicomElement("0020,0010", "StudyID", "SH", "1", "C", "Study ID"),
        DicomElement("0020,0011", "SeriesNumber", "IS", "1", "C", "Series number"),
        DicomElement("0020,0013", "InstanceNumber", "IS", "1", "C", "Instance number"),
        DicomElement("0020,0052", "FrameOfReferenceUID", "UI", "1", "M", "Frame of reference UID"),
        DicomElement("300A,0002", "RTPlanLabel", "SH", "1", "M", "RT Plan label"),
        DicomElement("300A,0003", "RTPlanName", "LO", "1", "C", "RT Plan name"),
        DicomElement("300A,0004", "RTPlanDescription", "ST", "1", "C", "RT Plan description"),
        DicomElement("300A,0006", "RTPlanDate", "DA", "1", "M", "RT Plan date"),
        DicomElement("300A,0007", "RTPlanTime", "TM", "1", "M", "RT Plan time"),
        DicomElement("300A,0009", "TreatmentProtocols", "LO", "1-n", "C", "Treatment protocols"),
        DicomElement("300A,000A", "PlanIntent", "CS", "1", "C", "Treatment plan intent"),
        DicomElement("300A,000B", "TreatmentSites", "LO", "1-n", "C", "Treatment sites"),
        DicomElement("300A,000C", "RTPlanGeometry", "CS", "1", "M", "RT Plan geometry"),
        DicomElement("300A,0010", "DoseReferenceSequence", "SQ", "1", "C", "Dose reference sequence"),
        DicomElement("300A,0070", "FractionGroupSequence", "SQ", "1", "M", "Fraction group sequence"),
        DicomElement("300A,00B0", "BeamSequence", "SQ", "1", "M", "Beam sequence"),
        DicomElement("300C,0002", "ReferencedRTToolSequence", "SQ", "1", "C", "Referenced RT tool sequence"),
        DicomElement("300C,0060", "ReferencedStructureSetSequence", "SQ", "1", "C", "Referenced structure set sequence"),
        DicomElement("300C,0080", "ReferencedDoseSequence", "SQ", "1", "C", "Referenced dose sequence"),
    ]

    # Standard SOP Class UIDs for RT objects
    SOP_CLASS_UIDS = {
        IODType.CT_IMAGE: "1.2.840.10008.5.1.4.1.1.2",
        IODType.RT_STRUCTURE_SET: "1.2.840.10008.5.1.4.1.1.481.3",
        IODType.RT_DOSE: "1.2.840.10008.5.1.4.1.1.481.2",
        IODType.RT_PLAN: "1.2.840.10008.5.1.4.1.1.481.5",
    }

    def __init__(
        self,
        strict_conformance: bool = True,
        tps_compatibility: bool = True,
        validate_sequences: bool = True,
    ):
        """Initialize DICOM compliance validator."""
        self.strict_conformance = strict_conformance
        self.tps_compatibility = tps_compatibility
        self.validate_sequences = validate_sequences

        # Build tag lookup tables
        self._build_element_lookups()

    def _build_element_lookups(self):
        """Build efficient lookup tables for IOD elements."""
        self.ct_elements = {elem.tag: elem for elem in self.CT_IMAGE_IOD_ELEMENTS}
        self.struct_elements = {
            elem.tag: elem for elem in self.RT_STRUCTURE_SET_IOD_ELEMENTS
        }
        self.dose_elements = {elem.tag: elem for elem in self.RT_DOSE_IOD_ELEMENTS}
        self.plan_elements = {elem.tag: elem for elem in self.RT_PLAN_IOD_ELEMENTS}

    def validate_ct_image_iod(self, dataset: Dataset) -> List[ValidationResult]:
        """
        Validate CT Image IOD compliance.

        Parameters
        ----------
        dataset : Dataset
            CT DICOM dataset to validate

        Returns
        -------
        List[ValidationResult]
            Validation results for CT IOD compliance
        """
        results = []

        # SOP Class validation
        results.extend(self._validate_sop_class(dataset, IODType.CT_IMAGE))

        # Mandatory elements validation
        results.extend(self._validate_mandatory_elements(dataset, self.ct_elements))

        # CT-specific validation
        results.extend(self._validate_ct_specific_elements(dataset))

        # Pixel data validation
        results.extend(self._validate_pixel_data(dataset))

        # TPS compatibility
        if self.tps_compatibility:
            results.extend(self._validate_ct_tps_compatibility(dataset))

        logger.debug(f"CT IOD validation: {len(results)} issues found")
        return results

    def validate_rt_structure_set_iod(self, dataset: Dataset) -> List[ValidationResult]:
        """
        Validate RT Structure Set IOD compliance.

        Parameters
        ----------
        dataset : Dataset
            RT Structure Set DICOM dataset to validate

        Returns
        -------
        List[ValidationResult]
            Validation results for RT Structure Set IOD compliance
        """
        results = []

        # SOP Class validation
        results.extend(self._validate_sop_class(dataset, IODType.RT_STRUCTURE_SET))

        # Mandatory elements validation
        results.extend(self._validate_mandatory_elements(dataset, self.struct_elements))

        # RT Structure Set specific validation
        results.extend(self._validate_structure_set_sequences(dataset))

        # TPS compatibility
        if self.tps_compatibility:
            results.extend(self._validate_struct_tps_compatibility(dataset))

        logger.debug(f"RT Structure Set IOD validation: {len(results)} issues found")
        return results

    def validate_rt_dose_iod(self, dataset: Dataset) -> List[ValidationResult]:
        """
        Validate RT Dose IOD compliance.

        Validates RT Dose DICOM dataset against IOD specifications including
        mandatory elements, dose-specific modules, multi-frame structure,
        and TPS compatibility requirements.

        Parameters
        ----------
        dataset : Dataset
            RT Dose DICOM dataset to validate

        Returns
        -------
        List[ValidationResult]
            Validation results for RT Dose IOD compliance

        Clinical Notes
        -------------
        RT Dose IOD validation ensures:
        - Proper dose grid scaling and units
        - Multi-frame pixel data structure compliance
        - Frame of reference consistency
        - TPS compatibility for dose analysis tools

        Examples
        --------
        >>> validator = DicomComplianceValidator()
        >>> results = validator.validate_rt_dose_iod(dose_dataset)
        >>> errors = [r for r in results if r.level == ValidationLevel.ERROR]
        >>> len(errors)
        0
        """
        results = []

        # SOP Class validation
        results.extend(self._validate_sop_class(dataset, IODType.RT_DOSE))

        # Mandatory elements validation
        results.extend(self._validate_mandatory_elements(dataset, self.dose_elements))

        # RT Dose specific validation
        results.extend(self._validate_dose_modules(dataset))

        # Multi-frame structure validation
        results.extend(self._validate_dose_multiframe_structure(dataset))

        # TPS compatibility
        if self.tps_compatibility:
            results.extend(self._validate_dose_tps_compatibility(dataset))

        return results

    def validate_rt_plan_iod(self, dataset: Dataset) -> List[ValidationResult]:
        """
        Validate RT Plan IOD compliance.

        Validates RT Plan DICOM dataset against IOD specifications including
        mandatory elements, plan-specific modules, beam sequences, fraction
        group sequences, and treatment planning system compatibility.

        Parameters
        ----------
        dataset : Dataset
            RT Plan DICOM dataset to validate

        Returns
        -------
        List[ValidationResult]
            List of validation results including any issues found

        Clinical Notes
        -------------
        RT Plan validation ensures treatment planning safety through:
        - Complete beam configuration validation
        - Prescription dose and fractionation verification
        - Geometric parameter consistency checks
        - TPS compatibility validation for major vendors

        Examples
        --------
        >>> validator = DicomComplianceValidator()
        >>> results = validator.validate_rt_plan_iod(plan_dataset)
        >>> errors = [r for r in results if r.level == ValidationLevel.ERROR]
        """
        results = []

        # SOP Class validation
        results.extend(self._validate_sop_class(dataset, IODType.RT_PLAN))

        # Mandatory elements validation
        results.extend(self._validate_mandatory_elements(dataset, self.plan_elements))

        # RT Plan specific validation
        results.extend(self._validate_plan_modules(dataset))

        # Beam sequence validation
        results.extend(self._validate_beam_sequences(dataset))

        # Fraction group sequence validation
        results.extend(self._validate_fraction_group_sequences(dataset))

        # TPS compatibility
        if self.tps_compatibility:
            results.extend(self._validate_plan_tps_compatibility(dataset))

        return results

    def _validate_sop_class(
        self, dataset: Dataset, iod_type: IODType
    ) -> List[ValidationResult]:
        """Validate SOP Class UID for IOD type."""
        results = []

        if "SOPClassUID" not in dataset:
            results.append(
                ValidationResult(
                    level=ValidationLevel.ERROR,
                    message="Missing mandatory SOPClassUID element",
                    parameter="SOPClassUID",
                    value=None,
                    suggestion="Add SOP Class UID for IOD type",
                )
            )
            return results

        expected_uid = self.SOP_CLASS_UIDS[iod_type]
        actual_uid = str(dataset.SOPClassUID)

        if actual_uid != expected_uid:
            results.append(
                ValidationResult(
                    level=ValidationLevel.ERROR,
                    message=f"Incorrect SOP Class UID for {iod_type.value}",
                    parameter="SOPClassUID",
                    value=actual_uid,
                    expected_range=(expected_uid, expected_uid),
                    suggestion=f"Use correct SOP Class UID: {expected_uid}",
                )
            )

        return results

    def _validate_mandatory_elements(
        self, dataset: Dataset, elements: Dict[str, DicomElement]
    ) -> List[ValidationResult]:
        """Validate presence of mandatory IOD elements."""
        results = []

        for tag_str, element in elements.items():
            if element.requirement == "M":  # Mandatory
                # Convert string format "0008,0020" to "0x00080020" format
                tag = Tag(f"0x{tag_str.replace(',', '')}")

                if tag not in dataset:
                    results.append(
                        ValidationResult(
                            level=ValidationLevel.ERROR,
                            message=f"Missing mandatory element {element.keyword}",
                            parameter=element.keyword,
                            value=None,
                            suggestion=f"Add mandatory element {element.description}",
                        )
                    )
                else:
                    # Validate VR and VM if present
                    results.extend(
                        self._validate_element_constraints(dataset, tag, element)
                    )

        return results

    def _validate_element_constraints(
        self, dataset: Dataset, tag: Tag, element: DicomElement
    ) -> List[ValidationResult]:
        """Validate VR and VM constraints for DICOM element."""
        results = []

        if tag not in dataset:
            return results

        data_element = dataset[tag]

        # VR validation
        if hasattr(data_element, "VR") and data_element.VR != element.vr:
            # Some flexibility for common VR variations
            if not self._is_acceptable_vr_variation(data_element.VR, element.vr):
                level = (
                    ValidationLevel.WARNING
                    if not self.strict_conformance
                    else ValidationLevel.ERROR
                )
                results.append(
                    ValidationResult(
                        level=level,
                        message=f"Incorrect VR for {element.keyword}: got {data_element.VR}, expected {element.vr}",
                        parameter=element.keyword,
                        value=data_element.VR,
                        suggestion=f"Use correct VR: {element.vr}",
                    )
                )

        # VM validation for multi-valued elements
        if element.vm != "1" and hasattr(data_element, "value"):
            vm_valid = self._validate_value_multiplicity(data_element.value, element.vm)
            if not vm_valid:
                results.append(
                    ValidationResult(
                        level=ValidationLevel.WARNING,
                        message=f"Value multiplicity issue for {element.keyword}",
                        parameter=element.keyword,
                        value=str(data_element.value),
                        suggestion=f"Check value multiplicity requirements: {element.vm}",
                    )
                )

        return results

    def _validate_ct_specific_elements(
        self, dataset: Dataset
    ) -> List[ValidationResult]:
        """Validate CT-specific element requirements."""
        results = []

        # Image type validation
        if "ImageType" in dataset:
            image_type = dataset.ImageType
            # Convert to list if needed (pydicom may return different types)
            if hasattr(image_type, "__iter__") and not isinstance(image_type, str):
                image_type_list = list(image_type)
            else:
                image_type_list = [image_type] if image_type else []

            if len(image_type_list) < 2:
                results.append(
                    ValidationResult(
                        level=ValidationLevel.ERROR,
                        message="ImageType must be multi-valued with at least 2 components",
                        parameter="ImageType",
                        value=str(image_type),
                        suggestion="Use format: ['ORIGINAL', 'PRIMARY', 'AXIAL']",
                    )
                )
            elif image_type[0] not in ["ORIGINAL", "DERIVED"]:
                results.append(
                    ValidationResult(
                        level=ValidationLevel.WARNING,
                        message=f"Unusual ImageType first value: {image_type[0]}",
                        parameter="ImageType",
                        value=image_type[0],
                        suggestion="First value should be 'ORIGINAL' or 'DERIVED'",
                    )
                )

        # Modality validation
        if "Modality" in dataset and dataset.Modality != "CT":
            results.append(
                ValidationResult(
                    level=ValidationLevel.ERROR,
                    message=f"Incorrect modality for CT image: {dataset.Modality}",
                    parameter="Modality",
                    value=dataset.Modality,
                    suggestion="Set Modality to 'CT'",
                )
            )

        # Pixel spacing validation
        if "PixelSpacing" in dataset:
            pixel_spacing = dataset.PixelSpacing
            # Handle both single values and lists/MultiValue
            if hasattr(pixel_spacing, "__len__"):
                if len(pixel_spacing) != 2:
                    results.append(
                        ValidationResult(
                            level=ValidationLevel.ERROR,
                            message="PixelSpacing must have exactly 2 values",
                            parameter="PixelSpacing",
                            value=str(pixel_spacing),
                            suggestion="Provide [row_spacing, column_spacing] in mm",
                        )
                    )
            else:
                # Single value, should be 2 values
                results.append(
                    ValidationResult(
                        level=ValidationLevel.ERROR,
                        message="PixelSpacing must have exactly 2 values",
                        parameter="PixelSpacing",
                        value=str(pixel_spacing),
                        suggestion="Provide [row_spacing, column_spacing] in mm",
                    )
                )

        return results

    def _validate_pixel_data(self, dataset: Dataset) -> List[ValidationResult]:
        """Validate pixel data consistency."""
        results = []

        required_tags = ["Rows", "Columns", "BitsAllocated", "BitsStored", "PixelData"]
        missing_tags = [tag for tag in required_tags if tag not in dataset]

        if missing_tags:
            results.append(
                ValidationResult(
                    level=ValidationLevel.ERROR,
                    message=f"Missing pixel data elements: {missing_tags}",
                    parameter="PixelData",
                    value=None,
                    suggestion="Add all required pixel data elements",
                )
            )
            return results

        # Pixel data size validation
        rows = dataset.Rows
        columns = dataset.Columns
        bits_allocated = dataset.BitsAllocated
        samples_per_pixel = getattr(dataset, "SamplesPerPixel", 1)

        expected_size = rows * columns * (bits_allocated // 8) * samples_per_pixel

        if hasattr(dataset, "PixelData") and dataset.PixelData:
            actual_size = len(dataset.PixelData)
            if actual_size != expected_size:
                results.append(
                    ValidationResult(
                        level=ValidationLevel.WARNING,
                        message=f"Pixel data size mismatch: got {actual_size}, expected {expected_size}",
                        parameter="PixelData",
                        value=actual_size,
                        suggestion="Verify pixel data dimensions and bit allocation",
                    )
                )

        return results

    def _validate_structure_set_sequences(
        self, dataset: Dataset
    ) -> List[ValidationResult]:
        """Validate RT Structure Set sequence requirements."""
        results = []

        # Structure Set ROI Sequence validation
        if "StructureSetROISequence" not in dataset:
            results.append(
                ValidationResult(
                    level=ValidationLevel.ERROR,
                    message="Missing mandatory StructureSetROISequence",
                    parameter="StructureSetROISequence",
                    value=None,
                    suggestion="Add Structure Set ROI Sequence with structure definitions",
                )
            )
        else:
            roi_sequence = dataset.StructureSetROISequence
            if not roi_sequence:
                results.append(
                    ValidationResult(
                        level=ValidationLevel.WARNING,
                        message="Empty StructureSetROISequence",
                        parameter="StructureSetROISequence",
                        value=len(roi_sequence),
                        suggestion="Add at least one structure definition",
                    )
                )
            else:
                # Validate individual ROI items
                for i, roi_item in enumerate(roi_sequence):
                    results.extend(self._validate_roi_sequence_item(roi_item, i))

        # ROI Contour Sequence validation
        if "ROIContourSequence" in dataset:
            contour_sequence = dataset.ROIContourSequence
            for i, contour_item in enumerate(contour_sequence):
                results.extend(self._validate_contour_sequence_item(contour_item, i))

        return results

    def _validate_roi_sequence_item(
        self, roi_item: Dataset, index: int
    ) -> List[ValidationResult]:
        """Validate individual ROI sequence item."""
        results = []

        required_elements = ["ROINumber", "ROIName", "ROIGenerationAlgorithm"]
        for element in required_elements:
            if element not in roi_item:
                results.append(
                    ValidationResult(
                        level=ValidationLevel.ERROR,
                        message=f"Missing {element} in ROI item {index}",
                        parameter=element,
                        value=None,
                        suggestion=f"Add {element} to ROI sequence item",
                    )
                )

        # ROI Number validation
        if "ROINumber" in roi_item:
            roi_number = roi_item.ROINumber
            if not isinstance(roi_number, int) or roi_number <= 0:
                results.append(
                    ValidationResult(
                        level=ValidationLevel.ERROR,
                        message=f"Invalid ROI Number: {roi_number}",
                        parameter="ROINumber",
                        value=roi_number,
                        suggestion="ROI Number must be positive integer",
                    )
                )

        return results

    def _validate_contour_sequence_item(
        self, contour_item: Dataset, index: int
    ) -> List[ValidationResult]:
        """Validate individual contour sequence item."""
        results = []

        if "ContourSequence" in contour_item:
            contour_seq = contour_item.ContourSequence
            for j, contour in enumerate(contour_seq):
                if "ContourData" not in contour:
                    results.append(
                        ValidationResult(
                            level=ValidationLevel.ERROR,
                            message=f"Missing ContourData in contour {j} of ROI {index}",
                            parameter="ContourData",
                            value=None,
                            suggestion="Add contour coordinate data",
                        )
                    )
                elif "NumberOfContourPoints" in contour:
                    # Validate consistency between NumberOfContourPoints and ContourData
                    num_points = contour.NumberOfContourPoints
                    contour_data = contour.ContourData
                    expected_data_length = num_points * 3  # 3 coordinates per point

                    if len(contour_data) != expected_data_length:
                        results.append(
                            ValidationResult(
                                level=ValidationLevel.ERROR,
                                message=f"ContourData length mismatch in ROI {index}, contour {j}",
                                parameter="ContourData",
                                value=len(contour_data),
                                expected_range=(
                                    expected_data_length,
                                    expected_data_length,
                                ),
                                suggestion=f"ContourData should have {expected_data_length} values for {num_points} points",
                            )
                        )

        return results

    def _validate_ct_tps_compatibility(
        self, dataset: Dataset
    ) -> List[ValidationResult]:
        """Validate CT dataset for TPS compatibility."""
        results = []

        # Common TPS requirements
        if "SliceThickness" not in dataset:
            results.append(
                ValidationResult(
                    level=ValidationLevel.WARNING,
                    message="SliceThickness missing - required by most TPS systems",
                    parameter="SliceThickness",
                    value=None,
                    suggestion="Add SliceThickness for TPS compatibility",
                )
            )

        if "StudyDescription" not in dataset:
            results.append(
                ValidationResult(
                    level=ValidationLevel.INFO,
                    message="StudyDescription missing - recommended for clinical workflow",
                    parameter="StudyDescription",
                    value=None,
                    suggestion="Add StudyDescription for clinical identification",
                )
            )

        # Pixel spacing reasonableness
        if "PixelSpacing" in dataset:
            pixel_spacing = dataset.PixelSpacing
            # Handle both single values and lists/MultiValue
            if hasattr(pixel_spacing, "__len__") and hasattr(pixel_spacing, "__iter__"):
                if any(float(spacing) > 5.0 for spacing in pixel_spacing):
                    results.append(
                        ValidationResult(
                            level=ValidationLevel.WARNING,
                            message="Large pixel spacing may cause TPS issues",
                            parameter="PixelSpacing",
                            value=pixel_spacing,
                            suggestion="Consider using finer resolution for treatment planning",
                        )
                    )
            else:
                # Single value
                if float(pixel_spacing) > 5.0:
                    results.append(
                        ValidationResult(
                            level=ValidationLevel.WARNING,
                            message="Large pixel spacing may cause TPS issues",
                            parameter="PixelSpacing",
                            value=pixel_spacing,
                            suggestion="Consider using finer resolution for treatment planning",
                        )
                    )

        return results

    def _validate_struct_tps_compatibility(
        self, dataset: Dataset
    ) -> List[ValidationResult]:
        """Validate RT Structure Set for TPS compatibility."""
        results = []

        # Structure naming for TPS systems
        if "StructureSetROISequence" in dataset:
            roi_names = []
            for roi_item in dataset.StructureSetROISequence:
                if "ROIName" in roi_item:
                    roi_name = roi_item.ROIName
                    # Convert MultiValue to string if needed
                    roi_name_str = (
                        str(roi_name)
                        if hasattr(roi_name, "__iter__")
                        and not isinstance(roi_name, str)
                        else roi_name
                    )
                    roi_names.append(roi_name_str)

                    # Check for problematic characters
                    if any(
                        char in roi_name_str
                        for char in ["/", "\\", ":", "*", "?", '"', "<", ">", "|"]
                    ):
                        results.append(
                            ValidationResult(
                                level=ValidationLevel.WARNING,
                                message=f"ROI name '{roi_name_str}' contains characters that may cause TPS issues",
                                parameter="ROIName",
                                value=roi_name_str,
                                suggestion="Use alphanumeric characters, spaces, and underscores only",
                            )
                        )

            # Check for duplicate names
            duplicate_names = [
                name for name in set(roi_names) if roi_names.count(name) > 1
            ]
            if duplicate_names:
                results.append(
                    ValidationResult(
                        level=ValidationLevel.ERROR,
                        message=f"Duplicate ROI names found: {duplicate_names}",
                        parameter="ROIName",
                        value=duplicate_names,
                        suggestion="Ensure all ROI names are unique",
                    )
                )

        return results

    def _is_acceptable_vr_variation(self, actual_vr: str, expected_vr: str) -> bool:
        """Check if VR variation is acceptable."""
        # Common acceptable VR variations
        acceptable_variations = {
            ("OB", "OW"),  # Binary data
            ("DS", "IS"),  # Numeric strings
            ("CS", "LO"),  # String types
            ("OB or OW", "OW"),  # Pydicom's representation for binary data
            ("OW or OB", "OW"),  # Alternative representation
        }

        return (actual_vr, expected_vr) in acceptable_variations or (
            expected_vr,
            actual_vr,
        ) in acceptable_variations

    def _validate_value_multiplicity(self, value: Any, vm: str) -> bool:
        """Validate value multiplicity constraints."""
        if vm == "1":
            return True  # Single value always valid

        if not isinstance(value, (list, tuple)):
            return vm in ["1", "1-n"]  # Single value for multi-valued element

        value_count = len(value)

        if vm == "1-n":
            return value_count >= 1
        elif vm.isdigit():
            return value_count == int(vm)
        elif "-" in vm:
            parts = vm.split("-")
            if len(parts) == 2:
                min_val = int(parts[0]) if parts[0].isdigit() else 1
                max_val = (
                    int(parts[1])
                    if parts[1].isdigit() and parts[1] != "n"
                    else float("inf")
                )
                return min_val <= value_count <= max_val

        return True  # Default to valid for complex VM patterns

    def _validate_dose_modules(self, dataset: Dataset) -> List[ValidationResult]:
        """Validate RT Dose specific modules."""
        results = []

        # Validate dose units
        if "DoseUnits" in dataset:
            dose_units = dataset.DoseUnits
            valid_units = ["GY", "RELATIVE"]
            if dose_units not in valid_units:
                results.append(
                    ValidationResult(
                        level=ValidationLevel.ERROR,
                        message=f"Invalid DoseUnits: {dose_units}",
                        parameter="DoseUnits",
                        value=dose_units,
                        expected_range=valid_units,
                        suggestion="Use 'GY' for absolute dose or 'RELATIVE' for percentage",
                    )
                )

        # Validate dose type
        if "DoseType" in dataset:
            dose_type = dataset.DoseType
            valid_types = ["PHYSICAL", "EFFECTIVE", "ERROR"]
            if dose_type not in valid_types:
                results.append(
                    ValidationResult(
                        level=ValidationLevel.ERROR,
                        message=f"Invalid DoseType: {dose_type}",
                        parameter="DoseType",
                        value=dose_type,
                        expected_range=valid_types,
                        suggestion="Use 'PHYSICAL' for standard dose, 'EFFECTIVE' for biological dose, or 'ERROR' for uncertainty",
                    )
                )

        # Validate summation type
        if "DoseSummationType" in dataset:
            summation_type = dataset.DoseSummationType
            valid_summations = ["PLAN", "BEAM", "BRACHY", "CONTROL_POINT"]
            if summation_type not in valid_summations:
                results.append(
                    ValidationResult(
                        level=ValidationLevel.ERROR,
                        message=f"Invalid DoseSummationType: {summation_type}",
                        parameter="DoseSummationType",
                        value=summation_type,
                        expected_range=valid_summations,
                        suggestion="Use 'PLAN' for total plan dose, 'BEAM' for individual beam, 'BRACHY' for brachytherapy, or 'CONTROL_POINT' for control point dose",
                    )
                )

        # Validate dose grid scaling
        if "DoseGridScaling" in dataset:
            dose_scaling = dataset.DoseGridScaling
            if dose_scaling <= 0:
                results.append(
                    ValidationResult(
                        level=ValidationLevel.ERROR,
                        message=f"Invalid DoseGridScaling: {dose_scaling}",
                        parameter="DoseGridScaling",
                        value=dose_scaling,
                        suggestion="DoseGridScaling must be positive",
                    )
                )

        return results

    def _validate_dose_multiframe_structure(
        self, dataset: Dataset
    ) -> List[ValidationResult]:
        """Validate multi-frame structure for RT Dose."""
        results = []

        # Check NumberOfFrames consistency
        if "NumberOfFrames" in dataset and "GridFrameOffsetVector" in dataset:
            num_frames = int(dataset.NumberOfFrames)
            num_offsets = len(dataset.GridFrameOffsetVector)

            if num_frames != num_offsets:
                results.append(
                    ValidationResult(
                        level=ValidationLevel.ERROR,
                        message=f"NumberOfFrames ({num_frames}) does not match GridFrameOffsetVector length ({num_offsets})",
                        parameter="NumberOfFrames",
                        value=num_frames,
                        expected_range=[num_offsets],
                        suggestion="Ensure NumberOfFrames equals the number of dose planes",
                    )
                )

        # Validate FrameIncrementPointer
        if "FrameIncrementPointer" in dataset:
            frame_increment_pointer = dataset.FrameIncrementPointer
            expected_pointer = 0x3004000C  # Points to GridFrameOffsetVector
            if frame_increment_pointer != expected_pointer:
                results.append(
                    ValidationResult(
                        level=ValidationLevel.WARNING,
                        message=f"FrameIncrementPointer ({hex(frame_increment_pointer)}) should point to GridFrameOffsetVector ({hex(expected_pointer)})",
                        parameter="FrameIncrementPointer",
                        value=hex(frame_increment_pointer),
                        expected_range=[hex(expected_pointer)],
                        suggestion="Set FrameIncrementPointer to 0x3004000C",
                    )
                )

        return results

    def _validate_dose_tps_compatibility(
        self, dataset: Dataset
    ) -> List[ValidationResult]:
        """Validate RT Dose for TPS compatibility."""
        results = []

        # Check pixel spacing for reasonable dose grid resolution
        if "PixelSpacing" in dataset:
            pixel_spacing = dataset.PixelSpacing
            if len(pixel_spacing) >= 2:
                row_spacing, col_spacing = pixel_spacing[0], pixel_spacing[1]

                # Warn about very coarse resolution
                if row_spacing > 5.0 or col_spacing > 5.0:
                    results.append(
                        ValidationResult(
                            level=ValidationLevel.WARNING,
                            message=f"Coarse pixel spacing ({row_spacing:.1f}, {col_spacing:.1f}) mm may affect dose accuracy",
                            parameter="PixelSpacing",
                            value=pixel_spacing,
                            suggestion="Consider using finer resolution (≤2.5mm) for accurate dose analysis",
                        )
                    )

                # Warn about very fine resolution (performance impact)
                if row_spacing < 0.5 or col_spacing < 0.5:
                    results.append(
                        ValidationResult(
                            level=ValidationLevel.INFO,
                            message=f"Very fine pixel spacing ({row_spacing:.1f}, {col_spacing:.1f}) mm may impact TPS performance",
                            parameter="PixelSpacing",
                            value=pixel_spacing,
                            suggestion="Verify TPS can handle high-resolution dose grids efficiently",
                        )
                    )

        # Check for reasonable dose grid size
        if "Rows" in dataset and "Columns" in dataset and "NumberOfFrames" in dataset:
            rows = int(dataset.Rows)
            cols = int(dataset.Columns)
            frames = int(dataset.NumberOfFrames)
            total_voxels = rows * cols * frames

            # Warn about very large dose grids
            if total_voxels > 512**3:
                results.append(
                    ValidationResult(
                        level=ValidationLevel.WARNING,
                        message=f"Large dose grid ({rows}×{cols}×{frames} = {total_voxels:,} voxels) may cause TPS performance issues",
                        parameter="DoseGridSize",
                        value=f"{rows}×{cols}×{frames}",
                        suggestion="Consider reducing dose grid resolution for better TPS compatibility",
                    )
                )

        return results

    def generate_compliance_report(self, results: List[ValidationResult]) -> str:
        """
        Generate comprehensive DICOM compliance report.

        Parameters
        ----------
        results : List[ValidationResult]
            Validation results to include in report

        Returns
        -------
        str
            Formatted DICOM compliance report
        """
        if not results:
            return "✅ DICOM Compliance: PASSED (0 errors, 0 warnings)"

        # Count by severity
        error_count = len([r for r in results if r.level == ValidationLevel.ERROR])
        warning_count = len([r for r in results if r.level == ValidationLevel.WARNING])
        info_count = len([r for r in results if r.level == ValidationLevel.INFO])

        # Overall status
        status = "FAILED" if error_count > 0 else "PASSED"
        status_icon = "❌" if error_count > 0 else "✅"

        report_lines = [
            f"{status_icon} DICOM Compliance: {status} ({error_count} errors, {warning_count} warnings)",
            "=" * 60,
        ]

        if error_count > 0:
            report_lines.append(f"🚨 ERRORS ({error_count}):")
            for result in results:
                if result.level == ValidationLevel.ERROR:
                    report_lines.append(f"  • {result.message}")
                    if result.suggestion:
                        report_lines.append(f"    → {result.suggestion}")

        if warning_count > 0:
            report_lines.append(f"⚠️  WARNINGS ({warning_count}):")
            for result in results:
                if result.level == ValidationLevel.WARNING:
                    report_lines.append(f"  • {result.message}")
                    if result.suggestion:
                        report_lines.append(f"    → {result.suggestion}")

        if info_count > 0:
            report_lines.append(f"ℹ️  INFO ({info_count}):")
            for result in results:
                if result.level == ValidationLevel.INFO:
                    report_lines.append(f"  • {result.message}")

        return "\n".join(report_lines)

    def _validate_plan_modules(self, dataset: Dataset) -> List[ValidationResult]:
        """Validate RT Plan specific modules."""
        results = []

        # Validate plan intent values
        if "PlanIntent" in dataset:
            plan_intent = dataset.PlanIntent
            valid_intents = ["CURATIVE", "PALLIATIVE", "VERIFICATION", "MACHINE_QA", "RESEARCH"]
            if plan_intent not in valid_intents:
                results.append(
                    ValidationResult(
                        level=ValidationLevel.WARNING,
                        message=f"Unusual plan intent: {plan_intent}",
                        parameter="PlanIntent",
                        value=plan_intent,
                        expected_range=valid_intents,
                        suggestion="Use standard plan intent values for TPS compatibility",
                    )
                )

        # Validate RT Plan geometry
        if "RTPlanGeometry" in dataset:
            plan_geometry = dataset.RTPlanGeometry
            valid_geometries = ["PATIENT", "TREATMENT_DEVICE"]
            if plan_geometry not in valid_geometries:
                results.append(
                    ValidationResult(
                        level=ValidationLevel.ERROR,
                        message=f"Invalid RT Plan geometry: {plan_geometry}",
                        parameter="RTPlanGeometry",
                        value=plan_geometry,
                        expected_range=valid_geometries,
                        suggestion="Use 'PATIENT' for patient-based geometry or 'TREATMENT_DEVICE' for device-based",
                    )
                )

        # Validate RT Plan label length (DICOM VR=SH max 16 chars)
        if "RTPlanLabel" in dataset:
            plan_label = dataset.RTPlanLabel
            if len(plan_label) > 16:
                results.append(
                    ValidationResult(
                        level=ValidationLevel.ERROR,
                        message=f"RT Plan label too long: {len(plan_label)} characters (max 16)",
                        parameter="RTPlanLabel",
                        value=plan_label,
                        suggestion="Shorten plan label to 16 characters or less",
                    )
                )

        return results

    def _validate_beam_sequences(self, dataset: Dataset) -> List[ValidationResult]:
        """Validate beam sequences in RT Plan."""
        results = []

        if "BeamSequence" not in dataset:
            results.append(
                ValidationResult(
                    level=ValidationLevel.ERROR,
                    message="Missing mandatory BeamSequence",
                    parameter="BeamSequence",
                    value=None,
                    suggestion="Add BeamSequence with at least one beam definition",
                )
            )
            return results

        beam_sequence = dataset.BeamSequence
        if len(beam_sequence) == 0:
            results.append(
                ValidationResult(
                    level=ValidationLevel.ERROR,
                    message="Empty BeamSequence",
                    parameter="BeamSequence",
                    value=len(beam_sequence),
                    suggestion="Add at least one beam to BeamSequence",
                )
            )
            return results

        # Validate individual beam items
        for i, beam_item in enumerate(beam_sequence):
            results.extend(self._validate_beam_sequence_item(beam_item, i))

        return results

    def _validate_beam_sequence_item(self, beam_item: Dataset, beam_number: int) -> List[ValidationResult]:
        """Validate individual beam sequence item."""
        results = []

        # Required beam elements
        required_elements = [
            ("BeamNumber", "IS"),
            ("BeamName", "LO"),
            ("BeamType", "CS"),
            ("RadiationType", "CS"),
            ("PrimaryDosimeterUnit", "CS"),
        ]

        for element_name, vr in required_elements:
            if element_name not in beam_item:
                results.append(
                    ValidationResult(
                        level=ValidationLevel.ERROR,
                        message=f"Missing required beam element: {element_name} in beam {beam_number + 1}",
                        parameter=f"BeamSequence[{beam_number}].{element_name}",
                        value=None,
                        suggestion=f"Add {element_name} to beam {beam_number + 1}",
                    )
                )

        # Validate beam type
        if "BeamType" in beam_item:
            beam_type = beam_item.BeamType
            valid_beam_types = ["STATIC", "DYNAMIC", "SETUP"]
            if beam_type not in valid_beam_types:
                results.append(
                    ValidationResult(
                        level=ValidationLevel.WARNING,
                        message=f"Unusual beam type: {beam_type} in beam {beam_number + 1}",
                        parameter=f"BeamSequence[{beam_number}].BeamType",
                        value=beam_type,
                        expected_range=valid_beam_types,
                        suggestion="Use standard beam types for TPS compatibility",
                    )
                )

        # Validate radiation type
        if "RadiationType" in beam_item:
            radiation_type = beam_item.RadiationType
            valid_radiation_types = ["PHOTON", "ELECTRON", "PROTON", "NEUTRON", "ION"]
            if radiation_type not in valid_radiation_types:
                results.append(
                    ValidationResult(
                        level=ValidationLevel.WARNING,
                        message=f"Unusual radiation type: {radiation_type} in beam {beam_number + 1}",
                        parameter=f"BeamSequence[{beam_number}].RadiationType",
                        value=radiation_type,
                        expected_range=valid_radiation_types,
                        suggestion="Use standard radiation types for TPS compatibility",
                    )
                )

        return results

    def _validate_fraction_group_sequences(self, dataset: Dataset) -> List[ValidationResult]:
        """Validate fraction group sequences in RT Plan."""
        results = []

        if "FractionGroupSequence" not in dataset:
            results.append(
                ValidationResult(
                    level=ValidationLevel.ERROR,
                    message="Missing mandatory FractionGroupSequence",
                    parameter="FractionGroupSequence",
                    value=None,
                    suggestion="Add FractionGroupSequence with fraction group definition",
                )
            )
            return results

        fraction_sequence = dataset.FractionGroupSequence
        if len(fraction_sequence) == 0:
            results.append(
                ValidationResult(
                    level=ValidationLevel.ERROR,
                    message="Empty FractionGroupSequence",
                    parameter="FractionGroupSequence",
                    value=len(fraction_sequence),
                    suggestion="Add at least one fraction group to FractionGroupSequence",
                )
            )
            return results

        # Validate individual fraction group items
        for i, fraction_item in enumerate(fraction_sequence):
            results.extend(self._validate_fraction_group_item(fraction_item, i))

        return results

    def _validate_fraction_group_item(self, fraction_item: Dataset, group_number: int) -> List[ValidationResult]:
        """Validate individual fraction group sequence item."""
        results = []

        # Required fraction group elements
        required_elements = [
            ("FractionGroupNumber", "IS"),
            ("NumberOfFractionsPlanned", "IS"),
        ]

        for element_name, vr in required_elements:
            if element_name not in fraction_item:
                results.append(
                    ValidationResult(
                        level=ValidationLevel.ERROR,
                        message=f"Missing required fraction group element: {element_name} in group {group_number + 1}",
                        parameter=f"FractionGroupSequence[{group_number}].{element_name}",
                        value=None,
                        suggestion=f"Add {element_name} to fraction group {group_number + 1}",
                    )
                )

        # Validate number of fractions
        if "NumberOfFractionsPlanned" in fraction_item:
            num_fractions = int(fraction_item.NumberOfFractionsPlanned)
            if num_fractions <= 0:
                results.append(
                    ValidationResult(
                        level=ValidationLevel.ERROR,
                        message=f"Invalid number of fractions: {num_fractions} in group {group_number + 1}",
                        parameter=f"FractionGroupSequence[{group_number}].NumberOfFractionsPlanned",
                        value=num_fractions,
                        suggestion="Number of fractions must be positive",
                    )
                )
            elif num_fractions > 50:
                results.append(
                    ValidationResult(
                        level=ValidationLevel.WARNING,
                        message=f"Unusually high number of fractions: {num_fractions} in group {group_number + 1}",
                        parameter=f"FractionGroupSequence[{group_number}].NumberOfFractionsPlanned",
                        value=num_fractions,
                        suggestion="Verify fraction count is appropriate for treatment protocol",
                    )
                )

        return results

    def _validate_plan_tps_compatibility(self, dataset: Dataset) -> List[ValidationResult]:
        """Validate RT Plan for TPS compatibility."""
        results = []

        # Check for TPS-specific elements that improve compatibility
        recommended_elements = {
            "Manufacturer": "Equipment manufacturer helps TPS identify file format requirements",
            "SoftwareVersions": "Software version information aids in compatibility assessment",
            "ManufacturerModelName": "Equipment model name helps TPS identify specific capabilities",
        }

        for element_name, description in recommended_elements.items():
            if element_name not in dataset:
                results.append(
                    ValidationResult(
                        level=ValidationLevel.INFO,
                        message=f"Recommended element missing: {element_name}",
                        parameter=element_name,
                        value=None,
                        suggestion=description,
                    )
                )

        # Validate beam names are unique and TPS-friendly
        if "BeamSequence" in dataset:
            beam_names = []
            for i, beam_item in enumerate(dataset.BeamSequence):
                if "BeamName" in beam_item:
                    beam_name = beam_item.BeamName
                    beam_names.append(beam_name)
                    
                    # Check for TPS-unfriendly characters
                    if not beam_name.replace("_", "").replace("-", "").isalnum():
                        results.append(
                            ValidationResult(
                                level=ValidationLevel.WARNING,
                                message=f"Beam name contains special characters: '{beam_name}' in beam {i + 1}",
                                parameter=f"BeamSequence[{i}].BeamName",
                                value=beam_name,
                                suggestion="Use alphanumeric characters, underscores, and hyphens only for TPS compatibility",
                            )
                        )

            # Check for duplicate beam names
            if len(beam_names) != len(set(beam_names)):
                duplicate_names = [name for name in set(beam_names) if beam_names.count(name) > 1]
                results.append(
                    ValidationResult(
                        level=ValidationLevel.ERROR,
                        message=f"Duplicate beam names found: {duplicate_names}",
                        parameter="BeamSequence.BeamName",
                        value=duplicate_names,
                        suggestion="Ensure all beam names are unique",
                    )
                )

        return results


def validate_dicom_compliance(
    dataset: Dataset,
    iod_type: IODType,
    strict_conformance: bool = True,
    tps_compatibility: bool = True,
) -> List[ValidationResult]:
    """
    Validate DICOM dataset compliance for specific IOD type.

    Parameters
    ----------
    dataset : Dataset
        DICOM dataset to validate
    iod_type : IODType
        Type of IOD to validate against
    strict_conformance : bool, default True
        Enforce strict DICOM conformance
    tps_compatibility : bool, default True
        Enable TPS compatibility checks

    Returns
    -------
    List[ValidationResult]
        Validation results for DICOM compliance

    Examples
    --------
    >>> results = validate_dicom_compliance(
    ...     dataset=ct_dataset,
    ...     iod_type=IODType.CT_IMAGE
    ... )
    >>> compliance_passed = all(r.level != ValidationLevel.ERROR for r in results)
    >>> compliance_passed
    True
    """
    validator = DicomComplianceValidator(
        strict_conformance=strict_conformance, tps_compatibility=tps_compatibility
    )

    if iod_type == IODType.CT_IMAGE:
        return validator.validate_ct_image_iod(dataset)
    elif iod_type == IODType.RT_STRUCTURE_SET:
        return validator.validate_rt_structure_set_iod(dataset)
    elif iod_type == IODType.RT_DOSE:
        return validator.validate_rt_dose_iod(dataset)
    elif iod_type == IODType.RT_PLAN:
        return validator.validate_rt_plan_iod(dataset)
    else:
        raise ValidationError(f"IOD type {iod_type} not yet implemented")


def generate_conformance_statement(
    results: List[ValidationResult], iod_type: IODType
) -> str:
    """
    Generate DICOM conformance statement from validation results.

    Parameters
    ----------
    results : List[ValidationResult]
        Validation results
    iod_type : IODType
        IOD type that was validated

    Returns
    -------
    str
        DICOM conformance statement
    """
    error_count = len([r for r in results if r.level == ValidationLevel.ERROR])
    warning_count = len([r for r in results if r.level == ValidationLevel.WARNING])

    compliance_level = (
        "FULL"
        if error_count == 0 and warning_count == 0
        else "PARTIAL" if error_count == 0 else "NON-COMPLIANT"
    )

    statement = f"""
DICOM Conformance Statement
===========================
IOD Type: {iod_type.value.upper().replace('_', ' ')}
Compliance Level: {compliance_level}
Validation Date: {datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

Validation Summary:
- Errors: {error_count}
- Warnings: {warning_count}
- Total Issues: {len(results)}

Standard References:
- DICOM Part 3: Information Object Definitions
- DICOM Part 5: Data Structures and Encoding
- DICOM Part 6: Data Dictionary
"""

    if error_count == 0:
        statement += (
            "\n✅ This object meets DICOM standard requirements for clinical use."
        )
    else:
        statement += "\n❌ This object requires corrections before clinical deployment."

    return statement
