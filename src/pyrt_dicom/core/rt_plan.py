"""
RT Plan DICOM Creation Implementation.

This module provides the RTPlan class for creating DICOM RT Plan objects from
beam configuration data, implementing the final component needed to complete
the pyrt-dicom MVP for all four core RT DICOM types (CT, RTSTRUCT, RTDOSE, RTPLAN).

## Clinical Usage

The RTPlan class enables creation of treatment plan DICOM files from beam
configurations, prescription doses, and fractionation schemes, supporting
the complete radiotherapy workflow from planning through delivery:

```python
import pyrt_dicom as prt

# Define beam configuration for 3-field prostate plan
beam_config = {
    'prescription_dose': 7000,  # cGy (70 Gy)
    'fractions': 35,
    'beams': [
        {
            'name': 'AP',
            'energy': 18,
            'gantry_angle': 0,
            'collimator_angle': 0,
            'couch_angle': 0,
            'dose_weight': 0.33,
            'beam_type': 'STATIC',
            'radiation_type': 'PHOTON'
        },
        {
            'name': 'RPO',
            'energy': 18,
            'gantry_angle': 120,
            'collimator_angle': 0,
            'couch_angle': 0,
            'dose_weight': 0.33,
            'beam_type': 'STATIC',
            'radiation_type': 'PHOTON'
        },
        {
            'name': 'LPO',
            'energy': 18,
            'gantry_angle': 240,
            'collimator_angle': 0,
            'couch_angle': 0,
            'dose_weight': 0.34,
            'beam_type': 'STATIC',
            'radiation_type': 'PHOTON'
        }
    ]
}

# Create RT Plan with references to dose and structures
rt_plan = prt.RTPlan.from_beam_config(
    prescription=beam_config,
    reference_dose=rt_dose,        # Link to RT Dose object
    reference_structures=rt_struct, # Link to RT Structure Set
    patient_info=patient_info
)

# Save with automatic validation
rt_plan.save('patient_plan.dcm')
```

## Architecture Integration

The RTPlan class integrates with the complete pyrt-dicom framework:

- **BaseDicomCreator**: Inherits common DICOM creation functionality including
  UID management, validation framework, and file saving capabilities
- **RTPlanTemplate**: Uses the RT Plan IOD template for proper DICOM structure
- **CoordinateTransformer**: Ensures geometric consistency with CT reference
- **Clinical Validation**: Validates beam parameters and prescription doses
- **UID Registry**: Maintains relationships with referenced RT objects

## Treatment Planning System Compatibility

Generated RT Plan files are compatible with major treatment planning systems:

- Varian Eclipse and TrueBeam systems
- Elekta Monaco and Versa HD systems  
- RaySearch RayStation
- Philips Pinnacle³
- Accuray CyberKnife
- BrainLAB iPlan RT
- ViewRay MRIdian

## Cross-References

**Related Classes**:
- :class:`~pyrt_dicom.core.base.BaseDicomCreator` - Base DICOM creator framework
- :class:`~pyrt_dicom.templates.plan_template.RTPlanTemplate` - RT Plan IOD template
- :class:`~pyrt_dicom.core.rt_dose.RTDose` - RT Dose creation for plan references
- :class:`~pyrt_dicom.core.rt_struct.RTStructureSet` - RT Structure Set creation

**See Also**:
- :mod:`~pyrt_dicom.validation.clinical` - Clinical parameter validation
- :mod:`~pyrt_dicom.coordinates.transforms` - Coordinate system consistency
- DICOM Part 3 Section C.8.8.16: RT Plan IOD specification
"""

from pathlib import Path
from typing import Dict, List, Optional, Union, Any
import datetime

import pydicom
from pydicom.dataset import Dataset

from .base import BaseDicomCreator
from ..templates.plan_template import RTPlanTemplate
from ..utils.exceptions import DicomCreationError, ValidationError
from ..validation.clinical import ClinicalValidator
from ..validation.dicom_compliance import DicomComplianceValidator


class RTPlan(BaseDicomCreator):
    """DICOM RT Plan creator for treatment planning workflows.

    Creates RT Plan DICOM files from beam configurations, prescription doses,
    and fractionation schemes. Supports both static (3D-CRT) and dynamic
    (IMRT/VMAT) beam delivery modes with proper geometric parameter validation
    and clinical safety checks.

    Clinical Notes:
        RT Plans serve as the central coordination point for radiotherapy
        treatments, linking beam delivery parameters with dose calculations
        and anatomical structures. Key clinical requirements include:

        - Accurate beam geometry specification for treatment setup
        - Proper prescription dose distribution across beam configurations
        - Fraction scheme organization for dose delivery scheduling
        - Geometric consistency with planning CT and structure sets
        - Machine parameter validation for treatment delivery verification

        The class ensures clinical safety through comprehensive validation
        of beam parameters, dose prescriptions, and geometric relationships
        with referenced RT objects.

    Attributes:
        prescription_dose: Total prescribed dose in cGy
        fractions: Number of treatment fractions
        beam_configurations: List of beam configuration parameters
        plan_name: Descriptive name for the treatment plan
        treatment_intent: Treatment intent classification
        referenced_dose: Referenced RT Dose object for plan evaluation
        referenced_structures: Referenced RT Structure Set for planning
    """

    def __init__(
        self,
        reference_image: Optional[Union[Dataset, str, Path]] = None,
        patient_info: Optional[Dict[str, Union[str, int]]] = None,
        plan_name: str = "RT Plan",
        treatment_intent: str = "CURATIVE",
        **kwargs,
    ):
        """Initialize RT Plan creator.

        Args:
            reference_image: Reference CT image or path for geometric alignment.
                Used to establish Frame of Reference UID and spatial coordinate
                system consistency with planning CT.
            patient_info: Patient demographic and study information dictionary.
                Should include required fields like 'PatientID' and optional
                fields like 'PatientName', 'StudyDescription'.
            plan_name: Descriptive name for the treatment plan. Limited to
                16 characters for DICOM RTPlanLabel field compliance.
            treatment_intent: Treatment intent classification ('CURATIVE',
                'PALLIATIVE', 'VERIFICATION', 'MACHINE_QA').
            **kwargs: Additional DICOM elements to include in the dataset.

        Clinical Notes:
            The reference_image is critical for maintaining geometric consistency
            across the complete treatment plan dataset. All RT objects (structures,
            dose, plan) should reference the same planning CT Frame of Reference
            UID to ensure spatial alignment during treatment delivery.

        Examples:
            Create RT Plan with CT reference:

            >>> rt_plan = RTPlan(
            ...     reference_image=planning_ct,
            ...     patient_info={'PatientID': 'RT001', 'PatientName': 'Doe^John'},
            ...     plan_name="Prostate IMRT",
            ...     treatment_intent="CURATIVE"
            ... )

            Create research plan with minimal information:

            >>> rt_plan = RTPlan(
            ...     patient_info={'PatientID': 'RESEARCH_001'},
            ...     plan_name="Research Plan"
            ... )
        """
        super().__init__(reference_image, patient_info, **kwargs)
        
        self.plan_name = plan_name
        self.treatment_intent = treatment_intent
        
        # Plan-specific attributes (set by from_beam_config)
        self.prescription_dose: Optional[float] = None
        self.fractions: Optional[int] = None
        self.beam_configurations: List[Dict[str, Any]] = []
        
        # Referenced RT objects
        self.referenced_dose: Optional[Union[Dataset, str]] = None
        self.referenced_structures: Optional[Union[Dataset, str]] = None
        
        # Clinical validators
        self.clinical_validator = ClinicalValidator()
        self.dicom_validator = DicomComplianceValidator()

    @classmethod
    def from_beam_config(
        cls,
        prescription: Dict[str, Any],
        reference_dose: Optional[Union[Dataset, str, "RTDose"]] = None,
        reference_structures: Optional[Union[Dataset, str, "RTStructureSet"]] = None,
        reference_image: Optional[Union[Dataset, str, Path]] = None,
        patient_info: Optional[Dict[str, Union[str, int]]] = None,
        **kwargs,
    ) -> "RTPlan":
        """Create RT Plan from beam configuration data.

        This is the primary factory method for creating RT Plan objects from
        treatment planning parameters. Handles beam geometry, prescription
        doses, fractionation schemes, and references to related RT objects.

        Args:
            prescription: Prescription configuration dictionary containing:
                - prescription_dose: Total dose in cGy (required)
                - fractions: Number of fractions (required)
                - beams: List of beam configuration dictionaries (required)
                Each beam configuration should contain:
                - name: Beam identifier (string)
                - energy: Beam energy in MV (photons) or MeV (electrons)
                - gantry_angle: Gantry rotation angle in degrees (0-359.9)
                - dose_weight: Relative beam weight (0.0-1.0, should sum to 1.0)
                - Optional: collimator_angle, couch_angle, beam_type, radiation_type
            reference_dose: Referenced RT Dose object, dataset, or SOP Instance UID
                for linking plan to dose calculations. Can be RTDose object,
                pydicom Dataset, or UID string.
            reference_structures: Referenced RT Structure Set object, dataset, or
                SOP Instance UID for linking plan to anatomical contours.
            reference_image: Planning CT image for geometric consistency.
            patient_info: Patient demographic information.
            **kwargs: Additional plan parameters including plan_name, treatment_intent.

        Returns:
            RTPlan instance configured with the specified beam configuration.

        Raises:
            DicomCreationError: If prescription parameters are invalid, beam
                configurations are inconsistent, or required data is missing.
            ValidationError: If clinical validation fails for beam parameters,
                dose prescriptions, or geometric relationships.

        Clinical Notes:
            This method implements comprehensive clinical validation to ensure
            treatment plan safety and consistency:

            **Prescription Validation**: Checks dose levels against typical
            clinical ranges (180-1000 cGy per fraction) and validates total
            prescription doses for different treatment sites.

            **Beam Configuration**: Validates beam geometry parameters, energy
            specifications, and dose weight distributions. Ensures beam angles
            are physically achievable and dose weights sum to 1.0.

            **Reference Consistency**: Verifies that referenced dose and structure
            objects maintain consistent Frame of Reference UIDs and coordinate
            systems with the planning CT.

        Examples:
            Create 3-field conformal prostate plan:

            >>> prescription_config = {
            ...     'prescription_dose': 7000,  # 70 Gy
            ...     'fractions': 35,
            ...     'beams': [
            ...         {
            ...             'name': 'AP', 'energy': 18, 'gantry_angle': 0,
            ...             'dose_weight': 0.33, 'beam_type': 'STATIC'
            ...         },
            ...         {
            ...             'name': 'RPO', 'energy': 18, 'gantry_angle': 120,
            ...             'dose_weight': 0.33, 'beam_type': 'STATIC'
            ...         },
            ...         {
            ...             'name': 'LPO', 'energy': 18, 'gantry_angle': 240,
            ...             'dose_weight': 0.34, 'beam_type': 'STATIC'
            ...         }
            ...     ]
            ... }
            >>> rt_plan = RTPlan.from_beam_config(
            ...     prescription=prescription_config,
            ...     reference_dose=rt_dose_object,
            ...     reference_structures=rt_struct_object,
            ...     reference_image=planning_ct,
            ...     patient_info={'PatientID': 'RT001'},
            ...     plan_name="Prostate 3D-CRT"
            ... )

            Create SBRT lung plan with setup beam:

            >>> sbrt_config = {
            ...     'prescription_dose': 5000,  # 50 Gy in 5 fractions
            ...     'fractions': 5,
            ...     'beams': [
            ...         {
            ...             'name': 'Setup_kV', 'energy': 6, 'gantry_angle': 0,
            ...             'dose_weight': 0.0, 'beam_type': 'SETUP'
            ...         },
            ...         {
            ...             'name': 'Arc_CW', 'energy': 10, 'gantry_angle': 181,
            ...             'dose_weight': 0.5, 'beam_type': 'DYNAMIC'
            ...         },
            ...         {
            ...             'name': 'Arc_CCW', 'energy': 10, 'gantry_angle': 179,
            ...             'dose_weight': 0.5, 'beam_type': 'DYNAMIC'
            ...         }
            ...     ]
            ... }
            >>> rt_plan = RTPlan.from_beam_config(
            ...     prescription=sbrt_config,
            ...     reference_dose=sbrt_dose,
            ...     treatment_intent="CURATIVE",
            ...     plan_name="Lung SBRT"
            ... )

            Create plan with comprehensive references:

            >>> rt_plan = RTPlan.from_beam_config(
            ...     prescription=prescription_config,
            ...     reference_dose=dose_dataset,
            ...     reference_structures=struct_dataset,
            ...     reference_image="planning_ct.dcm",
            ...     patient_info={
            ...         'PatientID': 'CLINIC_123',
            ...         'PatientName': 'Smith^John',
            ...         'StudyDescription': 'Prostate RT Planning'
            ...     },
            ...     plan_name="Prostate IMRT",
            ...     treatment_intent="CURATIVE"
            ... )
        """
        # Validate prescription configuration
        cls._validate_prescription_config(prescription)
        
        # Extract plan parameters
        plan_name = kwargs.pop('plan_name', 'RT Plan')
        treatment_intent = kwargs.pop('treatment_intent', 'CURATIVE')
        
        # Create RT Plan instance
        rt_plan = cls(
            reference_image=reference_image,
            patient_info=patient_info,
            plan_name=plan_name,
            treatment_intent=treatment_intent,
            **kwargs
        )
        
        # Set prescription parameters
        rt_plan.prescription_dose = prescription['prescription_dose']
        rt_plan.fractions = prescription['fractions']
        rt_plan.beam_configurations = prescription['beams']
        
        # Set referenced objects
        rt_plan.referenced_dose = reference_dose
        rt_plan.referenced_structures = reference_structures
        
        # Validate clinical parameters
        rt_plan._validate_clinical_parameters()
        
        return rt_plan

    @classmethod
    def _validate_prescription_config(cls, prescription: Dict[str, Any]) -> None:
        """Validate prescription configuration dictionary.
        
        Args:
            prescription: Prescription configuration to validate
            
        Raises:
            DicomCreationError: If prescription configuration is invalid
        """
        required_fields = ['prescription_dose', 'fractions', 'beams']
        for field in required_fields:
            if field not in prescription:
                raise DicomCreationError(
                    f"Missing required prescription field: {field}",
                    suggestions=[
                        f"Add '{field}' to prescription configuration dictionary",
                        "Required fields: prescription_dose, fractions, beams",
                        "See documentation for complete prescription format",
                    ],
                    clinical_context={
                        "missing_field": field,
                        "required_fields": required_fields,
                    }
                )
        
        # Validate beams is a non-empty list
        if not isinstance(prescription['beams'], list) or not prescription['beams']:
            raise DicomCreationError(
                "Prescription 'beams' must be a non-empty list",
                suggestions=[
                    "Provide at least one beam configuration",
                    "Each beam should be a dictionary with geometry parameters",
                    "Include setup beams if used for patient positioning",
                ],
            )

    def _validate_clinical_parameters(self) -> None:
        """Validate clinical parameters for safety and consistency.
        
        Raises:
            ValidationError: If clinical validation fails
        """
        errors = []
        warnings = []
        
        # Clinical dose limits per fraction based on number of fractions
        DOSE_LIMITS_PER_FRACTION = {
            1: 2500,   # Single fraction (SRS)
            2: 2000,   # 2-fraction SBRT
            3: 1800,   # 3-fraction SBRT
            4: 1500,   # 4-fraction SBRT
            5: 1200,   # 5-fraction SBRT
            6: 1200,   # 6-fraction SBRT
            7: 1000,   # 7-fraction SBRT
            8: 800,    # 8-fraction SBRT
            9: 800,    # 9-fraction SBRT
            10: 700,   # 10-fraction SBRT
            # No limit for >10 fractions (conventional fractionation)
        }
        
        # Validate prescription dose ranges
        if self.prescription_dose:
            dose_per_fraction = self.prescription_dose / self.fractions
            
            # Check for unusually low dose per fraction
            if dose_per_fraction < 100:  # < 1 Gy per fraction
                errors.append(
                    f"Low dose per fraction: {dose_per_fraction:.1f} cGy. "
                    f"Typical range: 180-2000 cGy per fraction."
                )
            
            # Check against clinical dose limits (warnings only for QA flexibility)
            if self.fractions in DOSE_LIMITS_PER_FRACTION:
                limit = DOSE_LIMITS_PER_FRACTION[self.fractions]
                if dose_per_fraction > limit:
                    warnings.append(
                        f"Dose per fraction ({dose_per_fraction:.1f} cGy) exceeds typical "
                        f"clinical limit for {self.fractions} fractions ({limit} cGy/fx). "
                        f"Consider review for QA or research protocols."
                    )
        
        # Validate beam configuration
        if self.beam_configurations:
            treatment_beams = [
                beam for beam in self.beam_configurations
                if beam.get('beam_type', 'STATIC') != 'SETUP'
            ]
            
            if len(treatment_beams) < 1:
                errors.append("At least one treatment beam is required")
        
        # Log warnings for clinical review (but don't block creation)
        if warnings:
            for warning in warnings:
                self.logger.warning(
                    f"Clinical parameter warning: {warning}",
                    extra={
                        "prescription_dose": self.prescription_dose,
                        "fractions": self.fractions,
                        "dose_per_fraction": self.prescription_dose / self.fractions if self.fractions else None,
                    }
                )
        
        # Only raise errors for serious validation failures
        if errors:
            raise ValidationError(
                "Clinical validation failed:\n" + "\n".join(f"- {error}" for error in errors),
                validation_type="clinical_safety",
                clinical_context={
                    "prescription_dose": self.prescription_dose,
                    "fractions": self.fractions,
                    "num_beams": len(self.beam_configurations),
                    "warnings": warnings,
                }
            )

    def _create_modality_specific_dataset(self) -> Dataset:
        """Create RT Plan specific DICOM dataset.

        Returns:
            Complete DICOM dataset with RT Plan IOD structure

        Raises:
            DicomCreationError: If dataset creation fails
        """
        if not all([self.prescription_dose, self.fractions, self.beam_configurations]):
            raise DicomCreationError(
                "RT Plan not properly configured. Use from_beam_config() to create plan.",
                suggestions=[
                    "Use RTPlan.from_beam_config() class method",
                    "Ensure prescription_dose, fractions, and beam_configurations are set",
                    "Validate beam configuration parameters before dataset creation",
                ]
            )
        
        try:
            # Get referenced object UIDs
            ref_dose_uid = self._get_referenced_object_uid(self.referenced_dose)
            ref_struct_uid = self._get_referenced_object_uid(self.referenced_structures)
            
            # Create dataset using template
            dataset = RTPlanTemplate.create_dataset(
                prescription_dose=self.prescription_dose,
                fractions=self.fractions,
                beam_configurations=self.beam_configurations,
                plan_name=self.plan_name,
                plan_description=f"Treatment plan created with pyrt-dicom",
                treatment_intent=self.treatment_intent,
                referenced_structure_set_sop_instance_uid=ref_struct_uid,
                referenced_dose_sop_instance_uid=ref_dose_uid,
            )
            
            # Set base dataset elements
            base_dataset = self._create_base_dataset()
            
            # Copy base elements to plan dataset
            for element in base_dataset:
                if element.tag not in dataset:
                    dataset[element.tag] = element
            
            return dataset
            
        except Exception as e:
            raise DicomCreationError(
                f"Failed to create RT Plan dataset: {e}",
                suggestions=[
                    "Verify beam configuration parameters are valid",
                    "Check that prescription dose and fractions are positive",
                    "Ensure referenced objects have valid UIDs",
                ],
                clinical_context={
                    "plan_name": self.plan_name,
                    "prescription_dose": self.prescription_dose,
                    "fractions": self.fractions,
                    "num_beams": len(self.beam_configurations) if self.beam_configurations else 0,
                }
            )

    def _get_referenced_object_uid(self, ref_object: Optional[Union[Dataset, str, Any]]) -> Optional[str]:
        """Extract SOP Instance UID from referenced object.
        
        Args:
            ref_object: Referenced object (Dataset, UID string, or RT object)
            
        Returns:
            SOP Instance UID string or None if no reference
        """
        if ref_object is None:
            return None
        
        if isinstance(ref_object, str):
            return ref_object
        
        if isinstance(ref_object, Dataset):
            return getattr(ref_object, 'SOPInstanceUID', None)
        
        # Handle RT objects from this library (RTDose, RTStructureSet)
        if hasattr(ref_object, 'dataset') and ref_object.dataset:
            return getattr(ref_object.dataset, 'SOPInstanceUID', None)
        
        return None

    def _validate_modality_specific(self) -> None:
        """Perform RT Plan specific validation.
        
        Adds validation errors to self._validation_errors for plan-specific issues.
        """
        # Validate plan is properly configured
        if not self.prescription_dose:
            self._validation_errors.append("Prescription dose not set - use from_beam_config()")
        
        if not self.fractions:
            self._validation_errors.append("Number of fractions not set - use from_beam_config()")
        
        if not self.beam_configurations:
            self._validation_errors.append("Beam configurations not set - use from_beam_config()")
        
        # Validate plan name length (DICOM RTPlanLabel max 16 characters)
        if len(self.plan_name) > 16:
            self._validation_errors.append(
                f"Plan name too long: {len(self.plan_name)} characters (max 16 for RTPlanLabel)"
            )
        
        # Validate treatment intent
        if self.treatment_intent not in RTPlanTemplate.TREATMENT_INTENTS:
            self._validation_errors.append(
                f"Invalid treatment intent: {self.treatment_intent}"
            )

    def add_beam(self, beam_config: Dict[str, Any]) -> None:
        """Add a beam configuration to the plan.

        Args:
            beam_config: Beam configuration dictionary with required fields:
                name, energy, gantry_angle, dose_weight

        Clinical Notes:
            This method allows incremental beam addition for complex plans.
            After adding beams, ensure total dose weights sum to 1.0 for
            treatment beams (excluding setup beams with dose_weight=0.0).

        Examples:
            Add static beam to existing plan:

            >>> rt_plan.add_beam({
            ...     'name': 'PA',
            ...     'energy': 18,
            ...     'gantry_angle': 180,
            ...     'dose_weight': 0.25,
            ...     'beam_type': 'STATIC'
            ... })

            Add setup beam:

            >>> rt_plan.add_beam({
            ...     'name': 'Setup_kV',
            ...     'energy': 6,
            ...     'gantry_angle': 0,
            ...     'dose_weight': 0.0,
            ...     'beam_type': 'SETUP'
            ... })
        """
        # Validate beam configuration
        RTPlanTemplate._validate_beam_configuration(beam_config, len(self.beam_configurations))
        
        # Add to beam configurations
        self.beam_configurations.append(beam_config)
        
        # Reset validation state since configuration changed
        self._is_validated = False

    def get_prescription_summary(self) -> Dict[str, Any]:
        """Get summary of prescription parameters.

        Returns:
            Dictionary containing prescription summary information

        Examples:
            >>> summary = rt_plan.get_prescription_summary()
            >>> print(f"Total dose: {summary['total_dose']} cGy")
            >>> print(f"Dose per fraction: {summary['dose_per_fraction']} cGy")
            >>> print(f"Treatment beams: {summary['treatment_beams']}")
        """
        if not self.prescription_dose or not self.fractions:
            return {
                'total_dose': None,
                'dose_per_fraction': None,
                'fractions': None,
                'treatment_beams': 0,
                'setup_beams': 0,
            }
        
        treatment_beams = [
            beam for beam in self.beam_configurations
            if beam.get('beam_type', 'STATIC') != 'SETUP'
        ]
        setup_beams = [
            beam for beam in self.beam_configurations
            if beam.get('beam_type', 'STATIC') == 'SETUP'
        ]
        
        return {
            'total_dose': self.prescription_dose,
            'dose_per_fraction': self.prescription_dose / self.fractions,
            'fractions': self.fractions,
            'treatment_beams': len(treatment_beams),
            'setup_beams': len(setup_beams),
            'beam_names': [beam['name'] for beam in treatment_beams],
        }

    def _create_beam_sequence(self, beam_configurations: List[Dict[str, Any]]) -> "Sequence":
        """Create DICOM beam sequence from beam configuration data.

        This method converts beam configuration dictionaries to DICOM beam sequence
        format, handling beam geometry parameters, energy specifications, and
        delivery parameters according to DICOM RT Plan IOD requirements.

        Args:
            beam_configurations: List of beam configuration dictionaries, each containing:
                - name: Beam identifier string (required)
                - energy: Beam energy in MV (photons) or MeV (electrons) (required)
                - gantry_angle: Gantry rotation angle in degrees 0-359.9 (required)
                - dose_weight: Relative beam weight 0.0-1.0 (required)
                - collimator_angle: Collimator rotation angle in degrees (optional, default 0.0)
                - couch_angle: Treatment couch rotation angle in degrees (optional, default 0.0)
                - beam_type: Beam delivery type 'STATIC'|'DYNAMIC'|'SETUP' (optional, default 'STATIC')
                - radiation_type: Radiation type 'PHOTON'|'ELECTRON'|'PROTON' (optional, default 'PHOTON')
                - machine_name: Treatment machine identifier (optional)
                - sad: Source-axis distance in mm (optional, default 1000.0)
                - meterset: Monitor units for beam delivery (optional, default 100.0)

        Returns:
            DICOM Sequence object containing beam definitions with proper IOD structure
            including beam limiting device sequences, control point sequences, and
            delivery parameters for treatment planning system compatibility.

        Raises:
            DicomCreationError: If beam configurations are invalid, contain
                inconsistent parameters, or fail DICOM compliance validation.

        Clinical Notes:
            The beam sequence serves as the primary specification for treatment
            delivery, encoding all geometric and dosimetric parameters needed
            for accurate beam setup and delivery verification:

            **Geometric Parameters**: Gantry, collimator, and couch angles define
            the spatial relationship between the radiation source and patient.
            These must be precise for accurate dose delivery and patient safety.

            **Energy Specification**: Beam energy determines penetration depth
            and dose distribution characteristics. Must match linac capabilities
            and be appropriate for treatment site and depth.

            **Dose Weight Distribution**: Relative beam contributions determine
            dose distribution optimization. Weights should sum to 1.0 for proper
            dose calculation across all treatment beams.

            **Delivery Parameters**: Monitor units, source-axis distance, and
            beam limiting device positions control the physical beam delivery
            and must be consistent with dose calculation algorithms.

        DICOM Compliance:
            Generated beam sequences conform to DICOM Part 3 C.8.8.16 RT Plan IOD
            specification, ensuring compatibility with:
            - Treatment planning systems (TPS) for plan import/export
            - Linear accelerator control systems for delivery verification
            - Quality assurance systems for plan validation
            - Dose calculation engines for independent verification

        Examples:
            Create beam sequence for 3-field conformal plan:

            >>> beam_configs = [
            ...     {
            ...         'name': 'AP', 'energy': 18, 'gantry_angle': 0,
            ...         'dose_weight': 0.33, 'beam_type': 'STATIC'
            ...     },
            ...     {
            ...         'name': 'RPO', 'energy': 18, 'gantry_angle': 120,
            ...         'dose_weight': 0.33, 'beam_type': 'STATIC'
            ...     },
            ...     {
            ...         'name': 'LPO', 'energy': 18, 'gantry_angle': 240,
            ...         'dose_weight': 0.34, 'beam_type': 'STATIC'
            ...     }
            ... ]
            >>> beam_sequence = rt_plan._create_beam_sequence(beam_configs)

            Create beam sequence with setup beam for SBRT:

            >>> sbrt_beams = [
            ...     {
            ...         'name': 'Setup_kV', 'energy': 6, 'gantry_angle': 0,
            ...         'dose_weight': 0.0, 'beam_type': 'SETUP'
            ...     },
            ...     {
            ...         'name': 'Arc_CW', 'energy': 10, 'gantry_angle': 181,
            ...         'dose_weight': 0.5, 'beam_type': 'DYNAMIC'
            ...     },
            ...     {
            ...         'name': 'Arc_CCW', 'energy': 10, 'gantry_angle': 179,
            ...         'dose_weight': 0.5, 'beam_type': 'DYNAMIC'
            ...     }
            ... ]
            >>> beam_sequence = rt_plan._create_beam_sequence(sbrt_beams)
        """
        try:
            # Validate beam configurations before creating sequence
            self._validate_beam_configurations(beam_configurations)
            
            # Use template method to create DICOM-compliant beam sequence
            beam_sequence = RTPlanTemplate._create_beam_sequence(beam_configurations)
            
            # Log beam sequence creation for clinical audit
            self.logger.info(
                f"Created beam sequence with {len(beam_configurations)} beams",
                extra={
                    "num_beams": len(beam_configurations),
                    "beam_names": [beam['name'] for beam in beam_configurations],
                    "treatment_beams": len([b for b in beam_configurations if b.get('beam_type', 'STATIC') != 'SETUP']),
                    "setup_beams": len([b for b in beam_configurations if b.get('beam_type', 'STATIC') == 'SETUP']),
                }
            )
            
            return beam_sequence
            
        except Exception as e:
            raise DicomCreationError(
                f"Failed to create beam sequence: {e}",
                suggestions=[
                    "Verify beam configuration parameters are valid",
                    "Check that beam weights sum to 1.0 for treatment beams",
                    "Ensure beam angles are within valid ranges (0-359.9 degrees)",
                    "Validate beam energies match linac specifications",
                ],
                clinical_context={
                    "num_beams": len(beam_configurations) if beam_configurations else 0,
                    "beam_names": [beam.get('name', 'unknown') for beam in beam_configurations] if beam_configurations else [],
                }
            )

    def _create_fraction_scheme(
        self, 
        prescription_dose: float, 
        fractions: int, 
        beam_configurations: List[Dict[str, Any]]
    ) -> "Sequence":
        """Create DICOM fraction group sequence from prescription parameters.

        This method creates the fraction scheme component of the RT Plan, organizing
        the prescription dose delivery across the specified number of fractions and
        linking beam configurations to the fractionation schedule.

        Args:
            prescription_dose: Total prescribed dose in cGy for complete treatment
            fractions: Number of treatment fractions for dose delivery
            beam_configurations: List of beam configuration dictionaries used to
                calculate per-beam dose contributions and organize treatment delivery

        Returns:
            DICOM Sequence object containing fraction group information with proper
            IOD structure including:
            - Fractionation parameters (number of fractions, fraction pattern)
            - Referenced beam sequence with per-beam dose calculations
            - Dose delivery scheduling and treatment organization

        Raises:
            DicomCreationError: If prescription parameters are invalid, fractionation
                scheme is clinically inappropriate, or DICOM compliance validation fails.

        Clinical Notes:
            The fraction scheme defines the temporal organization of dose delivery,
            critical for treatment efficacy and normal tissue sparing:

            **Fractionation Biology**: Dose per fraction affects both tumor control
            and normal tissue tolerance. Conventional fractionation (1.8-2.0 Gy/fx)
            optimizes therapeutic ratio, while hypofractionation (>2.0 Gy/fx) may
            be appropriate for specific indications.

            **Dose Distribution**: Per-beam dose calculations ensure accurate dose
            delivery verification and enable independent dose calculations by
            treatment planning systems and quality assurance programs.

            **Treatment Scheduling**: Fraction group organization supports treatment
            delivery scheduling and helps coordinate multi-beam delivery sequences
            for complex treatments like IMRT and VMAT.

            **Clinical Safety**: Validates prescription doses against typical clinical
            ranges and generates warnings for doses exceeding established protocols
            while maintaining flexibility for research and QA applications.

        DICOM Compliance:
            Generated fraction schemes conform to DICOM Part 3 C.8.8.16 RT Plan IOD
            specification, ensuring proper integration with:
            - Treatment planning systems for dose calculation verification
            - Record and verify systems for treatment delivery tracking
            - Clinical information systems for treatment monitoring
            - Quality assurance systems for plan validation

        Examples:
            Create fraction scheme for conventional fractionation:

            >>> prescription_dose = 7000  # 70 Gy
            >>> fractions = 35
            >>> beam_configs = [
            ...     {'name': 'AP', 'dose_weight': 0.33},
            ...     {'name': 'RPO', 'dose_weight': 0.33},
            ...     {'name': 'LPO', 'dose_weight': 0.34}
            ... ]
            >>> fraction_scheme = rt_plan._create_fraction_scheme(
            ...     prescription_dose, fractions, beam_configs
            ... )
            # Results in 200 cGy per fraction over 35 fractions

            Create fraction scheme for SBRT hypofractionation:

            >>> prescription_dose = 5000  # 50 Gy
            >>> fractions = 5
            >>> beam_configs = [
            ...     {'name': 'Arc_1', 'dose_weight': 0.5},
            ...     {'name': 'Arc_2', 'dose_weight': 0.5}
            ... ]
            >>> fraction_scheme = rt_plan._create_fraction_scheme(
            ...     prescription_dose, fractions, beam_configs
            ... )
            # Results in 1000 cGy per fraction over 5 fractions
        """
        try:
            # Validate prescription parameters
            self._validate_prescription_parameters(prescription_dose, fractions)
            
            # Use template method to create DICOM-compliant fraction group sequence
            fraction_sequence = RTPlanTemplate._create_fraction_group_sequence(
                prescription_dose, fractions, beam_configurations
            )
            
            # Calculate clinical parameters for logging
            dose_per_fraction = prescription_dose / fractions
            treatment_beams = [b for b in beam_configurations if b.get('beam_type', 'STATIC') != 'SETUP']
            
            # Log fraction scheme creation for clinical audit
            self.logger.info(
                f"Created fraction scheme: {prescription_dose} cGy in {fractions} fractions "
                f"({dose_per_fraction:.1f} cGy/fx) with {len(treatment_beams)} treatment beams",
                extra={
                    "total_dose": prescription_dose,
                    "fractions": fractions,
                    "dose_per_fraction": dose_per_fraction,
                    "treatment_beams": len(treatment_beams),
                    "fractionation_type": "conventional" if dose_per_fraction <= 220 else "hypofractionated",
                }
            )
            
            return fraction_sequence
            
        except Exception as e:
            raise DicomCreationError(
                f"Failed to create fraction scheme: {e}",
                suggestions=[
                    "Verify prescription dose is positive and clinically appropriate",
                    "Check that number of fractions is positive integer",
                    "Ensure beam configurations are valid for dose calculation",
                    "Review dose per fraction against clinical protocols",
                ],
                clinical_context={
                    "prescription_dose": prescription_dose,
                    "fractions": fractions,
                    "dose_per_fraction": prescription_dose / fractions if fractions > 0 else None,
                    "num_beams": len(beam_configurations) if beam_configurations else 0,
                }
            )

    def _validate_beam_configurations(self, beam_configurations: List[Dict[str, Any]]) -> None:
        """Validate beam configurations for clinical safety and DICOM compliance.
        
        Args:
            beam_configurations: List of beam configuration dictionaries to validate
            
        Raises:
            DicomCreationError: If beam configurations are invalid
        """
        if not beam_configurations:
            raise DicomCreationError(
                "No beam configurations provided",
                suggestions=[
                    "Provide at least one beam configuration",
                    "Include both treatment and setup beams if applicable",
                    "Each beam should specify name, energy, gantry_angle, dose_weight",
                ]
            )
        
        # Validate individual beam configurations
        for i, beam_config in enumerate(beam_configurations):
            RTPlanTemplate._validate_beam_configuration(beam_config, i)
        
        # Validate beam weight distribution for treatment beams
        treatment_beams = [b for b in beam_configurations if b.get('beam_type', 'STATIC') != 'SETUP']
        if treatment_beams:
            total_weight = sum(beam['dose_weight'] for beam in treatment_beams)
            if abs(total_weight - 1.0) > 0.001:  # Allow small floating point errors
                raise DicomCreationError(
                    f"Treatment beam weights sum to {total_weight:.3f}, must sum to 1.0",
                    suggestions=[
                        "Adjust beam dose_weight values to sum to 1.0",
                        "Setup beams should have dose_weight=0.0",
                        "Treatment beam weights represent dose fractions",
                    ],
                    clinical_context={
                        "total_weight": total_weight,
                        "treatment_beams": len(treatment_beams),
                        "beam_weights": [beam['dose_weight'] for beam in treatment_beams],
                    }
                )
        
        # Validate beam name uniqueness
        beam_names = [beam['name'] for beam in beam_configurations]
        if len(beam_names) != len(set(beam_names)):
            duplicate_names = list(set([name for name in beam_names if beam_names.count(name) > 1]))
            raise DicomCreationError(
                f"Duplicate beam names found: {duplicate_names}",
                suggestions=[
                    "Use unique names for each beam (e.g., 'AP', 'PA', 'LPO')",
                    "Beam names are used for identification in TPS",
                    "Consider adding numbering for similar beam types",
                ]
            )

    def _validate_prescription_parameters(self, prescription_dose: float, fractions: int) -> None:
        """Validate prescription parameters for clinical appropriateness.
        
        Args:
            prescription_dose: Total prescribed dose in cGy
            fractions: Number of treatment fractions
            
        Raises:
            DicomCreationError: If prescription parameters are invalid
        """
        # Validate prescription dose
        if not isinstance(prescription_dose, (int, float)) or prescription_dose <= 0:
            raise DicomCreationError(
                f"Prescription dose must be positive number, got {prescription_dose}",
                suggestions=[
                    "Prescription dose should be in cGy (e.g., 7000 for 70 Gy)",
                    "Typical ranges: 2000-8000 cGy for curative treatments",
                    "Check dose units and calculation accuracy",
                ]
            )
        
        # Validate number of fractions
        if not isinstance(fractions, int) or fractions <= 0:
            raise DicomCreationError(
                f"Number of fractions must be positive integer, got {fractions}",
                suggestions=[
                    "Fractions should be integer number of treatment sessions",
                    "Typical ranges: 1-40 fractions depending on indication",
                    "Single fraction (1) for SRS, 5 for SBRT, 25-35 for conventional",
                ]
            )
        
        # Validate dose per fraction (warnings only, not blocking)
        dose_per_fraction = prescription_dose / fractions
        if dose_per_fraction > 3000:  # > 30 Gy per fraction
            self.logger.warning(
                f"Very high dose per fraction: {dose_per_fraction:.1f} cGy. "
                f"Consider clinical appropriateness for research/QA protocols.",
                extra={
                    "prescription_dose": prescription_dose,
                    "fractions": fractions,
                    "dose_per_fraction": dose_per_fraction,
                }
            )

    def __repr__(self) -> str:
        """String representation for debugging."""
        prescription_summary = self.get_prescription_summary()
        patient_id = self.patient_info.get("PatientID", "N/A")
        
        if prescription_summary['total_dose']:
            dose_info = f"{prescription_summary['total_dose']} cGy in {prescription_summary['fractions']} fx"
            beam_info = f"{prescription_summary['treatment_beams']} beams"
        else:
            dose_info = "not configured"
            beam_info = "no beams"
        
        validation_status = "validated" if self._is_validated else "not validated"
        
        return (
            f"RTPlan(PatientID={patient_id}, plan='{self.plan_name}', "
            f"dose={dose_info}, {beam_info}, {validation_status})"
        )