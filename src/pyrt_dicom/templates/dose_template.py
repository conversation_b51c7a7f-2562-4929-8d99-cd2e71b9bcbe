"""
RT Dose IOD Template Implementation.

Provides a complete DICOM Information Object Definition (IOD) template for RT
Dose objects, implementing all required and optional modules according to DICOM
Part 3 Section C.8.8.3 (RT Dose IOD). This template incorporates proven patterns
from PyMedPhys dose handling while providing a simplified API for broader compatibility.

## Clinical Usage

The RT Dose template ensures that all created RTDOSE DICOM files contain the
proper DICOM elements required for clinical viewing systems, dose analysis tools,
and treatment planning systems. This includes mandatory elements for dose grid
definition, multi-frame pixel data, and geometric relationships with planning CT.

```python
import pyrt_dicom as prt
import numpy as np

# Create RT Dose from 3D dose calculation
dose_array = np.random.rand(64, 256, 256) * 70.0  # Dose in Gy (Z, Y, X)
ct_reference = pydicom.dcmread('planning_ct.dcm')

rt_dose = prt.RTDose.from_array(
    dose_array=dose_array,
    reference_image=ct_reference,
    dose_units='GY',
    dose_type='PHYSICAL',
    summation_type='PLAN'
)
rt_dose.save('patient_dose.dcm')
```

## DICOM Compliance

This template implements the complete RT Dose IOD as defined in DICOM PS 3.3
Part 3, ensuring compatibility with all major dose analysis and treatment
planning systems including:

- Varian Eclipse
- Elekta Monaco
- RaySearch RayStation  
- Philips Pinnacle
- CMS XiO/Monaco
- Accuray CyberKnife
- BrainLAB iPlan
- MIM Software
- Velocity Medical Solutions
- ScandiDos Delta4

## PyMedPhys Integration

The dose template leverages proven patterns from PyMedPhys dose handling:

- **DoseGridScaling Algorithm**: Precision-optimized scaling factor calculation
- **Multi-Frame Structure**: Proper DICOM multi-frame pixel data organization
- **Coordinate System**: Consistent axis handling and spatial alignment
- **Performance**: Memory-efficient processing for large dose grids (up to 512³)

## Cross-References

**Related Classes**:
- :class:`~pyrt_dicom.core.rt_dose.RTDose` - RT Dose implementation
- :class:`~pyrt_dicom.core.base.BaseDicomCreator` - Base DICOM creator framework
- :class:`~pyrt_dicom.coordinates.transforms.CoordinateTransformer` - Spatial consistency

**See Also**:
- DICOM Part 3 Section C.8.8.3: RT Dose IOD
- DICOM Part 6: Data Dictionary for element definitions
- :mod:`~pyrt_dicom.validation.dicom_compliance` - IOD compliance validation
- :doc:`~pyrt_dicom.docs.research.pymedphys_dose_patterns` - PyMedPhys integration patterns
"""

from typing import Dict, Any, Optional, List, Tuple, Union
import pydicom
from pydicom.dataset import Dataset
from pydicom.sequence import Sequence
from pydicom.uid import RTDoseStorage
import numpy as np
from datetime import datetime

from ..utils.exceptions import TemplateError
from ..uid_generation.generators import DefaultUIDGenerator


class RTDoseTemplate:
    """DICOM RT Dose IOD template implementation.

    Creates properly structured DICOM datasets conforming to the RT Dose IOD
    specification (DICOM PS 3.3 C.8.8.3). Incorporates proven patterns from
    PyMedPhys dose handling while providing enhanced API for broader input
    source compatibility.

    Clinical Notes:
        The RT Dose IOD is critical for radiotherapy workflows as it stores
        calculated dose distributions from treatment planning systems. Key
        clinical requirements include:

        - Accurate dose grid scaling for sub-percent precision
        - Multi-frame pixel data organization for 3D dose visualization
        - Geometric consistency with planning CT for spatial accuracy
        - Frame of Reference UID alignment for treatment delivery
        - Dose summation type classification for plan evaluation
        - Dose calibration and unit specification for clinical interpretation

        Dose data must maintain clinical accuracy (typically <1% uncertainty)
        to support dose-volume histogram analysis, plan comparison, and
        regulatory dose reporting requirements.

    Attributes:
        SOP_CLASS_UID: RT Dose Storage SOP Class UID (1.2.840.10008.*******.1.481.2)
        REQUIRED_MODULES: List of mandatory DICOM modules for RT Dose IOD
        OPTIONAL_MODULES: List of optional modules that enhance clinical functionality
        DOSE_UNITS: Valid dose unit specifications ('GY' or 'RELATIVE')
        DOSE_TYPES: Valid dose type classifications ('PHYSICAL', 'EFFECTIVE', 'ERROR')
        SUMMATION_TYPES: Valid summation type classifications ('PLAN', 'BEAM', 'BRACHY', 'CONTROL_POINT')
    """

    # RT Dose Storage SOP Class UID from DICOM Part 6
    SOP_CLASS_UID = RTDoseStorage

    # Required modules per DICOM Part 3 C.8.8.3
    REQUIRED_MODULES = [
        "Patient",
        "General Study",
        "RT Series", 
        "Frame of Reference",
        "General Equipment",
        "General Image",
        "Image Plane",
        "Image Pixel",
        "Multi-frame",
        "RT Dose",
        "SOP Common",
    ]

    # Optional modules that enhance clinical functionality
    OPTIONAL_MODULES = [
        "Clinical Trial Subject",
        "Clinical Trial Study",
        "Clinical Trial Series", 
        "Approval",
        "Audio", 
        "Cine",
        "Overlay Plane",
        "VOI LUT",
        "ICC Profile",
    ]

    # Valid dose units per DICOM standard
    DOSE_UNITS = [
        'GY',       # Gray (absolute dose)
        'RELATIVE'  # Relative dose (percentage)
    ]

    # Valid dose types per DICOM standard  
    DOSE_TYPES = [
        'PHYSICAL',   # Physical dose
        'EFFECTIVE',  # Effective dose
        'ERROR'       # Dose error/uncertainty
    ]

    # Valid summation types per DICOM standard
    SUMMATION_TYPES = [
        'PLAN',          # Plan sum
        'BEAM',          # Individual beam
        'BRACHY',        # Brachytherapy
        'CONTROL_POINT'  # Control point dose
    ]

    @classmethod
    def create_dataset(
        cls,
        dose_array: np.ndarray,
        dose_scaling: float,
        pixel_spacing: Tuple[float, float],
        slice_thickness: float,
        image_position_patient: Tuple[float, float, float],
        image_orientation_patient: Tuple[float, float, float, float, float, float],
        frame_of_reference_uid: str,
        dose_units: str = 'GY',
        dose_type: str = 'PHYSICAL',
        summation_type: str = 'PLAN',
        referenced_rt_plan_sop_instance_uid: Optional[str] = None,
        pixel_data: Optional[bytes] = None,
        **kwargs,
    ) -> Dataset:
        """Create RT Dose dataset with proper IOD structure.

        Args:
            dose_array: 3D dose array in (Z, Y, X) convention with shape (slices, rows, columns).
                Values should be in the units specified by dose_units parameter.
            dose_scaling: DoseGridScaling factor for converting pixel values to dose.
                Formula: dose_value = pixel_value * dose_scaling
            pixel_spacing: (row_spacing, column_spacing) in mm for dose grid resolution
            slice_thickness: Spacing between dose planes in mm
            image_position_patient: (x, y, z) coordinates in mm of the upper left hand
                corner (center of first voxel) of the dose grid in Patient Coordinate System
            image_orientation_patient: Six-element tuple defining row and column direction
                cosines relative to patient coordinate system
            frame_of_reference_uid: Frame of Reference UID linking to planning CT
            dose_units: Dose units - 'GY' for Gray or 'RELATIVE' for percentage
            dose_type: Dose type classification - 'PHYSICAL', 'EFFECTIVE', or 'ERROR'
            summation_type: Dose summation type - 'PLAN', 'BEAM', 'BRACHY', or 'CONTROL_POINT'
            referenced_rt_plan_sop_instance_uid: Optional SOP Instance UID of related RT Plan
            pixel_data: Optional pre-created pixel data bytes. If None, will be created from dose_array
            **kwargs: Additional DICOM elements to include in dataset

        Returns:
            Complete DICOM dataset conforming to RT Dose IOD specification

        Raises:
            TemplateError: If dose parameters are invalid, geometric parameters are 
                inconsistent, or required DICOM elements cannot be created

        Clinical Notes:
            The RT Dose template ensures proper clinical workflow integration:

            **Dose Grid Scaling**: Following PyMedPhys patterns, the scaling factor
            is optimized for maximum precision while staying within DICOM data type
            limits. Typical scaling: max_dose / max_pixel_value.

            **Multi-Frame Structure**: 3D dose data is stored as multi-frame DICOM
            with proper frame offset vectors and increment pointers for spatial
            reconstruction by clinical viewers.

            **Geometric Consistency**: All spatial parameters must align with the
            referenced planning CT to ensure accurate dose overlay and analysis.

            **Clinical Validation**: Dose units, types, and summation classifications
            follow DICOM standards for proper interpretation by planning systems.

        Examples:
            Create basic dose from treatment planning calculation:

            >>> dose_array = np.random.rand(64, 256, 256) * 70.0  # 70 Gy max dose
            >>> dose_scaling = 70.0 / np.iinfo(np.uint32).max    # Optimal precision
            >>> dataset = RTDoseTemplate.create_dataset(
            ...     dose_array=dose_array,
            ...     dose_scaling=dose_scaling,
            ...     pixel_spacing=(2.0, 2.0),
            ...     slice_thickness=2.5,
            ...     image_position_patient=(-256.0, -256.0, -160.0),
            ...     image_orientation_patient=(1, 0, 0, 0, 1, 0),
            ...     frame_of_reference_uid='*******.5.6.7',
            ...     dose_units='GY',
            ...     dose_type='PHYSICAL'
            ... )
            >>> print(dataset.SOPClassUID)
            1.2.840.10008.*******.1.481.2

            Create relative dose distribution:

            >>> # Dose as percentage of prescription (0-100%)
            >>> relative_dose = dose_array / prescription_dose * 100.0
            >>> dataset = RTDoseTemplate.create_dataset(
            ...     dose_array=relative_dose,
            ...     dose_scaling=100.0 / np.iinfo(np.uint16).max,
            ...     dose_units='RELATIVE',
            ...     dose_type='PHYSICAL',
            ...     summation_type='PLAN',
            ...     # ... other geometric parameters
            ... )

            Create beam-specific dose distribution:

            >>> beam_dose = get_beam_dose_array()
            >>> dataset = RTDoseTemplate.create_dataset(
            ...     dose_array=beam_dose,
            ...     summation_type='BEAM',
            ...     referenced_rt_plan_sop_instance_uid=plan_sop_uid,
            ...     # ... other parameters
            ... )
        """
        # Validate input parameters
        cls._validate_dose_parameters(
            dose_array, dose_units, dose_type, summation_type
        )
        cls._validate_geometric_parameters(
            pixel_spacing, slice_thickness, image_position_patient, image_orientation_patient
        )

        # Create base dataset
        dataset = Dataset()

        # SOP Common Module (C.12.1) - Required
        dataset.SOPClassUID = cls.SOP_CLASS_UID
        uid_generator = DefaultUIDGenerator.create_default_generator()
        dataset.SOPInstanceUID = uid_generator.generate_sop_instance_uid()

        # RT Series Module (C.8.8.1) - Required for RT objects
        dataset.Modality = "RTDOSE"
        dataset.SeriesDescription = kwargs.get("SeriesDescription", "RT Dose")

        # General Equipment Module (C.7.5.1) - Required
        dataset.Manufacturer = kwargs.get("Manufacturer", "pyrt-dicom")
        dataset.ManufacturerModelName = kwargs.get("ManufacturerModelName", "pyrt-dicom DICOM Creator")
        dataset.SoftwareVersions = kwargs.get("SoftwareVersions", "1.0.0")

        # Frame of Reference Module (C.7.4.1) - Required
        dataset.FrameOfReferenceUID = frame_of_reference_uid
        dataset.PositionReferenceIndicator = ""

        # General Image Module (C.7.6.1) - Required
        dataset.InstanceNumber = kwargs.get("InstanceNumber", "1")
        dataset.PatientOrientation = ""
        now = datetime.now()
        dataset.ContentDate = now.strftime("%Y%m%d")
        dataset.ContentTime = now.strftime("%H%M%S.%f")[:10]
        dataset.ImageType = ["DERIVED", "SECONDARY", "OTHER"]

        # Image Plane Module (C.7.6.2) - Required
        dataset.PixelSpacing = [float(pixel_spacing[0]), float(pixel_spacing[1])]
        dataset.ImageOrientationPatient = [float(x) for x in image_orientation_patient]
        dataset.ImagePositionPatient = [float(x) for x in image_position_patient]
        dataset.SliceThickness = float(slice_thickness)

        # Image Pixel Module (C.7.6.3) - Required
        num_slices, rows, columns = dose_array.shape
        dataset.SamplesPerPixel = 1
        dataset.PhotometricInterpretation = "MONOCHROME2"
        dataset.Rows = rows
        dataset.Columns = columns
        dataset.BitsAllocated = 32  # 32-bit for dose precision
        dataset.BitsStored = 32
        dataset.HighBit = 31
        dataset.PixelRepresentation = 0  # Unsigned
        
        # Use provided pixel data or create it from dose array
        if pixel_data is not None:
            dataset.PixelData = pixel_data
        else:
            dataset.PixelData = cls._create_pixel_data(dose_array, dose_scaling)

        # Multi-frame Module (C.7.6.6) - Required for 3D dose
        dataset.NumberOfFrames = num_slices
        dataset.FrameIncrementPointer = 0x3004000C  # Points to GridFrameOffsetVector

        # RT Dose Module (C.8.8.3) - Required
        dataset.DoseUnits = dose_units
        dataset.DoseType = dose_type
        dataset.DoseSummationType = summation_type
        dataset.DoseGridScaling = float(dose_scaling)

        # Grid Frame Offset Vector - Z positions for each dose plane
        z_start = image_position_patient[2]
        grid_frame_offsets = [z_start + i * slice_thickness for i in range(num_slices)]
        dataset.GridFrameOffsetVector = grid_frame_offsets

        # Optional RT Plan reference
        if referenced_rt_plan_sop_instance_uid:
            ref_rt_plan_seq = Sequence()
            ref_rt_plan_item = Dataset()
            ref_rt_plan_item.ReferencedSOPClassUID = "1.2.840.10008.*******.1.481.5"  # RT Plan Storage
            ref_rt_plan_item.ReferencedSOPInstanceUID = referenced_rt_plan_sop_instance_uid
            ref_rt_plan_seq.append(ref_rt_plan_item)
            dataset.ReferencedRTPlanSequence = ref_rt_plan_seq

        # Add any additional elements from kwargs
        for key, value in kwargs.items():
            if key not in [
                "SeriesDescription",
                "InstanceNumber",
            ]:
                try:
                    setattr(dataset, key, value)
                except Exception:
                    # Skip invalid DICOM elements
                    continue

        return dataset

    @classmethod
    def _validate_dose_parameters(
        cls,
        dose_array: np.ndarray,
        dose_units: str,
        dose_type: str,
        summation_type: str,
    ) -> None:
        """Validate dose-specific parameters.
        
        Args:
            dose_array: 3D dose array to validate
            dose_units: Dose units to validate
            dose_type: Dose type to validate  
            summation_type: Summation type to validate
            
        Raises:
            TemplateError: If any parameters are invalid
        """
        # Validate dose array
        if not isinstance(dose_array, np.ndarray):
            raise TemplateError(
                "dose_array must be a numpy ndarray",
                modality="RTDOSE",
                suggestions=[
                    "Convert dose data to numpy.ndarray format",
                    "Use np.array() to convert from lists or other formats",
                    "Ensure dose data is numeric (float or int)",
                ],
            )

        if dose_array.ndim != 3:
            raise TemplateError(
                f"dose_array must be 3D, got {dose_array.ndim}D array",
                modality="RTDOSE",
                suggestions=[
                    "Dose array must have shape (Z, Y, X) = (slices, rows, columns)",
                    "Use dose_array.reshape() to convert 1D/2D arrays to 3D",
                    "Check that all dose slices are included in the array",
                ],
                clinical_context={
                    "provided_shape": dose_array.shape,
                    "expected_dimensions": 3,
                    "convention": "(slices, rows, columns)",
                },
            )

        # Validate array contains finite values
        if not np.all(np.isfinite(dose_array)):
            raise TemplateError(
                "dose_array contains non-finite values (NaN or Inf)",
                modality="RTDOSE",
                suggestions=[
                    "Check dose calculation for numerical errors",
                    "Replace NaN values with zeros for regions outside patient",
                    "Verify dose calculation algorithm stability",
                ],
            )

        # Validate dose units
        if dose_units not in cls.DOSE_UNITS:
            raise TemplateError(
                f"Invalid dose_units: '{dose_units}', must be one of {cls.DOSE_UNITS}",
                modality="RTDOSE",
                suggestions=[
                    "Use 'GY' for absolute dose in Gray",
                    "Use 'RELATIVE' for relative dose as percentage",
                    "Check dose calculation output units",
                ],
                clinical_context={
                    "provided_units": dose_units,
                    "valid_units": cls.DOSE_UNITS,
                },
            )

        # Validate dose type
        if dose_type not in cls.DOSE_TYPES:
            raise TemplateError(
                f"Invalid dose_type: '{dose_type}', must be one of {cls.DOSE_TYPES}",
                modality="RTDOSE",
                suggestions=[
                    "Use 'PHYSICAL' for standard treatment planning dose",
                    "Use 'EFFECTIVE' for biological effective dose",
                    "Use 'ERROR' for dose uncertainty distributions",
                ],
                clinical_context={
                    "provided_type": dose_type,
                    "valid_types": cls.DOSE_TYPES,
                },
            )

        # Validate summation type
        if summation_type not in cls.SUMMATION_TYPES:
            raise TemplateError(
                f"Invalid summation_type: '{summation_type}', must be one of {cls.SUMMATION_TYPES}",
                modality="RTDOSE",
                suggestions=[
                    "Use 'PLAN' for total plan dose distribution",
                    "Use 'BEAM' for individual beam contributions",
                    "Use 'BRACHY' for brachytherapy dose",
                    "Use 'CONTROL_POINT' for individual control point dose",
                ],
                clinical_context={
                    "provided_summation": summation_type,
                    "valid_summations": cls.SUMMATION_TYPES,
                },
            )

    @classmethod
    def _validate_geometric_parameters(
        cls,
        pixel_spacing: Tuple[float, float],
        slice_thickness: float,
        image_position_patient: Tuple[float, float, float],
        image_orientation_patient: Tuple[float, float, float, float, float, float],
    ) -> None:
        """Validate geometric parameters for clinical consistency.
        
        Args:
            pixel_spacing: (row_spacing, column_spacing) in mm
            slice_thickness: Slice spacing in mm
            image_position_patient: (x, y, z) position in mm
            image_orientation_patient: Six direction cosines
            
        Raises:
            TemplateError: If geometric parameters are invalid
        """
        # Validate pixel spacing
        if len(pixel_spacing) != 2:
            raise TemplateError(
                f"pixel_spacing must have 2 elements, got {len(pixel_spacing)}",
                modality="RTDOSE",
                suggestions=[
                    "Provide pixel spacing as (row_spacing, column_spacing)",
                    "Both values should be in millimeters",
                    "Extract from reference CT ImagePlane module",
                ],
            )

        for i, spacing in enumerate(pixel_spacing):
            if spacing <= 0:
                raise TemplateError(
                    f"pixel_spacing[{i}] must be positive, got {spacing}",
                    modality="RTDOSE",
                    suggestions=[
                        "Pixel spacing must be greater than zero",
                        "Check reference CT pixel spacing values",
                        "Typical values: 1-5 mm for dose calculations",
                    ],
                )

        # Validate slice thickness
        if slice_thickness <= 0:
            raise TemplateError(
                f"slice_thickness must be positive, got {slice_thickness}",
                modality="RTDOSE",
                suggestions=[
                    "Slice thickness must be greater than zero",
                    "Check reference CT slice spacing",
                    "Typical values: 1-5 mm for dose calculations",
                ],
            )

        # Validate image position patient
        if len(image_position_patient) != 3:
            raise TemplateError(
                f"image_position_patient must have 3 elements, got {len(image_position_patient)}",
                modality="RTDOSE",
                suggestions=[
                    "Provide position as (x, y, z) coordinates in mm",
                    "Extract from reference CT Image Plane module",
                    "Use Patient Coordinate System (LPS)",
                ],
            )

        # Validate image orientation patient  
        if len(image_orientation_patient) != 6:
            raise TemplateError(
                f"image_orientation_patient must have 6 elements, got {len(image_orientation_patient)}",
                modality="RTDOSE",
                suggestions=[
                    "Provide orientation as 6 direction cosines",
                    "Format: [row_x, row_y, row_z, col_x, col_y, col_z]",
                    "Extract from reference CT Image Plane module",
                ],
            )

        # Validate orientation cosines are unit vectors
        row_cosines = np.array(image_orientation_patient[:3])
        col_cosines = np.array(image_orientation_patient[3:])
        
        row_magnitude = np.linalg.norm(row_cosines)
        col_magnitude = np.linalg.norm(col_cosines)
        
        if not np.isclose(row_magnitude, 1.0, atol=1e-3):
            raise TemplateError(
                f"Row direction cosines not normalized: magnitude = {row_magnitude}",
                modality="RTDOSE",
                suggestions=[
                    "Direction cosines should be unit vectors (magnitude = 1.0)",
                    "Check reference CT ImageOrientationPatient values",
                    "Normalize vectors: cosines / np.linalg.norm(cosines)",
                ],
            )

        if not np.isclose(col_magnitude, 1.0, atol=1e-3):
            raise TemplateError(
                f"Column direction cosines not normalized: magnitude = {col_magnitude}",
                modality="RTDOSE",
                suggestions=[
                    "Direction cosines should be unit vectors (magnitude = 1.0)",
                    "Check reference CT ImageOrientationPatient values", 
                    "Normalize vectors: cosines / np.linalg.norm(cosines)",
                ],
            )

        # Validate orthogonality
        dot_product = np.dot(row_cosines, col_cosines)
        if not np.isclose(dot_product, 0.0, atol=1e-3):
            raise TemplateError(
                f"Row and column direction cosines not orthogonal: dot product = {dot_product}",
                modality="RTDOSE",
                suggestions=[
                    "Row and column directions should be perpendicular",
                    "Check reference CT ImageOrientationPatient values",
                    "Verify coordinate system is right-handed orthogonal",
                ],
            )

    @classmethod
    def _create_pixel_data(cls, dose_array: np.ndarray, dose_scaling: float) -> bytes:
        """Create multi-frame pixel data from dose array.
        
        Converts 3D dose array to DICOM pixel data using PyMedPhys-inspired
        scaling and multi-frame organization.
        
        Args:
            dose_array: 3D dose array in (Z, Y, X) convention
            dose_scaling: DoseGridScaling factor
            
        Returns:
            Encoded pixel data bytes for DICOM storage
            
        Clinical Notes:
            Follows PyMedPhys patterns for optimal precision and compatibility:
            - Uses 32-bit unsigned integers for dose precision
            - Maintains (Z, Y, X) format for DICOM compliance
            - Organizes as multi-frame structure for 3D visualization
        """
        # Convert dose to pixel values using scaling factor
        # Formula: pixel_value = dose_value / dose_scaling
        pixel_array = (dose_array / dose_scaling).astype(np.uint32)
        
        # DICOM pixel data expects (Z, Y, X) format - no axis swapping needed
        # Multi-frame DICOM stores as sequence of 2D frames in (Z, Y, X) order
        pixel_data_flat = pixel_array.flatten()
        
        # Convert to bytes for DICOM storage
        return pixel_data_flat.tobytes()

    @classmethod
    def validate_compliance(cls, dataset: Dataset) -> List[str]:
        """Validate dataset compliance with RT Dose IOD.

        Args:
            dataset: DICOM dataset to validate

        Returns:
            List of validation error messages. Empty list indicates compliance

        Clinical Notes:
            Validation ensures that created RT Dose objects will load properly
            in treatment planning systems and maintain geometric relationships
            with planning CT images. Checks include DICOM IOD compliance,
            dose parameter validity, and multi-frame structure integrity.
        """
        errors = []

        # Check SOP Class UID
        if not hasattr(dataset, "SOPClassUID"):
            errors.append("Missing required SOPClassUID")
        elif dataset.SOPClassUID != cls.SOP_CLASS_UID:
            errors.append(
                f"Invalid SOPClassUID: expected {cls.SOP_CLASS_UID}, got {dataset.SOPClassUID}"
            )

        # Check required RT Dose elements
        required_elements = [
            "Modality",
            "DoseUnits",
            "DoseType", 
            "DoseSummationType",
            "DoseGridScaling",
            "GridFrameOffsetVector",
            "NumberOfFrames",
            "FrameIncrementPointer",
            "PixelData",
            "Rows",
            "Columns",
            "BitsAllocated",
        ]

        for element in required_elements:
            if not hasattr(dataset, element):
                errors.append(f"Missing required element: {element}")

        # Validate modality
        if hasattr(dataset, "Modality") and dataset.Modality != "RTDOSE":
            errors.append(f"Invalid modality for RT Dose: {dataset.Modality}")

        # Validate dose units
        if hasattr(dataset, "DoseUnits") and dataset.DoseUnits not in cls.DOSE_UNITS:
            errors.append(f"Invalid DoseUnits: {dataset.DoseUnits}")

        # Validate dose type
        if hasattr(dataset, "DoseType") and dataset.DoseType not in cls.DOSE_TYPES:
            errors.append(f"Invalid DoseType: {dataset.DoseType}")

        # Validate summation type
        if hasattr(dataset, "DoseSummationType") and dataset.DoseSummationType not in cls.SUMMATION_TYPES:
            errors.append(f"Invalid DoseSummationType: {dataset.DoseSummationType}")

        # Validate multi-frame consistency
        if hasattr(dataset, "NumberOfFrames") and hasattr(dataset, "GridFrameOffsetVector"):
            num_frames = int(dataset.NumberOfFrames)
            num_offsets = len(dataset.GridFrameOffsetVector)
            if num_frames != num_offsets:
                errors.append(
                    f"NumberOfFrames ({num_frames}) does not match GridFrameOffsetVector length ({num_offsets})"
                )

        # Validate FrameIncrementPointer points to GridFrameOffsetVector
        if hasattr(dataset, "FrameIncrementPointer"):
            if dataset.FrameIncrementPointer != 0x3004000C:
                errors.append(
                    f"FrameIncrementPointer should point to GridFrameOffsetVector (0x3004000C), "
                    f"got {hex(dataset.FrameIncrementPointer)}"
                )

        # Validate DoseGridScaling is positive
        if hasattr(dataset, "DoseGridScaling"):
            if dataset.DoseGridScaling <= 0:
                errors.append(f"DoseGridScaling must be positive, got {dataset.DoseGridScaling}")

        return errors