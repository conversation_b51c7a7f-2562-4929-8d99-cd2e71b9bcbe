"""
RT Plan IOD Template Implementation.

Provides a complete DICOM Information Object Definition (IOD) template for RT
Plan objects, implementing all required and optional modules according to DICOM
Part 3 Section C.8.8.16 (RT Plan IOD). This template incorporates proven patterns
from PyMedPhys plan handling while providing a simplified API for broader compatibility.

## Clinical Usage

The RT Plan template ensures that all created RTPLAN DICOM files contain the
proper DICOM elements required for treatment planning systems, beam delivery
verification, and plan comparison workflows. This includes mandatory elements for
beam geometry, fraction schemes, prescription dose, and geometric relationships
with RT structures and dose objects.

```python
import pyrt_dicom as prt
import numpy as np

# Create RT Plan from beam configuration
beam_config = {
    'prescription_dose': 7000,  # cGy
    'fractions': 35,
    'beams': [
        {'name': 'Beam_1', 'energy': 18, 'angle': 0, 'dose_weight': 0.33},
        {'name': 'Beam_2', 'energy': 18, 'angle': 120, 'dose_weight': 0.33},
        {'name': 'Beam_3', 'energy': 18, 'angle': 240, 'dose_weight': 0.34}
    ]
}

rt_plan = prt.RTPlan.from_beam_config(
    prescription=beam_config,
    reference_dose=rt_dose,
    patient_info=patient_info
)
rt_plan.save('patient_plan.dcm')
```

## DICOM Compliance

This template implements the complete RT Plan IOD as defined in DICOM PS 3.3
Part 3, ensuring compatibility with all major treatment planning and delivery
systems including:

- Varian Eclipse TPS and TrueBeam
- Elekta Monaco TPS and Versa HD
- RaySearch RayStation
- Philips Pinnacle³
- CMS XiO/Monaco
- Accuray CyberKnife
- BrainLAB iPlan RT
- Raysearch Raystation
- ViewRay MRIdian
- IBA ProteusONE

## PyMedPhys Integration

The plan template leverages proven patterns from PyMedPhys plan handling:

- **Beam Sequence Organization**: Proper DICOM beam sequence structure
- **Control Point Handling**: Multi-leaf collimator and dose rate parameters
- **Fraction Group Management**: Prescription dose distribution across beams
- **Geometric Validation**: Beam angle and collimator position consistency

## Cross-References

**Related Classes**:
- :class:`~pyrt_dicom.core.rt_plan.RTPlan` - RT Plan implementation
- :class:`~pyrt_dicom.core.base.BaseDicomCreator` - Base DICOM creator framework
- :class:`~pyrt_dicom.coordinates.transforms.CoordinateTransformer` - Spatial consistency

**See Also**:
- DICOM Part 3 Section C.8.8.16: RT Plan IOD
- DICOM Part 6: Data Dictionary for element definitions
- :mod:`~pyrt_dicom.validation.dicom_compliance` - IOD compliance validation
- :doc:`~pyrt_dicom.docs.research.pymedphys_plan_patterns` - PyMedPhys integration patterns
"""

from typing import Dict, Any, Optional, List, Tuple, Union
import pydicom
from pydicom.dataset import Dataset
from pydicom.sequence import Sequence
from pydicom.uid import RTPlanStorage
import numpy as np
from datetime import datetime

from ..utils.exceptions import TemplateError
from ..uid_generation.generators import DefaultUIDGenerator


class RTPlanTemplate:
    """DICOM RT Plan IOD template implementation.

    Creates properly structured DICOM datasets conforming to the RT Plan IOD
    specification (DICOM PS 3.3 C.8.8.16). Incorporates proven patterns from
    PyMedPhys plan handling while providing enhanced API for broader treatment
    planning system compatibility.

    Clinical Notes:
        The RT Plan IOD is critical for radiotherapy workflows as it stores
        treatment beam configurations, prescription dose information, and
        fractionation schemes. Key clinical requirements include:

        - Beam geometry definition for accurate treatment delivery
        - Prescription dose specification for proper dose calculation
        - Fraction group organization for hypofractionated and SBRT treatments
        - Machine parameter specification for beam delivery verification
        - Referenced dose and structure relationships for plan evaluation
        - Control point sequences for IMRT/VMAT delivery

        Plan data must maintain clinical accuracy and consistency with
        dose calculations to support treatment delivery verification,
        plan comparison, and regulatory dose reporting requirements.

    Attributes:
        SOP_CLASS_UID: RT Plan Storage SOP Class UID (1.2.840.10008.*******.1.481.5)
        REQUIRED_MODULES: List of mandatory DICOM modules for RT Plan IOD
        OPTIONAL_MODULES: List of optional modules that enhance clinical functionality
        BEAM_TYPES: Valid beam type classifications
        DELIVERY_TYPES: Valid radiation delivery type classifications
        TREATMENT_INTENTS: Valid treatment intent classifications
    """

    # RT Plan Storage SOP Class UID from DICOM Part 6
    SOP_CLASS_UID = RTPlanStorage

    # Required modules per DICOM Part 3 C.8.8.16
    REQUIRED_MODULES = [
        "Patient",
        "General Study",
        "RT Series",
        "Frame of Reference", 
        "General Equipment",
        "RT General Plan",
        "RT Prescription",
        "RT Tolerance Tables",
        "RT Patient Setup",
        "RT Fraction Scheme",
        "RT Beams",
        "SOP Common",
    ]

    # Optional modules that enhance clinical functionality
    OPTIONAL_MODULES = [
        "Clinical Trial Subject",
        "Clinical Trial Study",
        "Clinical Trial Series",
        "Approval",
        "RT General Machine",
        "RT General Wedge",
        "RT General Block",
        "RT General Compensator",
        "RT ROI Observations",
        "RT DVH",
    ]

    # Valid beam types per DICOM standard
    BEAM_TYPES = [
        'STATIC',       # Static beam (3D-CRT)
        'DYNAMIC',      # Dynamic beam (IMRT/VMAT)
        'SETUP',        # Setup/imaging beam
    ]

    # Valid radiation types per DICOM standard
    RADIATION_TYPES = [
        'PHOTON',       # X-ray photon beam
        'ELECTRON',     # Electron beam
        'PROTON',       # Proton beam
        'NEUTRON',      # Neutron beam
        'ION',          # Heavy ion beam
    ]

    # Valid treatment delivery types
    DELIVERY_TYPES = [
        'TREATMENT',    # Treatment beam
        'SETUP',        # Setup/verification beam
        'CONTINUATION', # Beam continuation
    ]

    # Valid treatment intents
    TREATMENT_INTENTS = [
        'CURATIVE',     # Curative treatment
        'PALLIATIVE',   # Palliative treatment
        'VERIFICATION', # Verification/QA
        'MACHINE_QA',   # Machine QA
    ]

    @classmethod
    def create_dataset(
        cls,
        prescription_dose: float,
        fractions: int,
        beam_configurations: List[Dict[str, Any]],
        plan_name: str = "RT Plan",
        plan_description: str = "",
        treatment_intent: str = "CURATIVE",
        referenced_structure_set_sop_instance_uid: Optional[str] = None,
        referenced_dose_sop_instance_uid: Optional[str] = None,
        **kwargs,
    ) -> Dataset:
        """Create RT Plan dataset with proper IOD structure.

        Args:
            prescription_dose: Total prescribed dose in cGy for the complete treatment
            fractions: Number of treatment fractions
            beam_configurations: List of beam configuration dictionaries, each containing:
                - name: Beam name (string)
                - energy: Beam energy in MV for photons or MeV for electrons
                - gantry_angle: Gantry rotation angle in degrees (0-359.9)
                - collimator_angle: Collimator rotation angle in degrees (0-359.9)
                - couch_angle: Treatment couch rotation angle in degrees (0-359.9)
                - dose_weight: Relative beam weight (0.0-1.0, sum should equal 1.0)
                - beam_type: Beam type ('STATIC' or 'DYNAMIC')
                - radiation_type: Radiation type ('PHOTON', 'ELECTRON', etc.)
            plan_name: Descriptive name for the treatment plan
            plan_description: Optional detailed description of the plan
            treatment_intent: Treatment intent classification ('CURATIVE', 'PALLIATIVE', etc.)
            referenced_structure_set_sop_instance_uid: SOP Instance UID of related RT Structure Set
            referenced_dose_sop_instance_uid: SOP Instance UID of related RT Dose
            **kwargs: Additional DICOM elements to include in dataset

        Returns:
            Complete DICOM dataset conforming to RT Plan IOD specification

        Raises:
            TemplateError: If plan parameters are invalid, beam configurations are
                inconsistent, or required DICOM elements cannot be created

        Clinical Notes:
            The RT Plan template ensures proper clinical workflow integration:

            **Prescription Management**: Prescription dose is distributed across
            beam configurations based on relative weights. Total beam weights
            should sum to 1.0 for proper dose calculation.

            **Beam Configuration**: Each beam must specify geometry (angles),
            energy, and delivery parameters for accurate treatment setup and
            delivery verification.

            **Fraction Scheme**: The plan organizes prescription dose delivery
            across the specified number of fractions, supporting conventional
            fractionation (1.8-2.0 Gy/fx) and hypofractionation (>2.0 Gy/fx).

            **Clinical References**: Links to RT Structure Set and RT Dose objects
            maintain spatial and dosimetric consistency across the treatment plan.

        Examples:
            Create basic 3-field conformal plan:

            >>> beam_configs = [
            ...     {
            ...         'name': 'AP', 'energy': 18, 'gantry_angle': 0,
            ...         'collimator_angle': 0, 'couch_angle': 0, 'dose_weight': 0.33,
            ...         'beam_type': 'STATIC', 'radiation_type': 'PHOTON'
            ...     },
            ...     {
            ...         'name': 'RPO', 'energy': 18, 'gantry_angle': 120,
            ...         'collimator_angle': 0, 'couch_angle': 0, 'dose_weight': 0.33,
            ...         'beam_type': 'STATIC', 'radiation_type': 'PHOTON'
            ...     },
            ...     {
            ...         'name': 'LPO', 'energy': 18, 'gantry_angle': 240,
            ...         'collimator_angle': 0, 'couch_angle': 0, 'dose_weight': 0.34,
            ...         'beam_type': 'STATIC', 'radiation_type': 'PHOTON'
            ...     }
            ... ]
            >>> dataset = RTPlanTemplate.create_dataset(
            ...     prescription_dose=7000,  # 70 Gy in cGy
            ...     fractions=35,
            ...     beam_configurations=beam_configs,
            ...     plan_name="Prostate IMRT"
            ... )
            >>> print(dataset.SOPClassUID)
            1.2.840.10008.*******.1.481.5

            Create SBRT plan with setup beam:

            >>> sbrt_beams = [
            ...     {
            ...         'name': 'Setup_kV', 'energy': 6, 'gantry_angle': 0,
            ...         'collimator_angle': 0, 'couch_angle': 0, 'dose_weight': 0.0,
            ...         'beam_type': 'SETUP', 'radiation_type': 'PHOTON'
            ...     },
            ...     {
            ...         'name': 'Arc_CW', 'energy': 10, 'gantry_angle': 181,
            ...         'collimator_angle': 30, 'couch_angle': 0, 'dose_weight': 0.5,
            ...         'beam_type': 'DYNAMIC', 'radiation_type': 'PHOTON'
            ...     },
            ...     {
            ...         'name': 'Arc_CCW', 'energy': 10, 'gantry_angle': 179,
            ...         'collimator_angle': 330, 'couch_angle': 0, 'dose_weight': 0.5,
            ...         'beam_type': 'DYNAMIC', 'radiation_type': 'PHOTON'
            ...     }
            ... ]
            >>> dataset = RTPlanTemplate.create_dataset(
            ...     prescription_dose=5000,  # 50 Gy in 5 fx SBRT
            ...     fractions=5,
            ...     beam_configurations=sbrt_beams,
            ...     plan_name="Lung SBRT",
            ...     treatment_intent="CURATIVE"
            ... )
        """
        # Validate input parameters
        cls._validate_plan_parameters(
            prescription_dose, fractions, beam_configurations, treatment_intent
        )

        # Create base dataset
        dataset = Dataset()

        # SOP Common Module (C.12.1) - Required
        dataset.SOPClassUID = cls.SOP_CLASS_UID
        uid_generator = DefaultUIDGenerator.create_default_generator()
        dataset.SOPInstanceUID = uid_generator.generate_sop_instance_uid()

        # RT Series Module (C.8.8.1) - Required for RT objects
        dataset.Modality = "RTPLAN"
        dataset.SeriesDescription = kwargs.get("SeriesDescription", "RT Plan")

        # General Equipment Module (C.7.5.1) - Required
        dataset.Manufacturer = kwargs.get("Manufacturer", "pyrt-dicom")
        dataset.ManufacturerModelName = kwargs.get("ManufacturerModelName", "pyrt-dicom DICOM Creator")
        dataset.SoftwareVersions = kwargs.get("SoftwareVersions", "1.0.0")

        # RT General Plan Module (C.8.8.14) - Required
        dataset.RTPlanLabel = plan_name[:16]  # Limit to 16 characters per DICOM standard
        dataset.RTPlanName = plan_name
        dataset.RTPlanDescription = plan_description
        dataset.RTPlanDate = datetime.now().strftime("%Y%m%d")
        dataset.RTPlanTime = datetime.now().strftime("%H%M%S.%f")[:10]
        dataset.TreatmentProtocols = ""
        dataset.PlanIntent = treatment_intent
        dataset.TreatmentSites = ""
        dataset.RTPlanGeometry = "PATIENT"  # Plan coordinate system

        # RT Prescription Module (C.8.8.15) - Required
        dataset.DoseReferenceSequence = cls._create_dose_reference_sequence(
            prescription_dose, fractions
        )

        # RT Fraction Scheme Module (C.8.8.12) - Required
        dataset.FractionGroupSequence = cls._create_fraction_group_sequence(
            prescription_dose, fractions, beam_configurations
        )

        # RT Beams Module (C.8.8.11) - Required
        dataset.BeamSequence = cls._create_beam_sequence(beam_configurations)

        # RT Patient Setup Module (C.8.8.13) - Required
        dataset.PatientSetupSequence = cls._create_patient_setup_sequence()

        # RT Tolerance Tables Module (C.8.8.20) - Required but can be empty
        dataset.ToleranceTableSequence = Sequence()

        # Referenced Structure Set
        if referenced_structure_set_sop_instance_uid:
            dataset.ReferencedStructureSetSequence = cls._create_referenced_structure_set_sequence(
                referenced_structure_set_sop_instance_uid
            )

        # Referenced Dose
        if referenced_dose_sop_instance_uid:
            dataset.ReferencedDoseSequence = cls._create_referenced_dose_sequence(
                referenced_dose_sop_instance_uid
            )

        # Add any additional elements from kwargs
        for key, value in kwargs.items():
            if key not in [
                "SeriesDescription",
                "Manufacturer",
                "ManufacturerModelName", 
                "SoftwareVersions",
            ]:
                try:
                    setattr(dataset, key, value)
                except Exception:
                    # Skip invalid DICOM elements
                    continue

        return dataset

    @classmethod
    def _validate_plan_parameters(
        cls,
        prescription_dose: float,
        fractions: int,
        beam_configurations: List[Dict[str, Any]],
        treatment_intent: str,
    ) -> None:
        """Validate RT plan parameters for clinical consistency.
        
        Args:
            prescription_dose: Prescribed dose in cGy
            fractions: Number of fractions
            beam_configurations: List of beam configuration dictionaries
            treatment_intent: Treatment intent classification
            
        Raises:
            TemplateError: If any parameters are invalid
        """
        # Validate prescription dose
        if prescription_dose <= 0:
            raise TemplateError(
                f"prescription_dose must be positive, got {prescription_dose}",
                modality="RTPLAN",
                suggestions=[
                    "Prescription dose should be in cGy (e.g., 7000 cGy = 70 Gy)",
                    "Check treatment protocol for correct prescription dose",
                    "Typical ranges: 4500-8000 cGy for conventional fractionation",
                ],
            )

        if prescription_dose > 10000:  # > 100 Gy is unusual
            raise TemplateError(
                f"prescription_dose unusually high: {prescription_dose} cGy",
                modality="RTPLAN",
                suggestions=[
                    "Verify prescription dose units (should be cGy, not Gy)",
                    "Check for stereotactic/SBRT protocols if dose is correct",
                    "Consult treatment protocol for appropriate dose levels",
                ],
            )

        # Validate fractions
        if fractions <= 0:
            raise TemplateError(
                f"fractions must be positive integer, got {fractions}",
                modality="RTPLAN",
                suggestions=[
                    "Number of fractions should be a positive integer",
                    "Typical ranges: 1-45 fractions depending on protocol",
                    "Check treatment protocol for correct fractionation scheme",
                ],
            )

        if fractions > 50:  # Unusually high number of fractions
            raise TemplateError(
                f"fractions unusually high: {fractions}",
                modality="RTPLAN",
                suggestions=[
                    "Verify fractionation scheme per treatment protocol",
                    "Consider if hyperfractionation is intended",
                    "Check for data entry errors in fraction count",
                ],
            )

        # Validate treatment intent
        if treatment_intent not in cls.TREATMENT_INTENTS:
            raise TemplateError(
                f"Invalid treatment_intent: '{treatment_intent}', must be one of {cls.TREATMENT_INTENTS}",
                modality="RTPLAN",
                suggestions=[
                    "Use 'CURATIVE' for definitive treatment plans",
                    "Use 'PALLIATIVE' for symptom management plans",
                    "Use 'VERIFICATION' for QA and verification plans",
                ],
                clinical_context={
                    "provided_intent": treatment_intent,
                    "valid_intents": cls.TREATMENT_INTENTS,
                },
            )

        # Validate beam configurations
        if not beam_configurations:
            raise TemplateError(
                "beam_configurations cannot be empty",
                modality="RTPLAN",
                suggestions=[
                    "Provide at least one beam configuration",
                    "Each beam should specify geometry and delivery parameters",
                    "Include setup beams if used for patient positioning",
                ],
            )

        # Validate individual beam configurations
        total_dose_weight = 0.0
        for i, beam_config in enumerate(beam_configurations):
            cls._validate_beam_configuration(beam_config, i)
            if beam_config.get('beam_type', 'STATIC') != 'SETUP':
                total_dose_weight += beam_config.get('dose_weight', 0.0)

        # Check total dose weight (should be approximately 1.0 for treatment beams)
        if abs(total_dose_weight - 1.0) > 0.01:  # Allow 1% tolerance
            raise TemplateError(
                f"Total beam dose weights sum to {total_dose_weight:.3f}, should be 1.0",
                modality="RTPLAN",
                suggestions=[
                    "Adjust beam dose weights to sum to 1.0",
                    "Setup beams should have dose_weight=0.0",
                    "Treatment beams should distribute dose proportionally",
                ],
                clinical_context={
                    "total_weight": total_dose_weight,
                    "expected_weight": 1.0,
                    "tolerance": 0.01,
                },
            )

    @classmethod
    def _validate_beam_configuration(cls, beam_config: Dict[str, Any], beam_index: int) -> None:
        """Validate individual beam configuration parameters.
        
        Args:
            beam_config: Beam configuration dictionary
            beam_index: Index of beam for error reporting
            
        Raises:
            TemplateError: If beam configuration is invalid
        """
        required_fields = ['name', 'energy', 'gantry_angle', 'dose_weight']
        for field in required_fields:
            if field not in beam_config:
                raise TemplateError(
                    f"Beam {beam_index} missing required field: {field}",
                    modality="RTPLAN",
                    suggestions=[
                        f"Add {field} to beam configuration dictionary",
                        "Required fields: name, energy, gantry_angle, dose_weight",
                        "Optional fields: collimator_angle, couch_angle, beam_type, radiation_type",
                    ],
                )

        # Validate beam name
        beam_name = beam_config['name']
        if not isinstance(beam_name, str) or not beam_name.strip():
            raise TemplateError(
                f"Beam {beam_index} name must be non-empty string",
                modality="RTPLAN",
                suggestions=[
                    "Use descriptive beam names (e.g., 'AP', 'PA', 'LPO')",
                    "Beam names should be unique within the plan",
                    "Avoid special characters that might cause DICOM issues",
                ],
            )

        # Validate beam energy
        energy = beam_config['energy']
        if not isinstance(energy, (int, float)) or energy <= 0:
            raise TemplateError(
                f"Beam {beam_index} energy must be positive number, got {energy}",
                modality="RTPLAN",
                suggestions=[
                    "Energy should be in MV for photon beams (e.g., 6, 10, 18)",
                    "Energy should be in MeV for electron beams (e.g., 6, 9, 12)",
                    "Check linac specifications for available energies",
                ],
            )

        # Validate gantry angle
        gantry_angle = beam_config['gantry_angle']
        if not isinstance(gantry_angle, (int, float)):
            raise TemplateError(
                f"Beam {beam_index} gantry_angle must be numeric, got {type(gantry_angle)}",
                modality="RTPLAN",
            )
        
        if not (0 <= gantry_angle < 360):
            raise TemplateError(
                f"Beam {beam_index} gantry_angle must be 0-359.9 degrees, got {gantry_angle}",
                modality="RTPLAN",
                suggestions=[
                    "Gantry angles should be in degrees (0-359.9)",
                    "0° = superior, 90° = left lateral, 180° = inferior, 270° = right lateral",
                    "Use IEC 61217 coordinate system conventions",
                ],
            )

        # Validate dose weight
        dose_weight = beam_config['dose_weight']
        if not isinstance(dose_weight, (int, float)):
            raise TemplateError(
                f"Beam {beam_index} dose_weight must be numeric, got {type(dose_weight)}",
                modality="RTPLAN",
            )
            
        if not (0 <= dose_weight <= 1):
            raise TemplateError(
                f"Beam {beam_index} dose_weight must be 0.0-1.0, got {dose_weight}",
                modality="RTPLAN",
                suggestions=[
                    "Dose weights are relative beam contributions (0.0-1.0)",
                    "Total weights should sum to 1.0 for treatment beams",
                    "Setup beams should have dose_weight=0.0",
                ],
            )

        # Validate optional parameters
        if 'collimator_angle' in beam_config:
            collimator_angle = beam_config['collimator_angle']
            if not isinstance(collimator_angle, (int, float)) or not (0 <= collimator_angle < 360):
                raise TemplateError(
                    f"Beam {beam_index} collimator_angle must be 0-359.9 degrees, got {collimator_angle}",
                    modality="RTPLAN",
                )

        if 'couch_angle' in beam_config:
            couch_angle = beam_config['couch_angle']
            if not isinstance(couch_angle, (int, float)) or not (0 <= couch_angle < 360):
                raise TemplateError(
                    f"Beam {beam_index} couch_angle must be 0-359.9 degrees, got {couch_angle}",
                    modality="RTPLAN",
                )

        # Validate beam type
        beam_type = beam_config.get('beam_type', 'STATIC')
        if beam_type not in cls.BEAM_TYPES:
            raise TemplateError(
                f"Beam {beam_index} invalid beam_type: '{beam_type}', must be one of {cls.BEAM_TYPES}",
                modality="RTPLAN",
                clinical_context={
                    "provided_type": beam_type,
                    "valid_types": cls.BEAM_TYPES,
                },
            )

        # Validate radiation type
        radiation_type = beam_config.get('radiation_type', 'PHOTON')
        if radiation_type not in cls.RADIATION_TYPES:
            raise TemplateError(
                f"Beam {beam_index} invalid radiation_type: '{radiation_type}', must be one of {cls.RADIATION_TYPES}",
                modality="RTPLAN",
                clinical_context={
                    "provided_type": radiation_type,
                    "valid_types": cls.RADIATION_TYPES,
                },
            )

    @classmethod
    def _create_dose_reference_sequence(cls, prescription_dose: float, fractions: int) -> Sequence:
        """Create dose reference sequence for prescription information.
        
        Args:
            prescription_dose: Total prescribed dose in cGy
            fractions: Number of fractions
            
        Returns:
            DICOM sequence containing dose reference information
        """
        dose_ref_seq = Sequence()
        dose_ref_item = Dataset()
        
        dose_ref_item.DoseReferenceNumber = "1"
        dose_ref_item.DoseReferenceUID = DefaultUIDGenerator.create_default_generator().generate_uid()
        dose_ref_item.DoseReferenceStructureType = "COORDINATES"
        dose_ref_item.DoseReferenceDescription = "Prescription Dose Reference"
        dose_ref_item.DoseReferenceType = "TARGET"
        dose_ref_item.TargetPrescriptionDose = float(prescription_dose)
        dose_ref_item.TargetMaximumDose = float(prescription_dose * 1.1)  # 110% typical maximum
        dose_ref_item.TargetMinimumDose = float(prescription_dose * 0.95)  # 95% typical minimum
        
        dose_ref_seq.append(dose_ref_item)
        return dose_ref_seq

    @classmethod
    def _create_fraction_group_sequence(
        cls, 
        prescription_dose: float, 
        fractions: int, 
        beam_configurations: List[Dict[str, Any]]
    ) -> Sequence:
        """Create fraction group sequence for fractionation scheme.
        
        Args:
            prescription_dose: Total prescribed dose in cGy
            fractions: Number of fractions
            beam_configurations: List of beam configurations
            
        Returns:
            DICOM sequence containing fraction group information
        """
        fraction_group_seq = Sequence()
        fraction_group_item = Dataset()
        
        fraction_group_item.FractionGroupNumber = "1"
        fraction_group_item.FractionGroupDescription = "Primary Fraction Group"
        fraction_group_item.NumberOfFractionsPlanned = str(fractions)
        fraction_group_item.NumberOfFractionPatternDigitsPerDay = "1"
        fraction_group_item.RepeatFractionCycleLength = "1"
        fraction_group_item.FractionPattern = "1"
        
        # Create referenced beam sequence
        ref_beam_seq = Sequence()
        treatment_beam_number = 1
        
        for beam_config in beam_configurations:
            # Only include treatment beams in fraction group, not setup beams
            if beam_config.get('beam_type', 'STATIC') != 'SETUP':
                ref_beam_item = Dataset()
                ref_beam_item.ReferencedBeamNumber = str(treatment_beam_number)
                ref_beam_item.BeamDose = float(prescription_dose * beam_config['dose_weight'])
                ref_beam_item.BeamMeterset = float(beam_config.get('meterset', 100.0))
                ref_beam_seq.append(ref_beam_item)
                treatment_beam_number += 1
        
        fraction_group_item.ReferencedBeamSequence = ref_beam_seq
        fraction_group_seq.append(fraction_group_item)
        return fraction_group_seq

    @classmethod
    def _create_beam_sequence(cls, beam_configurations: List[Dict[str, Any]]) -> Sequence:
        """Create beam sequence for treatment beam definitions.
        
        Args:
            beam_configurations: List of beam configuration dictionaries
            
        Returns:
            DICOM sequence containing beam definitions
        """
        beam_seq = Sequence()
        
        for i, beam_config in enumerate(beam_configurations, 1):
            beam_item = Dataset()
            
            # Basic beam identification
            beam_item.BeamNumber = str(i)
            beam_item.BeamName = beam_config['name']
            beam_item.BeamDescription = beam_config.get('description', f"Beam {i}")
            beam_item.BeamType = beam_config.get('beam_type', 'STATIC')
            beam_item.RadiationType = beam_config.get('radiation_type', 'PHOTON')
            beam_item.HighDoseTechniqueType = ""
            beam_item.TreatmentMachineName = beam_config.get('machine_name', "")
            
            # Beam geometry
            beam_item.PrimaryDosimeterUnit = "MU"
            beam_item.SourceAxisDistance = float(beam_config.get('sad', 1000.0))  # 100 cm default
            beam_item.BeamLimitingDeviceSequence = cls._create_beam_limiting_device_sequence()
            
            # Treatment delivery parameters
            beam_item.FinalCumulativeMetersetWeight = float(beam_config.get('meterset_weight', 1.0))
            beam_item.NumberOfWedges = "0"
            beam_item.NumberOfCompensators = "0" 
            beam_item.NumberOfBoli = "0"
            beam_item.NumberOfBlocks = "0"
            
            # Control point sequence (simplified for basic plan)
            beam_item.NumberOfControlPoints = "2"  # Start and end points
            beam_item.ControlPointSequence = cls._create_control_point_sequence(beam_config)
            
            beam_seq.append(beam_item)
        
        return beam_seq

    @classmethod
    def _create_beam_limiting_device_sequence(cls) -> Sequence:
        """Create beam limiting device sequence for basic collimator definition.
        
        Returns:
            DICOM sequence containing beam limiting device information
        """
        bld_seq = Sequence()
        
        # Asymmetric X (MLCX) jaws
        mlcx_item = Dataset()
        mlcx_item.RTBeamLimitingDeviceType = "MLCX"
        mlcx_item.SourceToBeamLimitingDeviceDistance = 500.0  # 50 cm typical
        mlcx_item.NumberOfLeafJawPairs = "1"
        bld_seq.append(mlcx_item)
        
        # Asymmetric Y jaws
        mlcy_item = Dataset()
        mlcy_item.RTBeamLimitingDeviceType = "ASYMY"
        mlcy_item.SourceToBeamLimitingDeviceDistance = 500.0  # 50 cm typical
        mlcy_item.NumberOfLeafJawPairs = "1"
        bld_seq.append(mlcy_item)
        
        return bld_seq

    @classmethod
    def _create_control_point_sequence(cls, beam_config: Dict[str, Any]) -> Sequence:
        """Create control point sequence for beam delivery parameters.
        
        Args:
            beam_config: Beam configuration dictionary
            
        Returns:
            DICOM sequence containing control point information
        """
        cp_seq = Sequence()
        
        # Start control point
        start_cp = Dataset()
        start_cp.ControlPointIndex = "0"
        start_cp.CumulativeMetersetWeight = 0.0
        start_cp.GantryAngle = float(beam_config['gantry_angle'])
        start_cp.GantryRotationDirection = "NONE"
        start_cp.BeamLimitingDeviceAngle = float(beam_config.get('collimator_angle', 0.0))
        start_cp.BeamLimitingDeviceRotationDirection = "NONE"
        start_cp.PatientSupportAngle = float(beam_config.get('couch_angle', 0.0))
        start_cp.PatientSupportRotationDirection = "NONE"
        start_cp.TableTopEccentricAngle = 0.0
        start_cp.TableTopEccentricRotationDirection = "NONE"
        start_cp.TableTopVerticalPosition = 0.0
        start_cp.TableTopLongitudinalPosition = 0.0
        start_cp.TableTopLateralPosition = 0.0
        start_cp.IsocenterPosition = [0.0, 0.0, 0.0]  # Will be set from reference
        start_cp.SurfaceEntryPoint = 0.0
        start_cp.SourceToSurfaceDistance = 1000.0  # 100 cm default
        start_cp.BeamLimitingDevicePositionSequence = cls._create_beam_limiting_device_position_sequence()
        cp_seq.append(start_cp)
        
        # End control point (same as start for static beams)
        end_cp = Dataset()
        end_cp.ControlPointIndex = "1"
        end_cp.CumulativeMetersetWeight = float(beam_config.get('meterset_weight', 1.0))
        cp_seq.append(end_cp)
        
        return cp_seq

    @classmethod
    def _create_beam_limiting_device_position_sequence(cls) -> Sequence:
        """Create beam limiting device position sequence for jaw positions.
        
        Returns:
            DICOM sequence containing beam limiting device positions
        """
        bldp_seq = Sequence()
        
        # X jaw positions (symmetric 10x10 cm field default)
        x_jaw_item = Dataset()
        x_jaw_item.RTBeamLimitingDeviceType = "MLCX"
        x_jaw_item.LeafJawPositions = [-50.0, 50.0]  # -5cm to +5cm
        bldp_seq.append(x_jaw_item)
        
        # Y jaw positions
        y_jaw_item = Dataset()
        y_jaw_item.RTBeamLimitingDeviceType = "ASYMY"
        y_jaw_item.LeafJawPositions = [-50.0, 50.0]  # -5cm to +5cm
        bldp_seq.append(y_jaw_item)
        
        return bldp_seq

    @classmethod
    def _create_patient_setup_sequence(cls) -> Sequence:
        """Create patient setup sequence for positioning information.
        
        Returns:
            DICOM sequence containing patient setup information
        """
        setup_seq = Sequence()
        setup_item = Dataset()
        
        setup_item.PatientSetupNumber = "1"
        setup_item.PatientSetupLabel = "Default Setup"
        setup_item.PatientPosition = "HFS"  # Head First Supine (most common)
        setup_item.SetupTechnique = "ISOCENTRIC"
        setup_item.SetupDeviceSequence = Sequence()  # Empty for basic plan
        setup_item.ShieldingDeviceSequence = Sequence()  # Empty for basic plan
        
        setup_seq.append(setup_item)
        return setup_seq

    @classmethod
    def _create_referenced_structure_set_sequence(cls, structure_set_sop_instance_uid: str) -> Sequence:
        """Create referenced structure set sequence.
        
        Args:
            structure_set_sop_instance_uid: SOP Instance UID of RT Structure Set
            
        Returns:
            DICOM sequence referencing the structure set
        """
        ref_struct_seq = Sequence()
        ref_struct_item = Dataset()
        ref_struct_item.ReferencedSOPClassUID = "1.2.840.10008.*******.1.481.3"  # RT Structure Set Storage
        ref_struct_item.ReferencedSOPInstanceUID = structure_set_sop_instance_uid
        ref_struct_seq.append(ref_struct_item)
        return ref_struct_seq

    @classmethod
    def _create_referenced_dose_sequence(cls, dose_sop_instance_uid: str) -> Sequence:
        """Create referenced dose sequence.
        
        Args:
            dose_sop_instance_uid: SOP Instance UID of RT Dose
            
        Returns:
            DICOM sequence referencing the dose
        """
        ref_dose_seq = Sequence()
        ref_dose_item = Dataset()
        ref_dose_item.ReferencedSOPClassUID = "1.2.840.10008.*******.1.481.2"  # RT Dose Storage
        ref_dose_item.ReferencedSOPInstanceUID = dose_sop_instance_uid
        ref_dose_seq.append(ref_dose_item)
        return ref_dose_seq

    @classmethod
    def validate_compliance(cls, dataset: Dataset) -> List[str]:
        """Validate dataset compliance with RT Plan IOD.

        Args:
            dataset: DICOM dataset to validate

        Returns:
            List of validation error messages. Empty list indicates compliance

        Clinical Notes:
            Validation ensures that created RT Plan objects will load properly
            in treatment planning systems and maintain proper relationships
            with RT structures and dose objects. Checks include DICOM IOD
            compliance, beam parameter validity, and fraction scheme integrity.
        """
        errors = []

        # Check SOP Class UID
        if not hasattr(dataset, "SOPClassUID"):
            errors.append("Missing required SOPClassUID")
        elif dataset.SOPClassUID != cls.SOP_CLASS_UID:
            errors.append(
                f"Invalid SOPClassUID: expected {cls.SOP_CLASS_UID}, got {dataset.SOPClassUID}"
            )

        # Check required RT Plan elements
        required_elements = [
            "Modality",
            "RTPlanLabel",
            "RTPlanName", 
            "RTPlanDate",
            "RTPlanTime",
            "PlanIntent",
            "RTPlanGeometry",
            "DoseReferenceSequence",
            "FractionGroupSequence",
            "BeamSequence",
            "PatientSetupSequence",
        ]

        for element in required_elements:
            if not hasattr(dataset, element):
                errors.append(f"Missing required element: {element}")

        # Validate modality
        if hasattr(dataset, "Modality") and dataset.Modality != "RTPLAN":
            errors.append(f"Invalid modality for RT Plan: {dataset.Modality}")

        # Validate plan intent
        if hasattr(dataset, "PlanIntent") and dataset.PlanIntent not in cls.TREATMENT_INTENTS:
            errors.append(f"Invalid PlanIntent: {dataset.PlanIntent}")

        # Validate beam sequence consistency
        if hasattr(dataset, "BeamSequence"):
            beam_numbers = set()
            for beam in dataset.BeamSequence:
                if hasattr(beam, "BeamNumber"):
                    if beam.BeamNumber in beam_numbers:
                        errors.append(f"Duplicate BeamNumber: {beam.BeamNumber}")
                    beam_numbers.add(beam.BeamNumber)
                else:
                    errors.append("Beam missing BeamNumber")

                # Validate required beam elements
                required_beam_elements = ["BeamName", "BeamType", "RadiationType"]
                for element in required_beam_elements:
                    if not hasattr(beam, element):
                        errors.append(f"Beam {beam.BeamNumber if hasattr(beam, 'BeamNumber') else '?'} missing {element}")

        # Validate fraction group consistency
        if hasattr(dataset, "FractionGroupSequence"):
            for fraction_group in dataset.FractionGroupSequence:
                if hasattr(fraction_group, "ReferencedBeamSequence"):
                    for ref_beam in fraction_group.ReferencedBeamSequence:
                        if hasattr(ref_beam, "ReferencedBeamNumber"):
                            ref_beam_num = ref_beam.ReferencedBeamNumber
                            if hasattr(dataset, "BeamSequence"):
                                beam_numbers_in_beam_seq = [b.BeamNumber for b in dataset.BeamSequence if hasattr(b, "BeamNumber")]
                                if ref_beam_num not in beam_numbers_in_beam_seq:
                                    errors.append(f"Referenced beam {ref_beam_num} not found in BeamSequence")

        return errors