"""
Tests for RT Plan IOD Template Implementation.

This module tests the RTPlanTemplate class for DICOM compliance, parameter validation,
and clinical safety checks. Ensures that created RT Plan datasets conform to DICOM
Part 3 Section C.8.8.16 requirements and maintain compatibility with treatment
planning systems.
"""

import pytest
import pydicom
from pydicom.dataset import Dataset
from pydicom.sequence import Sequence
from pydicom.uid import RTPlanStorage
import numpy as np

from pyrt_dicom.templates.plan_template import RTPlanTemplate
from pyrt_dicom.utils.exceptions import TemplateError


class TestRTPlanTemplate:
    """Test suite for RT Plan IOD template."""

    def test_class_constants(self):
        """Test that class constants are properly defined."""
        # Test SOP Class UID
        assert RTPlanTemplate.SOP_CLASS_UID == RTPlanStorage
        
        # Test required modules list
        assert "RT General Plan" in RTPlanTemplate.REQUIRED_MODULES
        assert "RT Prescription" in RTPlanTemplate.REQUIRED_MODULES
        assert "RT Beams" in RTPlanTemplate.REQUIRED_MODULES
        assert "RT Fraction Scheme" in RTPlanTemplate.REQUIRED_MODULES
        assert "Patient" in RTPlanTemplate.REQUIRED_MODULES
        
        # Test valid enumerations
        assert "STATIC" in RTPlanTemplate.BEAM_TYPES
        assert "DYNAMIC" in RTPlanTemplate.BEAM_TYPES
        assert "SETUP" in RTPlanTemplate.BEAM_TYPES
        
        assert "PHOTON" in RTPlanTemplate.RADIATION_TYPES
        assert "ELECTRON" in RTPlanTemplate.RADIATION_TYPES
        
        assert "CURATIVE" in RTPlanTemplate.TREATMENT_INTENTS
        assert "PALLIATIVE" in RTPlanTemplate.TREATMENT_INTENTS

    def test_create_basic_plan_dataset(self):
        """Test creation of basic RT Plan dataset."""
        beam_configs = [
            {
                'name': 'Test Beam',
                'energy': 6,
                'gantry_angle': 0,
                'dose_weight': 1.0,
                'beam_type': 'STATIC',
                'radiation_type': 'PHOTON'
            }
        ]
        
        dataset = RTPlanTemplate.create_dataset(
            prescription_dose=2000,  # 20 Gy
            fractions=10,
            beam_configurations=beam_configs
        )
        
        # Test basic DICOM elements
        assert dataset.SOPClassUID == RTPlanStorage
        assert hasattr(dataset, 'SOPInstanceUID')
        assert dataset.Modality == "RTPLAN"
        
        # Test RT Plan specific elements
        assert dataset.RTPlanLabel == "RT Plan"
        assert dataset.RTPlanName == "RT Plan"
        assert hasattr(dataset, 'RTPlanDate')
        assert hasattr(dataset, 'RTPlanTime')
        assert dataset.PlanIntent == "CURATIVE"
        
        # Test sequences are present
        assert hasattr(dataset, 'DoseReferenceSequence')
        assert hasattr(dataset, 'FractionGroupSequence')
        assert hasattr(dataset, 'BeamSequence')
        assert hasattr(dataset, 'PatientSetupSequence')

    def test_create_multi_beam_plan(self):
        """Test creation of plan with multiple beams."""
        beam_configs = [
            {
                'name': 'AP',
                'energy': 18,
                'gantry_angle': 0,
                'collimator_angle': 0,
                'couch_angle': 0,
                'dose_weight': 0.33,
                'beam_type': 'STATIC',
                'radiation_type': 'PHOTON'
            },
            {
                'name': 'RPO',
                'energy': 18,
                'gantry_angle': 120,
                'collimator_angle': 0,
                'couch_angle': 0,
                'dose_weight': 0.33,
                'beam_type': 'STATIC',
                'radiation_type': 'PHOTON'
            },
            {
                'name': 'LPO',
                'energy': 18,
                'gantry_angle': 240,
                'collimator_angle': 0,
                'couch_angle': 0,
                'dose_weight': 0.34,
                'beam_type': 'STATIC',
                'radiation_type': 'PHOTON'
            }
        ]
        
        dataset = RTPlanTemplate.create_dataset(
            prescription_dose=7000,  # 70 Gy
            fractions=35,
            beam_configurations=beam_configs,
            plan_name="Prostate 3D-CRT",
            treatment_intent="CURATIVE"
        )
        
        # Test plan identification
        assert dataset.RTPlanName == "Prostate 3D-CRT"
        assert dataset.PlanIntent == "CURATIVE"
        
        # Test beam sequence
        assert len(dataset.BeamSequence) == 3
        beam_names = [beam.BeamName for beam in dataset.BeamSequence]
        assert "AP" in beam_names
        assert "RPO" in beam_names
        assert "LPO" in beam_names
        
        # Test fraction group has all treatment beams
        fraction_group = dataset.FractionGroupSequence[0]
        assert len(fraction_group.ReferencedBeamSequence) == 3

    def test_create_plan_with_setup_beam(self):
        """Test creation of plan with setup and treatment beams."""
        beam_configs = [
            {
                'name': 'Setup_kV',
                'energy': 6,
                'gantry_angle': 0,
                'dose_weight': 0.0,
                'beam_type': 'SETUP',
                'radiation_type': 'PHOTON'
            },
            {
                'name': 'Treatment',
                'energy': 10,
                'gantry_angle': 180,
                'dose_weight': 1.0,
                'beam_type': 'STATIC',
                'radiation_type': 'PHOTON'
            }
        ]
        
        dataset = RTPlanTemplate.create_dataset(
            prescription_dose=5000,  # 50 Gy SBRT
            fractions=5,
            beam_configurations=beam_configs,
            plan_name="SBRT Plan"
        )
        
        # Test beam sequence includes both beams
        assert len(dataset.BeamSequence) == 2
        beam_types = [beam.BeamType for beam in dataset.BeamSequence]
        assert "SETUP" in beam_types
        assert "STATIC" in beam_types
        
        # Test fraction group only includes treatment beam
        fraction_group = dataset.FractionGroupSequence[0]
        assert len(fraction_group.ReferencedBeamSequence) == 1

    def test_referenced_objects(self):
        """Test creation of plan with referenced dose and structure set."""
        beam_configs = [
            {
                'name': 'Test Beam',
                'energy': 6,
                'gantry_angle': 0,
                'dose_weight': 1.0
            }
        ]
        
        dose_uid = "*******.*******.9.1"
        struct_uid = "*******.*******.9.2"
        
        dataset = RTPlanTemplate.create_dataset(
            prescription_dose=2000,
            fractions=10,
            beam_configurations=beam_configs,
            referenced_dose_sop_instance_uid=dose_uid,
            referenced_structure_set_sop_instance_uid=struct_uid
        )
        
        # Test referenced dose sequence
        assert hasattr(dataset, 'ReferencedDoseSequence')
        ref_dose = dataset.ReferencedDoseSequence[0]
        assert ref_dose.ReferencedSOPInstanceUID == dose_uid
        assert ref_dose.ReferencedSOPClassUID == "1.2.840.10008.*******.1.481.2"  # RT Dose Storage
        
        # Test referenced structure set sequence
        assert hasattr(dataset, 'ReferencedStructureSetSequence')
        ref_struct = dataset.ReferencedStructureSetSequence[0]
        assert ref_struct.ReferencedSOPInstanceUID == struct_uid
        assert ref_struct.ReferencedSOPClassUID == "1.2.840.10008.*******.1.481.3"  # RT Structure Set Storage

    def test_dose_reference_sequence(self):
        """Test dose reference sequence creation."""
        beam_configs = [{'name': 'Test', 'energy': 6, 'gantry_angle': 0, 'dose_weight': 1.0}]
        
        dataset = RTPlanTemplate.create_dataset(
            prescription_dose=6000,  # 60 Gy
            fractions=30,
            beam_configurations=beam_configs
        )
        
        dose_ref = dataset.DoseReferenceSequence[0]
        assert dose_ref.DoseReferenceNumber == "1"
        assert dose_ref.DoseReferenceType == "TARGET"
        assert dose_ref.TargetPrescriptionDose == 6000.0
        assert abs(float(dose_ref.TargetMaximumDose) - 6600.0) < 0.01  # 110% (allow floating point tolerance)
        assert abs(float(dose_ref.TargetMinimumDose) - 5700.0) < 0.01  # 95% (allow floating point tolerance)

    def test_beam_sequence_structure(self):
        """Test beam sequence internal structure."""
        beam_config = {
            'name': 'Test Beam',
            'energy': 18,
            'gantry_angle': 90,
            'collimator_angle': 15,
            'couch_angle': 0,
            'dose_weight': 1.0,
            'beam_type': 'STATIC',
            'radiation_type': 'PHOTON'
        }
        
        dataset = RTPlanTemplate.create_dataset(
            prescription_dose=2000,
            fractions=10,
            beam_configurations=[beam_config]
        )
        
        beam = dataset.BeamSequence[0]
        assert beam.BeamNumber == "1"
        assert beam.BeamName == "Test Beam"
        assert beam.BeamType == "STATIC"
        assert beam.RadiationType == "PHOTON"
        assert beam.NumberOfControlPoints == "2"
        
        # Test control point sequence
        assert len(beam.ControlPointSequence) == 2
        start_cp = beam.ControlPointSequence[0]
        assert start_cp.ControlPointIndex == "0"
        assert start_cp.CumulativeMetersetWeight == 0.0
        assert start_cp.GantryAngle == 90.0
        assert start_cp.BeamLimitingDeviceAngle == 15.0
        
        end_cp = beam.ControlPointSequence[1]
        assert end_cp.ControlPointIndex == "1"
        assert end_cp.CumulativeMetersetWeight == 1.0

    def test_validate_prescription_parameters(self):
        """Test validation of prescription parameters."""
        beam_configs = [{'name': 'Test', 'energy': 6, 'gantry_angle': 0, 'dose_weight': 1.0}]
        
        # Test negative prescription dose
        with pytest.raises(TemplateError, match="prescription_dose must be positive"):
            RTPlanTemplate.create_dataset(
                prescription_dose=-100,
                fractions=10,
                beam_configurations=beam_configs
            )
        
        # Test zero fractions
        with pytest.raises(TemplateError, match="fractions must be positive"):
            RTPlanTemplate.create_dataset(
                prescription_dose=2000,
                fractions=0,
                beam_configurations=beam_configs
            )
        
        # Test unusually high dose
        with pytest.raises(TemplateError, match="prescription_dose unusually high"):
            RTPlanTemplate.create_dataset(
                prescription_dose=15000,  # 150 Gy
                fractions=10,
                beam_configurations=beam_configs
            )

    def test_validate_beam_configurations(self):
        """Test validation of beam configuration parameters."""
        # Test empty beam configurations
        with pytest.raises(TemplateError, match="beam_configurations cannot be empty"):
            RTPlanTemplate.create_dataset(
                prescription_dose=2000,
                fractions=10,
                beam_configurations=[]
            )
        
        # Test missing required beam fields
        incomplete_beam = {'name': 'Test', 'energy': 6}  # Missing gantry_angle, dose_weight
        with pytest.raises(TemplateError, match="missing required field"):
            RTPlanTemplate.create_dataset(
                prescription_dose=2000,
                fractions=10,
                beam_configurations=[incomplete_beam]
            )
        
        # Test invalid gantry angle
        invalid_beam = {
            'name': 'Test',
            'energy': 6,
            'gantry_angle': 400,  # > 360
            'dose_weight': 1.0
        }
        with pytest.raises(TemplateError, match="gantry_angle must be 0-359.9 degrees"):
            RTPlanTemplate.create_dataset(
                prescription_dose=2000,
                fractions=10,
                beam_configurations=[invalid_beam]
            )
        
        # Test invalid dose weight
        invalid_weight_beam = {
            'name': 'Test',
            'energy': 6,
            'gantry_angle': 0,
            'dose_weight': 1.5  # > 1.0
        }
        with pytest.raises(TemplateError, match="dose_weight must be 0.0-1.0"):
            RTPlanTemplate.create_dataset(
                prescription_dose=2000,
                fractions=10,
                beam_configurations=[invalid_weight_beam]
            )

    def test_validate_dose_weight_sum(self):
        """Test validation of total dose weight sum."""
        # Test dose weights don't sum to 1.0
        beam_configs = [
            {'name': 'Beam1', 'energy': 6, 'gantry_angle': 0, 'dose_weight': 0.4},
            {'name': 'Beam2', 'energy': 6, 'gantry_angle': 180, 'dose_weight': 0.4}
            # Total = 0.8, should be 1.0
        ]
        
        with pytest.raises(TemplateError, match="Total beam dose weights sum to"):
            RTPlanTemplate.create_dataset(
                prescription_dose=2000,
                fractions=10,
                beam_configurations=beam_configs
            )

    def test_validate_treatment_intent(self):
        """Test validation of treatment intent parameter."""
        beam_configs = [{'name': 'Test', 'energy': 6, 'gantry_angle': 0, 'dose_weight': 1.0}]
        
        with pytest.raises(TemplateError, match="Invalid treatment_intent"):
            RTPlanTemplate.create_dataset(
                prescription_dose=2000,
                fractions=10,
                beam_configurations=beam_configs,
                treatment_intent="INVALID_INTENT"
            )

    def test_validate_beam_types_and_radiation_types(self):
        """Test validation of beam and radiation type parameters."""
        # Test invalid beam type
        invalid_beam_type = {
            'name': 'Test',
            'energy': 6,
            'gantry_angle': 0,
            'dose_weight': 1.0,
            'beam_type': 'INVALID_TYPE'
        }
        
        with pytest.raises(TemplateError, match="invalid beam_type"):
            RTPlanTemplate.create_dataset(
                prescription_dose=2000,
                fractions=10,
                beam_configurations=[invalid_beam_type]
            )
        
        # Test invalid radiation type
        invalid_radiation_type = {
            'name': 'Test',
            'energy': 6,
            'gantry_angle': 0,
            'dose_weight': 1.0,
            'radiation_type': 'INVALID_RADIATION'
        }
        
        with pytest.raises(TemplateError, match="invalid radiation_type"):
            RTPlanTemplate.create_dataset(
                prescription_dose=2000,
                fractions=10,
                beam_configurations=[invalid_radiation_type]
            )

    def test_validate_compliance(self):
        """Test DICOM compliance validation."""
        beam_configs = [{'name': 'Test', 'energy': 6, 'gantry_angle': 0, 'dose_weight': 1.0}]
        
        # Create valid dataset
        dataset = RTPlanTemplate.create_dataset(
            prescription_dose=2000,
            fractions=10,
            beam_configurations=beam_configs
        )
        
        # Test valid dataset has no errors
        errors = RTPlanTemplate.validate_compliance(dataset)
        assert len(errors) == 0
        
        # Test missing required element
        del dataset.RTPlanName
        errors = RTPlanTemplate.validate_compliance(dataset)
        assert any("Missing required element: RTPlanName" in error for error in errors)
        
        # Test invalid modality
        dataset.Modality = "INVALID"
        errors = RTPlanTemplate.validate_compliance(dataset)
        assert any("Invalid modality for RT Plan" in error for error in errors)

    def test_kwargs_handling(self):
        """Test handling of additional kwargs."""
        beam_configs = [{'name': 'Test', 'energy': 6, 'gantry_angle': 0, 'dose_weight': 1.0}]
        
        dataset = RTPlanTemplate.create_dataset(
            prescription_dose=2000,
            fractions=10,
            beam_configurations=beam_configs,
            custom_parameter="test_value",
            Manufacturer="Test Manufacturer",
            SeriesDescription="Custom Series"
        )
        
        # Test custom parameter is included (if valid DICOM)
        assert dataset.Manufacturer == "Test Manufacturer"
        assert dataset.SeriesDescription == "Custom Series"

    def test_patient_setup_sequence(self):
        """Test patient setup sequence creation."""
        beam_configs = [{'name': 'Test', 'energy': 6, 'gantry_angle': 0, 'dose_weight': 1.0}]
        
        dataset = RTPlanTemplate.create_dataset(
            prescription_dose=2000,
            fractions=10,
            beam_configurations=beam_configs
        )
        
        setup = dataset.PatientSetupSequence[0]
        assert setup.PatientSetupNumber == "1"
        assert setup.PatientPosition == "HFS"  # Head First Supine
        assert setup.SetupTechnique == "ISOCENTRIC"

    def test_tolerance_table_sequence(self):
        """Test tolerance table sequence creation."""
        beam_configs = [{'name': 'Test', 'energy': 6, 'gantry_angle': 0, 'dose_weight': 1.0}]
        
        dataset = RTPlanTemplate.create_dataset(
            prescription_dose=2000,
            fractions=10,
            beam_configurations=beam_configs
        )
        
        # Tolerance table sequence should be present but can be empty
        assert hasattr(dataset, 'ToleranceTableSequence')
        assert isinstance(dataset.ToleranceTableSequence, Sequence)

    def test_clinical_dose_ranges(self):
        """Test clinical dose range validation."""
        beam_configs = [{'name': 'Test', 'energy': 6, 'gantry_angle': 0, 'dose_weight': 1.0}]
        
        # Test typical conventional fractionation (should pass)
        dataset = RTPlanTemplate.create_dataset(
            prescription_dose=7000,  # 70 Gy
            fractions=35,  # 2 Gy/fx
            beam_configurations=beam_configs
        )
        assert dataset is not None
        
        # Test SBRT fractionation (should pass)
        dataset = RTPlanTemplate.create_dataset(
            prescription_dose=5000,  # 50 Gy
            fractions=5,   # 10 Gy/fx
            beam_configurations=beam_configs
        )
        assert dataset is not None