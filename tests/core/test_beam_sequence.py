"""
Tests for RT Plan beam sequence creation functionality.

This module provides comprehensive testing of the beam sequence creation logic
in the RTPlan class, covering beam configuration validation, DICOM sequence
generation, and clinical safety checks for treatment planning workflows.

Test Categories:
    - Beam configuration validation
    - DICOM beam sequence generation
    - Clinical parameter validation
    - Error handling and edge cases
    - Treatment planning system compatibility
"""

import pytest

from pyrt_dicom.core.rt_plan import RTPlan
from pyrt_dicom.utils.exceptions import DicomCreationError


class TestBeamSequenceCreation:
    """Test beam sequence creation functionality."""

    def test_create_beam_sequence_basic_static_beams(self, sample_beam_configs):
        """Test basic beam sequence creation with static beams."""
        rt_plan = RTPlan(patient_info={'PatientID': 'TEST001'})
        
        beam_sequence = rt_plan._create_beam_sequence(sample_beam_configs['static_3_field'])
        
        # Verify sequence structure
        assert len(beam_sequence) == 3
        assert all(hasattr(beam, 'BeamNumber') for beam in beam_sequence)
        assert all(hasattr(beam, 'BeamName') for beam in beam_sequence)
        assert all(hasattr(beam, 'BeamType') for beam in beam_sequence)
        
        # Verify beam names match configuration
        beam_names = [beam.BeamName for beam in beam_sequence]
        expected_names = [beam['name'] for beam in sample_beam_configs['static_3_field']]
        assert beam_names == expected_names

    def test_create_beam_sequence_with_setup_beam(self, sample_beam_configs):
        """Test beam sequence creation including setup beams."""
        rt_plan = RTPlan(patient_info={'PatientID': 'TEST001'})
        
        beam_sequence = rt_plan._create_beam_sequence(sample_beam_configs['sbrt_with_setup'])
        
        # Verify all beams are included
        assert len(beam_sequence) == 3
        
        # Verify setup beam is properly identified
        setup_beams = [beam for beam in beam_sequence if beam.BeamType == 'SETUP']
        treatment_beams = [beam for beam in beam_sequence if beam.BeamType != 'SETUP']
        
        assert len(setup_beams) == 1
        assert len(treatment_beams) == 2
        assert setup_beams[0].BeamName == 'Setup_kV'

    def test_create_beam_sequence_dynamic_beams(self, sample_beam_configs):
        """Test beam sequence creation with dynamic beams (IMRT/VMAT)."""
        rt_plan = RTPlan(patient_info={'PatientID': 'TEST001'})
        
        beam_sequence = rt_plan._create_beam_sequence(sample_beam_configs['vmat_arcs'])
        
        # Verify dynamic beam properties
        assert len(beam_sequence) == 2
        assert all(beam.BeamType == 'DYNAMIC' for beam in beam_sequence)
        assert all(hasattr(beam, 'ControlPointSequence') for beam in beam_sequence)
        
        # Verify gantry angles for arcs
        gantry_angles = [beam.ControlPointSequence[0].GantryAngle for beam in beam_sequence]
        expected_angles = [181.0, 179.0]
        assert gantry_angles == expected_angles

    def test_create_beam_sequence_validation_logging(self, sample_beam_configs, caplog):
        """Test that beam sequence creation includes proper logging."""
        rt_plan = RTPlan(patient_info={'PatientID': 'TEST001'})
        
        rt_plan._create_beam_sequence(sample_beam_configs['static_3_field'])
        
        # Verify basic logging information (the structured data is tested separately)
        assert "Created beam sequence with 3 beams" in caplog.text

    def test_create_beam_sequence_empty_config(self):
        """Test error handling for empty beam configuration."""
        rt_plan = RTPlan(patient_info={'PatientID': 'TEST001'})
        
        with pytest.raises(DicomCreationError) as exc_info:
            rt_plan._create_beam_sequence([])
        
        assert "No beam configurations provided" in str(exc_info.value)
        assert "Provide at least one beam configuration" in str(exc_info.value)

    def test_create_beam_sequence_invalid_beam_config(self):
        """Test error handling for invalid beam configuration."""
        rt_plan = RTPlan(patient_info={'PatientID': 'TEST001'})
        
        invalid_config = [
            {
                'name': 'InvalidBeam',
                # Missing required fields: energy, gantry_angle, dose_weight
            }
        ]
        
        with pytest.raises(DicomCreationError) as exc_info:
            rt_plan._create_beam_sequence(invalid_config)
        
        assert "Failed to create beam sequence" in str(exc_info.value)


class TestBeamConfigurationValidation:
    """Test beam configuration validation functionality."""

    def test_validate_beam_configurations_valid_static(self, sample_beam_configs):
        """Test validation of valid static beam configurations."""
        rt_plan = RTPlan(patient_info={'PatientID': 'TEST001'})
        
        # Should not raise any exceptions
        rt_plan._validate_beam_configurations(sample_beam_configs['static_3_field'])

    def test_validate_beam_configurations_valid_with_setup(self, sample_beam_configs):
        """Test validation of configurations including setup beams."""
        rt_plan = RTPlan(patient_info={'PatientID': 'TEST001'})
        
        # Should not raise any exceptions
        rt_plan._validate_beam_configurations(sample_beam_configs['sbrt_with_setup'])

    def test_validate_beam_configurations_empty_list(self):
        """Test validation error for empty beam configuration list."""
        rt_plan = RTPlan(patient_info={'PatientID': 'TEST001'})
        
        with pytest.raises(DicomCreationError) as exc_info:
            rt_plan._validate_beam_configurations([])
        
        assert "No beam configurations provided" in str(exc_info.value)

    def test_validate_beam_configurations_weight_sum_error(self):
        """Test validation error for beam weights not summing to 1.0."""
        rt_plan = RTPlan(patient_info={'PatientID': 'TEST001'})
        
        invalid_weights = [
            {
                'name': 'AP', 'energy': 18, 'gantry_angle': 0,
                'dose_weight': 0.5, 'beam_type': 'STATIC'
            },
            {
                'name': 'PA', 'energy': 18, 'gantry_angle': 180,
                'dose_weight': 0.4, 'beam_type': 'STATIC'
            }
        ]
        # Weights sum to 0.9, not 1.0
        
        with pytest.raises(DicomCreationError) as exc_info:
            rt_plan._validate_beam_configurations(invalid_weights)
        
        assert "Treatment beam weights sum to 0.900, must sum to 1.0" in str(exc_info.value)

    def test_validate_beam_configurations_duplicate_names(self):
        """Test validation error for duplicate beam names."""
        rt_plan = RTPlan(patient_info={'PatientID': 'TEST001'})
        
        duplicate_names = [
            {
                'name': 'AP', 'energy': 18, 'gantry_angle': 0,
                'dose_weight': 0.5, 'beam_type': 'STATIC'
            },
            {
                'name': 'AP', 'energy': 18, 'gantry_angle': 180,
                'dose_weight': 0.5, 'beam_type': 'STATIC'
            }
        ]
        
        with pytest.raises(DicomCreationError) as exc_info:
            rt_plan._validate_beam_configurations(duplicate_names)
        
        assert "Duplicate beam names found: ['AP']" in str(exc_info.value)

    def test_validate_beam_configurations_setup_beams_excluded_from_weight_check(self):
        """Test that setup beams are excluded from weight sum validation."""
        rt_plan = RTPlan(patient_info={'PatientID': 'TEST001'})
        
        config_with_setup = [
            {
                'name': 'Setup', 'energy': 6, 'gantry_angle': 0,
                'dose_weight': 0.0, 'beam_type': 'SETUP'
            },
            {
                'name': 'Treatment', 'energy': 18, 'gantry_angle': 0,
                'dose_weight': 1.0, 'beam_type': 'STATIC'
            }
        ]
        
        # Should not raise exception despite setup beam having 0 weight
        rt_plan._validate_beam_configurations(config_with_setup)

    def test_validate_beam_configurations_weight_tolerance(self):
        """Test that small floating point errors in weight sum are tolerated."""
        rt_plan = RTPlan(patient_info={'PatientID': 'TEST001'})
        
        # Use weights that sum to 1.0 + small floating point error
        close_to_one = [
            {
                'name': 'AP', 'energy': 18, 'gantry_angle': 0,
                'dose_weight': 0.33333, 'beam_type': 'STATIC'
            },
            {
                'name': 'RPO', 'energy': 18, 'gantry_angle': 120,
                'dose_weight': 0.33333, 'beam_type': 'STATIC'
            },
            {
                'name': 'LPO', 'energy': 18, 'gantry_angle': 240,
                'dose_weight': 0.33334, 'beam_type': 'STATIC'
            }
        ]
        # Sum = 1.00000, within tolerance
        
        # Should not raise exception
        rt_plan._validate_beam_configurations(close_to_one)


class TestBeamConfigurationEdgeCases:
    """Test edge cases in beam configuration handling."""

    def test_beam_configuration_optional_parameters(self):
        """Test beam configuration with only required parameters."""
        rt_plan = RTPlan(patient_info={'PatientID': 'TEST001'})
        
        minimal_config = [
            {
                'name': 'MinimalBeam',
                'energy': 18,
                'gantry_angle': 0,
                'dose_weight': 1.0
            }
        ]
        
        # Should work with defaults for optional parameters
        beam_sequence = rt_plan._create_beam_sequence(minimal_config)
        assert len(beam_sequence) == 1
        assert beam_sequence[0].BeamName == 'MinimalBeam'

    def test_beam_configuration_all_parameters(self):
        """Test beam configuration with all optional parameters specified."""
        rt_plan = RTPlan(patient_info={'PatientID': 'TEST001'})
        
        complete_config = [
            {
                'name': 'CompleteBeam',
                'energy': 18,
                'gantry_angle': 45,
                'dose_weight': 1.0,
                'collimator_angle': 90,
                'couch_angle': 0,
                'beam_type': 'STATIC',
                'radiation_type': 'PHOTON',
                'machine_name': 'TrueBeam',
                'sad': 1000.0,
                'meterset': 150.0
            }
        ]
        
        beam_sequence = rt_plan._create_beam_sequence(complete_config)
        assert len(beam_sequence) == 1
        assert beam_sequence[0].BeamName == 'CompleteBeam'

    def test_beam_configuration_electron_beam(self):
        """Test beam configuration for electron beams."""
        rt_plan = RTPlan(patient_info={'PatientID': 'TEST001'})
        
        electron_config = [
            {
                'name': 'ElectronField',
                'energy': 12,  # MeV for electrons
                'gantry_angle': 0,
                'dose_weight': 1.0,
                'beam_type': 'STATIC',
                'radiation_type': 'ELECTRON'
            }
        ]
        
        beam_sequence = rt_plan._create_beam_sequence(electron_config)
        assert len(beam_sequence) == 1
        assert beam_sequence[0].RadiationType == 'ELECTRON'

    def test_beam_configuration_extreme_angles(self):
        """Test beam configuration with boundary angle values."""
        rt_plan = RTPlan(patient_info={'PatientID': 'TEST001'})
        
        extreme_angles = [
            {
                'name': 'Angle0', 'energy': 18, 'gantry_angle': 0,
                'dose_weight': 0.25, 'beam_type': 'STATIC'
            },
            {
                'name': 'Angle90', 'energy': 18, 'gantry_angle': 90,
                'dose_weight': 0.25, 'beam_type': 'STATIC'
            },
            {
                'name': 'Angle180', 'energy': 18, 'gantry_angle': 180,
                'dose_weight': 0.25, 'beam_type': 'STATIC'
            },
            {
                'name': 'Angle359', 'energy': 18, 'gantry_angle': 359.9,
                'dose_weight': 0.25, 'beam_type': 'STATIC'
            }
        ]
        
        beam_sequence = rt_plan._create_beam_sequence(extreme_angles)
        assert len(beam_sequence) == 4
        
        # Verify angles are preserved
        gantry_angles = [beam.ControlPointSequence[0].GantryAngle for beam in beam_sequence]
        expected_angles = [0.0, 90.0, 180.0, 359.9]
        assert gantry_angles == expected_angles


@pytest.fixture
def sample_beam_configs():
    """Provide sample beam configurations for testing."""
    return {
        'static_3_field': [
            {
                'name': 'AP',
                'energy': 18,
                'gantry_angle': 0,
                'dose_weight': 0.33,
                'beam_type': 'STATIC',
                'radiation_type': 'PHOTON'
            },
            {
                'name': 'RPO',
                'energy': 18,
                'gantry_angle': 120,
                'dose_weight': 0.33,
                'beam_type': 'STATIC',
                'radiation_type': 'PHOTON'
            },
            {
                'name': 'LPO',
                'energy': 18,
                'gantry_angle': 240,
                'dose_weight': 0.34,
                'beam_type': 'STATIC',
                'radiation_type': 'PHOTON'
            }
        ],
        'sbrt_with_setup': [
            {
                'name': 'Setup_kV',
                'energy': 6,
                'gantry_angle': 0,
                'dose_weight': 0.0,
                'beam_type': 'SETUP',
                'radiation_type': 'PHOTON'
            },
            {
                'name': 'Arc_CW',
                'energy': 10,
                'gantry_angle': 181,
                'dose_weight': 0.5,
                'beam_type': 'DYNAMIC',
                'radiation_type': 'PHOTON'
            },
            {
                'name': 'Arc_CCW',
                'energy': 10,
                'gantry_angle': 179,
                'dose_weight': 0.5,
                'beam_type': 'DYNAMIC',
                'radiation_type': 'PHOTON'
            }
        ],
        'vmat_arcs': [
            {
                'name': 'VMAT_CW',
                'energy': 6,
                'gantry_angle': 181,
                'dose_weight': 0.5,
                'beam_type': 'DYNAMIC',
                'radiation_type': 'PHOTON'
            },
            {
                'name': 'VMAT_CCW',
                'energy': 6,
                'gantry_angle': 179,
                'dose_weight': 0.5,
                'beam_type': 'DYNAMIC',
                'radiation_type': 'PHOTON'
            }
        ]
    }