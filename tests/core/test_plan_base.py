"""
Tests for RT Plan Base Class Implementation.

This module tests the RTPlan class for basic instantiation, beam configuration
handling, clinical validation, and integration with the BaseDicomCreator framework.
Tests ensure proper treatment plan creation workflows and DICOM compliance.
"""

import pytest
import pydicom
from pydicom.dataset import Dataset, FileMetaDataset
from pydicom.uid import RTPlanStorage
import numpy as np
from pathlib import Path
import tempfile

from pyrt_dicom.core.rt_plan import RTPlan
from pyrt_dicom.core.base import BaseDicomCreator
from pyrt_dicom.utils.exceptions import DicomCreationError, ValidationError
from pyrt_dicom.templates.plan_template import RTPlanTemplate


class TestRTPlanBase:
    """Test suite for RT Plan base class functionality."""

    def test_inheritance(self):
        """Test that RTPlan properly inherits from BaseDicomCreator."""
        rt_plan = RTPlan()
        assert isinstance(rt_plan, BaseDicomCreator)
        assert isinstance(rt_plan, RTPlan)

    def test_basic_initialization(self):
        """Test basic RT Plan initialization."""
        patient_info = {'PatientID': 'TEST001', 'PatientName': 'Test^Patient'}
        
        rt_plan = RTPlan(
            patient_info=patient_info,
            plan_name="Test Plan",
            treatment_intent="CURATIVE"
        )
        
        assert rt_plan.patient_info == patient_info
        assert rt_plan.plan_name == "Test Plan"
        assert rt_plan.treatment_intent == "CURATIVE"
        assert rt_plan.prescription_dose is None
        assert rt_plan.fractions is None
        assert rt_plan.beam_configurations == []

    def test_initialization_with_reference_image(self):
        """Test RT Plan initialization with reference CT image."""
        # Create mock CT dataset
        ct_dataset = Dataset()
        ct_dataset.SOPClassUID = "1.2.840.10008.*******.1.2"  # CT Image Storage
        ct_dataset.SOPInstanceUID = "*******.*******.9"
        ct_dataset.StudyInstanceUID = "*******.*******"
        ct_dataset.FrameOfReferenceUID = "*******.5.6.7"
        ct_dataset.StudyDescription = "Planning CT"
        
        rt_plan = RTPlan(
            reference_image=ct_dataset,
            patient_info={'PatientID': 'TEST001'}
        )
        
        assert rt_plan.reference_image == ct_dataset

    def test_from_beam_config_basic(self):
        """Test creation from basic beam configuration."""
        prescription = {
            'prescription_dose': 2000,  # 20 Gy
            'fractions': 10,
            'beams': [
                {
                    'name': 'AP',
                    'energy': 6,
                    'gantry_angle': 0,
                    'dose_weight': 1.0,
                    'beam_type': 'STATIC',
                    'radiation_type': 'PHOTON'
                }
            ]
        }
        
        rt_plan = RTPlan.from_beam_config(
            prescription=prescription,
            patient_info={'PatientID': 'TEST001'},
            plan_name="Test Plan"
        )
        
        assert rt_plan.prescription_dose == 2000
        assert rt_plan.fractions == 10
        assert len(rt_plan.beam_configurations) == 1
        assert rt_plan.beam_configurations[0]['name'] == 'AP'
        assert rt_plan.plan_name == "Test Plan"

    def test_from_beam_config_multi_beam(self):
        """Test creation from multi-beam configuration."""
        prescription = {
            'prescription_dose': 7000,  # 70 Gy
            'fractions': 35,
            'beams': [
                {
                    'name': 'AP',
                    'energy': 18,
                    'gantry_angle': 0,
                    'collimator_angle': 0,
                    'couch_angle': 0,
                    'dose_weight': 0.33,
                    'beam_type': 'STATIC',
                    'radiation_type': 'PHOTON'
                },
                {
                    'name': 'RPO',
                    'energy': 18,
                    'gantry_angle': 120,
                    'collimator_angle': 0,
                    'couch_angle': 0,
                    'dose_weight': 0.33,
                    'beam_type': 'STATIC',
                    'radiation_type': 'PHOTON'
                },
                {
                    'name': 'LPO',
                    'energy': 18,
                    'gantry_angle': 240,
                    'collimator_angle': 0,
                    'couch_angle': 0,
                    'dose_weight': 0.34,
                    'beam_type': 'STATIC',
                    'radiation_type': 'PHOTON'
                }
            ]
        }
        
        rt_plan = RTPlan.from_beam_config(
            prescription=prescription,
            patient_info={'PatientID': 'PROSTATE001'},
            plan_name="Prostate 3D-CRT"
        )
        
        assert rt_plan.prescription_dose == 7000
        assert rt_plan.fractions == 35
        assert len(rt_plan.beam_configurations) == 3
        
        beam_names = [beam['name'] for beam in rt_plan.beam_configurations]
        assert 'AP' in beam_names
        assert 'RPO' in beam_names
        assert 'LPO' in beam_names

    def test_from_beam_config_with_setup_beam(self):
        """Test creation with setup and treatment beams."""
        prescription = {
            'prescription_dose': 5000,  # 50 Gy SBRT
            'fractions': 5,
            'beams': [
                {
                    'name': 'Setup_kV',
                    'energy': 6,
                    'gantry_angle': 0,
                    'dose_weight': 0.0,  # Setup beam
                    'beam_type': 'SETUP',
                    'radiation_type': 'PHOTON'
                },
                {
                    'name': 'Arc_CW',
                    'energy': 10,
                    'gantry_angle': 181,
                    'dose_weight': 0.5,
                    'beam_type': 'DYNAMIC',
                    'radiation_type': 'PHOTON'
                },
                {
                    'name': 'Arc_CCW',
                    'energy': 10,
                    'gantry_angle': 179,
                    'dose_weight': 0.5,
                    'beam_type': 'DYNAMIC',
                    'radiation_type': 'PHOTON'
                }
            ]
        }
        
        rt_plan = RTPlan.from_beam_config(
            prescription=prescription,
            plan_name="Lung SBRT"
        )
        
        # Check that setup beam and treatment beams are both included
        beam_types = [beam['beam_type'] for beam in rt_plan.beam_configurations]
        assert 'SETUP' in beam_types
        assert 'DYNAMIC' in beam_types
        
        # Check dose weights
        setup_beams = [beam for beam in rt_plan.beam_configurations if beam['beam_type'] == 'SETUP']
        treatment_beams = [beam for beam in rt_plan.beam_configurations if beam['beam_type'] != 'SETUP']
        
        assert setup_beams[0]['dose_weight'] == 0.0
        total_treatment_weight = sum(beam['dose_weight'] for beam in treatment_beams)
        assert abs(total_treatment_weight - 1.0) < 0.01

    def test_from_beam_config_with_references(self):
        """Test creation with referenced dose and structure objects."""
        # Create mock referenced objects
        dose_dataset = Dataset()
        dose_dataset.SOPInstanceUID = "*******.*******.9.1"
        dose_dataset.SOPClassUID = "1.2.840.10008.*******.1.481.2"  # RT Dose Storage
        
        struct_dataset = Dataset()
        struct_dataset.SOPInstanceUID = "*******.*******.9.2"
        struct_dataset.SOPClassUID = "1.2.840.10008.*******.1.481.3"  # RT Structure Set Storage
        
        prescription = {
            'prescription_dose': 6000,
            'fractions': 30,
            'beams': [
                {
                    'name': 'Test Beam',
                    'energy': 6,
                    'gantry_angle': 0,
                    'dose_weight': 1.0
                }
            ]
        }
        
        rt_plan = RTPlan.from_beam_config(
            prescription=prescription,
            reference_dose=dose_dataset,
            reference_structures=struct_dataset,
            patient_info={'PatientID': 'REF001'}
        )
        
        assert rt_plan.referenced_dose == dose_dataset
        assert rt_plan.referenced_structures == struct_dataset

    def test_validate_prescription_config_missing_fields(self):
        """Test validation of prescription configuration with missing fields."""
        # Test missing prescription_dose
        incomplete_prescription = {
            'fractions': 10,
            'beams': [{'name': 'Test', 'energy': 6, 'gantry_angle': 0, 'dose_weight': 1.0}]
        }
        
        with pytest.raises(DicomCreationError, match="Missing required prescription field: prescription_dose"):
            RTPlan.from_beam_config(prescription=incomplete_prescription)
        
        # Test missing fractions
        incomplete_prescription = {
            'prescription_dose': 2000,
            'beams': [{'name': 'Test', 'energy': 6, 'gantry_angle': 0, 'dose_weight': 1.0}]
        }
        
        with pytest.raises(DicomCreationError, match="Missing required prescription field: fractions"):
            RTPlan.from_beam_config(prescription=incomplete_prescription)
        
        # Test missing beams
        incomplete_prescription = {
            'prescription_dose': 2000,
            'fractions': 10
        }
        
        with pytest.raises(DicomCreationError, match="Missing required prescription field: beams"):
            RTPlan.from_beam_config(prescription=incomplete_prescription)

    def test_validate_prescription_config_empty_beams(self):
        """Test validation with empty beam list."""
        prescription = {
            'prescription_dose': 2000,
            'fractions': 10,
            'beams': []  # Empty beam list
        }
        
        with pytest.raises(DicomCreationError, match="'beams' must be a non-empty list"):
            RTPlan.from_beam_config(prescription=prescription)

    def test_clinical_validation_dose_ranges(self):
        """Test clinical validation of dose ranges."""
        # Test very low dose per fraction
        prescription = {
            'prescription_dose': 500,  # 5 Gy total
            'fractions': 10,  # 0.5 Gy per fraction
            'beams': [{'name': 'Test', 'energy': 6, 'gantry_angle': 0, 'dose_weight': 1.0}]
        }
        
        with pytest.raises(ValidationError, match="Low dose per fraction"):
            RTPlan.from_beam_config(prescription=prescription)
        
        # Test very high dose per fraction for SBRT (should generate warning, not error)
        prescription = {
            'prescription_dose': 6000,  # 60 Gy total  
            'fractions': 3,   # 20 Gy per fraction (exceeds 18 Gy limit for 3fx)
            'beams': [{'name': 'Test', 'energy': 6, 'gantry_angle': 0, 'dose_weight': 1.0}]
        }
        
        # Should create successfully but log warnings
        rt_plan = RTPlan.from_beam_config(prescription=prescription)
        assert rt_plan.prescription_dose == 6000
        assert rt_plan.fractions == 3

    def test_clinical_validation_beam_configuration(self):
        """Test clinical validation of beam configuration."""
        # Test plan with no treatment beams (only setup beams)
        prescription = {
            'prescription_dose': 2000,
            'fractions': 10,
            'beams': [
                {
                    'name': 'Setup_Only',
                    'energy': 6,
                    'gantry_angle': 0,
                    'dose_weight': 0.0,
                    'beam_type': 'SETUP'
                }
            ]
        }
        
        with pytest.raises(ValidationError, match="At least one treatment beam is required"):
            RTPlan.from_beam_config(prescription=prescription)

    def test_add_beam(self):
        """Test adding individual beams to plan."""
        rt_plan = RTPlan(patient_info={'PatientID': 'TEST001'})
        
        beam_config = {
            'name': 'AP',
            'energy': 6,
            'gantry_angle': 0,
            'dose_weight': 1.0,
            'beam_type': 'STATIC'
        }
        
        rt_plan.add_beam(beam_config)
        
        assert len(rt_plan.beam_configurations) == 1
        assert rt_plan.beam_configurations[0]['name'] == 'AP'

    def test_get_prescription_summary(self):
        """Test prescription summary generation."""
        prescription = {
            'prescription_dose': 7000,
            'fractions': 35,
            'beams': [
                {'name': 'AP', 'energy': 6, 'gantry_angle': 0, 'dose_weight': 0.5, 'beam_type': 'STATIC'},
                {'name': 'PA', 'energy': 6, 'gantry_angle': 180, 'dose_weight': 0.5, 'beam_type': 'STATIC'},
                {'name': 'Setup', 'energy': 6, 'gantry_angle': 0, 'dose_weight': 0.0, 'beam_type': 'SETUP'}
            ]
        }
        
        rt_plan = RTPlan.from_beam_config(prescription=prescription)
        summary = rt_plan.get_prescription_summary()
        
        assert summary['total_dose'] == 7000
        assert summary['dose_per_fraction'] == 200  # 7000/35
        assert summary['fractions'] == 35
        assert summary['treatment_beams'] == 2  # Excludes setup beam
        assert summary['setup_beams'] == 1
        assert 'AP' in summary['beam_names']
        assert 'PA' in summary['beam_names']
        assert 'Setup' not in summary['beam_names']  # Setup beams not in treatment beam names

    def test_get_prescription_summary_unconfigured(self):
        """Test prescription summary for unconfigured plan."""
        rt_plan = RTPlan()
        summary = rt_plan.get_prescription_summary()
        
        assert summary['total_dose'] is None
        assert summary['dose_per_fraction'] is None
        assert summary['fractions'] is None
        assert summary['treatment_beams'] == 0
        assert summary['setup_beams'] == 0

    def test_create_modality_specific_dataset(self):
        """Test creation of modality-specific DICOM dataset."""
        prescription = {
            'prescription_dose': 2000,
            'fractions': 10,
            'beams': [
                {
                    'name': 'Test Beam',
                    'energy': 6,
                    'gantry_angle': 0,
                    'dose_weight': 1.0
                }
            ]
        }
        
        rt_plan = RTPlan.from_beam_config(
            prescription=prescription,
            patient_info={'PatientID': 'TEST001'}
        )
        
        dataset = rt_plan._create_modality_specific_dataset()
        
        # Test basic DICOM elements
        assert dataset.SOPClassUID == RTPlanStorage
        assert dataset.Modality == "RTPLAN"
        assert dataset.RTPlanName == "RT Plan"
        
        # Test plan-specific sequences
        assert hasattr(dataset, 'BeamSequence')
        assert hasattr(dataset, 'FractionGroupSequence')
        assert hasattr(dataset, 'DoseReferenceSequence')

    def test_create_dataset_unconfigured_plan(self):
        """Test dataset creation for unconfigured plan fails."""
        rt_plan = RTPlan()
        
        with pytest.raises(DicomCreationError, match="RT Plan not properly configured"):
            rt_plan._create_modality_specific_dataset()

    def test_validate_modality_specific(self):
        """Test RT Plan specific validation."""
        # Test unconfigured plan
        rt_plan = RTPlan()
        rt_plan._validate_modality_specific()
        
        assert "Prescription dose not set - use from_beam_config()" in rt_plan._validation_errors
        assert "Number of fractions not set - use from_beam_config()" in rt_plan._validation_errors
        assert "Beam configurations not set - use from_beam_config()" in rt_plan._validation_errors
        
        # Test plan with long name
        rt_plan = RTPlan(plan_name="This is a very long plan name that exceeds 16 characters")
        rt_plan._validate_modality_specific()
        
        assert any("Plan name too long" in error for error in rt_plan._validation_errors)
        
        # Test plan with invalid treatment intent
        rt_plan = RTPlan(treatment_intent="INVALID_INTENT")
        rt_plan._validate_modality_specific()
        
        assert any("Invalid treatment intent" in error for error in rt_plan._validation_errors)

    def test_get_referenced_object_uid(self):
        """Test extraction of UIDs from referenced objects."""
        rt_plan = RTPlan()
        
        # Test string UID
        uid_string = "*******.*******.9"
        result = rt_plan._get_referenced_object_uid(uid_string)
        assert result == uid_string
        
        # Test dataset with UID
        dataset = Dataset()
        dataset.SOPInstanceUID = "*******.*******.9.1"
        result = rt_plan._get_referenced_object_uid(dataset)
        assert result == "*******.*******.9.1"
        
        # Test None reference
        result = rt_plan._get_referenced_object_uid(None)
        assert result is None

    def test_save_functionality(self):
        """Test saving RT Plan to DICOM file."""
        prescription = {
            'prescription_dose': 2000,
            'fractions': 10,
            'beams': [
                {
                    'name': 'Test Beam',
                    'energy': 6,
                    'gantry_angle': 0,
                    'dose_weight': 1.0
                }
            ]
        }
        
        rt_plan = RTPlan.from_beam_config(
            prescription=prescription,
            patient_info={'PatientID': 'SAVE001', 'PatientName': 'Save^Test'}
        )
        
        with tempfile.TemporaryDirectory() as temp_dir:
            output_path = Path(temp_dir) / "test_plan.dcm"
            saved_path = rt_plan.save(output_path)
            
            assert saved_path.exists()
            assert saved_path == output_path
            
            # Verify saved file can be read
            saved_dataset = pydicom.dcmread(saved_path)
            assert saved_dataset.SOPClassUID == RTPlanStorage
            assert saved_dataset.Modality == "RTPLAN"
            assert saved_dataset.PatientID == "SAVE001"

    def test_repr(self):
        """Test string representation of RT Plan."""
        # Test unconfigured plan
        rt_plan = RTPlan(patient_info={'PatientID': 'TEST001'})
        repr_str = repr(rt_plan)
        assert "RTPlan" in repr_str
        assert "PatientID=TEST001" in repr_str
        assert "not configured" in repr_str
        
        # Test configured plan
        prescription = {
            'prescription_dose': 7000,
            'fractions': 35,
            'beams': [
                {'name': 'AP', 'energy': 6, 'gantry_angle': 0, 'dose_weight': 0.5},
                {'name': 'PA', 'energy': 6, 'gantry_angle': 180, 'dose_weight': 0.5}
            ]
        }
        
        rt_plan = RTPlan.from_beam_config(
            prescription=prescription,
            patient_info={'PatientID': 'CONFIG001'},
            plan_name="Configured Plan"
        )
        
        repr_str = repr(rt_plan)
        assert "RTPlan" in repr_str
        assert "PatientID=CONFIG001" in repr_str
        assert "plan='Configured Plan'" in repr_str
        assert "7000 cGy in 35 fx" in repr_str
        assert "2 beams" in repr_str

    def test_validation_before_save(self):
        """Test validation occurs before saving."""
        # Create plan with validation issues
        rt_plan = RTPlan(plan_name="Too Long Plan Name That Exceeds Limit")
        
        with tempfile.TemporaryDirectory() as temp_dir:
            output_path = Path(temp_dir) / "test_plan.dcm"
            
            with pytest.raises(ValidationError):
                rt_plan.save(output_path)

    def test_referenced_objects_in_dataset(self):
        """Test that referenced objects appear in created dataset."""
        # Create mock referenced objects
        dose_uid = "*******.*******.9.1"
        struct_uid = "*******.*******.9.2"
        
        prescription = {
            'prescription_dose': 2000,
            'fractions': 10,
            'beams': [{'name': 'Test', 'energy': 6, 'gantry_angle': 0, 'dose_weight': 1.0}]
        }
        
        rt_plan = RTPlan.from_beam_config(
            prescription=prescription,
            reference_dose=dose_uid,
            reference_structures=struct_uid,
            patient_info={'PatientID': 'REF001'}
        )
        
        dataset = rt_plan._create_modality_specific_dataset()
        
        # Check referenced dose sequence
        assert hasattr(dataset, 'ReferencedDoseSequence')
        ref_dose = dataset.ReferencedDoseSequence[0]
        assert ref_dose.ReferencedSOPInstanceUID == dose_uid
        
        # Check referenced structure set sequence
        assert hasattr(dataset, 'ReferencedStructureSetSequence')
        ref_struct = dataset.ReferencedStructureSetSequence[0]
        assert ref_struct.ReferencedSOPInstanceUID == struct_uid