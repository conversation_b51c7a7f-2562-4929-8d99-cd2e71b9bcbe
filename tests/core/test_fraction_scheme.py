"""
Tests for RT Plan fraction scheme creation functionality.

This module provides comprehensive testing of the fraction scheme creation logic
in the RTPlan class, covering prescription parameter validation, DICOM fraction
group sequence generation, and clinical fractionation safety checks.

Test Categories:
    - Prescription parameter validation
    - DICOM fraction group sequence generation
    - Clinical fractionation validation
    - Dose calculation and beam linking
    - Error handling and edge cases
"""

import pytest

from pyrt_dicom.core.rt_plan import RTPlan
from pyrt_dicom.utils.exceptions import DicomCreationError


class TestFractionSchemeCreation:
    """Test fraction scheme creation functionality."""

    def test_create_fraction_scheme_conventional_fractionation(self, sample_beam_configs):
        """Test fraction scheme creation for conventional fractionation."""
        rt_plan = RTPlan(patient_info={'PatientID': 'TEST001'})
        
        prescription_dose = 7000  # 70 Gy
        fractions = 35
        beam_configs = sample_beam_configs['static_3_field']
        
        fraction_sequence = rt_plan._create_fraction_scheme(
            prescription_dose, fractions, beam_configs
        )
        
        # Verify sequence structure
        assert len(fraction_sequence) == 1  # Single fraction group
        fraction_group = fraction_sequence[0]
        
        assert fraction_group.FractionGroupNumber == "1"
        assert fraction_group.NumberOfFractionsPlanned == "35"
        assert hasattr(fraction_group, 'ReferencedBeamSequence')
        
        # Verify beam references (3 treatment beams)
        ref_beams = fraction_group.ReferencedBeamSequence
        assert len(ref_beams) == 3
        
        # Verify per-beam dose calculations
        total_beam_dose = sum(float(beam.BeamDose) for beam in ref_beams)
        assert abs(total_beam_dose - prescription_dose) < 0.1

    def test_create_fraction_scheme_sbrt_hypofractionation(self, sample_beam_configs):
        """Test fraction scheme creation for SBRT hypofractionation."""
        rt_plan = RTPlan(patient_info={'PatientID': 'TEST001'})
        
        prescription_dose = 5000  # 50 Gy
        fractions = 5
        beam_configs = sample_beam_configs['sbrt_with_setup']
        
        fraction_sequence = rt_plan._create_fraction_scheme(
            prescription_dose, fractions, beam_configs
        )
        
        fraction_group = fraction_sequence[0]
        assert fraction_group.NumberOfFractionsPlanned == "5"
        
        # Should only include treatment beams, not setup beam
        ref_beams = fraction_group.ReferencedBeamSequence
        assert len(ref_beams) == 2  # 2 treatment beams, setup beam excluded
        
        # Verify total dose matches prescription
        total_beam_dose = sum(float(beam.BeamDose) for beam in ref_beams)
        assert abs(total_beam_dose - prescription_dose) < 0.1

    def test_create_fraction_scheme_single_fraction_srs(self):
        """Test fraction scheme creation for single fraction SRS."""
        rt_plan = RTPlan(patient_info={'PatientID': 'TEST001'})
        
        prescription_dose = 2000  # 20 Gy
        fractions = 1
        beam_configs = [
            {
                'name': 'SRS_Arc',
                'energy': 6,
                'gantry_angle': 0,
                'dose_weight': 1.0,
                'beam_type': 'DYNAMIC'
            }
        ]
        
        fraction_sequence = rt_plan._create_fraction_scheme(
            prescription_dose, fractions, beam_configs
        )
        
        fraction_group = fraction_sequence[0]
        assert fraction_group.NumberOfFractionsPlanned == "1"
        
        # Verify single beam gets full dose
        ref_beams = fraction_group.ReferencedBeamSequence
        assert len(ref_beams) == 1
        assert float(ref_beams[0].BeamDose) == prescription_dose

    def test_create_fraction_scheme_logging(self, sample_beam_configs, caplog):
        """Test that fraction scheme creation includes proper logging."""
        rt_plan = RTPlan(patient_info={'PatientID': 'TEST001'})
        
        prescription_dose = 7000
        fractions = 35
        beam_configs = sample_beam_configs['static_3_field']
        
        rt_plan._create_fraction_scheme(prescription_dose, fractions, beam_configs)
        
        # Verify basic logging information (structured data is tested separately)
        assert "Created fraction scheme: 7000 cGy in 35 fractions" in caplog.text
        assert "200.0 cGy/fx" in caplog.text
        assert "treatment beams" in caplog.text

    def test_create_fraction_scheme_hypofractionated_logging(self, caplog):
        """Test logging for hypofractionated treatments."""
        rt_plan = RTPlan(patient_info={'PatientID': 'TEST001'})
        
        prescription_dose = 5000
        fractions = 5
        beam_configs = [
            {
                'name': 'SBRT_Arc',
                'energy': 10,
                'gantry_angle': 0,
                'dose_weight': 1.0,
                'beam_type': 'DYNAMIC'
            }
        ]
        
        rt_plan._create_fraction_scheme(prescription_dose, fractions, beam_configs)
        
        # Verify basic logging information
        assert "1000.0 cGy/fx" in caplog.text


class TestPrescriptionParameterValidation:
    """Test prescription parameter validation functionality."""

    def test_validate_prescription_parameters_valid_conventional(self):
        """Test validation of valid conventional prescription parameters."""
        rt_plan = RTPlan(patient_info={'PatientID': 'TEST001'})
        
        # Should not raise any exceptions
        rt_plan._validate_prescription_parameters(7000, 35)  # 200 cGy/fx

    def test_validate_prescription_parameters_valid_sbrt(self):
        """Test validation of valid SBRT prescription parameters."""
        rt_plan = RTPlan(patient_info={'PatientID': 'TEST001'})
        
        # Should not raise any exceptions
        rt_plan._validate_prescription_parameters(5000, 5)  # 1000 cGy/fx

    def test_validate_prescription_parameters_valid_srs(self):
        """Test validation of valid SRS prescription parameters."""
        rt_plan = RTPlan(patient_info={'PatientID': 'TEST001'})
        
        # Should not raise any exceptions
        rt_plan._validate_prescription_parameters(2000, 1)  # 2000 cGy/fx

    def test_validate_prescription_parameters_negative_dose(self):
        """Test validation error for negative prescription dose."""
        rt_plan = RTPlan(patient_info={'PatientID': 'TEST001'})
        
        with pytest.raises(DicomCreationError) as exc_info:
            rt_plan._validate_prescription_parameters(-1000, 10)
        
        assert "Prescription dose must be positive number" in str(exc_info.value)

    def test_validate_prescription_parameters_zero_dose(self):
        """Test validation error for zero prescription dose."""
        rt_plan = RTPlan(patient_info={'PatientID': 'TEST001'})
        
        with pytest.raises(DicomCreationError) as exc_info:
            rt_plan._validate_prescription_parameters(0, 10)
        
        assert "Prescription dose must be positive number" in str(exc_info.value)

    def test_validate_prescription_parameters_non_numeric_dose(self):
        """Test validation error for non-numeric prescription dose."""
        rt_plan = RTPlan(patient_info={'PatientID': 'TEST001'})
        
        with pytest.raises(DicomCreationError) as exc_info:
            rt_plan._validate_prescription_parameters("7000", 10)
        
        assert "Prescription dose must be positive number" in str(exc_info.value)

    def test_validate_prescription_parameters_negative_fractions(self):
        """Test validation error for negative number of fractions."""
        rt_plan = RTPlan(patient_info={'PatientID': 'TEST001'})
        
        with pytest.raises(DicomCreationError) as exc_info:
            rt_plan._validate_prescription_parameters(7000, -5)
        
        assert "Number of fractions must be positive integer" in str(exc_info.value)

    def test_validate_prescription_parameters_zero_fractions(self):
        """Test validation error for zero fractions."""
        rt_plan = RTPlan(patient_info={'PatientID': 'TEST001'})
        
        with pytest.raises(DicomCreationError) as exc_info:
            rt_plan._validate_prescription_parameters(7000, 0)
        
        assert "Number of fractions must be positive integer" in str(exc_info.value)

    def test_validate_prescription_parameters_float_fractions(self):
        """Test validation error for float number of fractions."""
        rt_plan = RTPlan(patient_info={'PatientID': 'TEST001'})
        
        with pytest.raises(DicomCreationError) as exc_info:
            rt_plan._validate_prescription_parameters(7000, 35.5)
        
        assert "Number of fractions must be positive integer" in str(exc_info.value)

    def test_validate_prescription_parameters_very_high_dose_per_fraction_warning(self, caplog):
        """Test warning for very high dose per fraction."""
        rt_plan = RTPlan(patient_info={'PatientID': 'TEST001'})
        
        # 35 Gy in single fraction - should generate warning
        rt_plan._validate_prescription_parameters(3500, 1)
        
        assert "Very high dose per fraction: 3500.0 cGy" in caplog.text
        assert "Consider clinical appropriateness" in caplog.text

    def test_validate_prescription_parameters_moderate_dose_no_warning(self, caplog):
        """Test that moderate doses don't generate warnings."""
        rt_plan = RTPlan(patient_info={'PatientID': 'TEST001'})
        
        # 25 Gy in single fraction - below warning threshold
        rt_plan._validate_prescription_parameters(2500, 1)
        
        # Should not contain high dose warning
        assert "Very high dose per fraction" not in caplog.text


class TestFractionSchemeEdgeCases:
    """Test edge cases in fraction scheme handling."""

    def test_fraction_scheme_minimal_dose(self):
        """Test fraction scheme with minimal prescription dose."""
        rt_plan = RTPlan(patient_info={'PatientID': 'TEST001'})
        
        # Very small dose for testing (e.g., QA plan)
        prescription_dose = 100  # 1 Gy
        fractions = 5
        beam_configs = [
            {
                'name': 'QA_Beam',
                'energy': 6,
                'gantry_angle': 0,
                'dose_weight': 1.0,
                'beam_type': 'STATIC'
            }
        ]
        
        fraction_sequence = rt_plan._create_fraction_scheme(
            prescription_dose, fractions, beam_configs
        )
        
        fraction_group = fraction_sequence[0]
        ref_beams = fraction_group.ReferencedBeamSequence
        assert len(ref_beams) == 1
        assert float(ref_beams[0].BeamDose) == prescription_dose

    def test_fraction_scheme_high_fraction_count(self):
        """Test fraction scheme with high number of fractions."""
        rt_plan = RTPlan(patient_info={'PatientID': 'TEST001'})
        
        # Extended fractionation (e.g., pediatric protocol)
        prescription_dose = 5040  # 50.4 Gy
        fractions = 42  # 1.2 Gy per fraction
        beam_configs = [
            {
                'name': 'Pediatric_AP',
                'energy': 6,
                'gantry_angle': 0,
                'dose_weight': 0.5,
                'beam_type': 'STATIC'
            },
            {
                'name': 'Pediatric_PA',
                'energy': 6,
                'gantry_angle': 180,
                'dose_weight': 0.5,
                'beam_type': 'STATIC'
            }
        ]
        
        fraction_sequence = rt_plan._create_fraction_scheme(
            prescription_dose, fractions, beam_configs
        )
        
        fraction_group = fraction_sequence[0]
        assert fraction_group.NumberOfFractionsPlanned == "42"
        
        # Verify dose distribution
        ref_beams = fraction_group.ReferencedBeamSequence
        assert len(ref_beams) == 2
        total_dose = sum(float(beam.BeamDose) for beam in ref_beams)
        assert abs(total_dose - prescription_dose) < 0.1

    def test_fraction_scheme_only_setup_beams(self):
        """Test fraction scheme with only setup beams (should create empty ref beam sequence)."""
        rt_plan = RTPlan(patient_info={'PatientID': 'TEST001'})
        
        # Use minimal treatment dose for QA plan with only setup beams having treatment dose
        prescription_dose = 1  # Minimal dose for QA
        fractions = 1
        beam_configs = [
            {
                'name': 'Setup_kV',
                'energy': 6,
                'gantry_angle': 0,
                'dose_weight': 0.0,
                'beam_type': 'SETUP'
            },
            {
                'name': 'Setup_CBCT',
                'energy': 6,
                'gantry_angle': 90,
                'dose_weight': 0.0,
                'beam_type': 'SETUP'
            }
        ]
        
        fraction_sequence = rt_plan._create_fraction_scheme(
            prescription_dose, fractions, beam_configs
        )
        
        fraction_group = fraction_sequence[0]
        ref_beams = fraction_group.ReferencedBeamSequence
        assert len(ref_beams) == 0  # No treatment beams

    def test_fraction_scheme_mixed_beam_types(self):
        """Test fraction scheme with mix of treatment and setup beams."""
        rt_plan = RTPlan(patient_info={'PatientID': 'TEST001'})
        
        prescription_dose = 6000  # 60 Gy
        fractions = 30
        beam_configs = [
            {
                'name': 'Setup_kV',
                'energy': 6,
                'gantry_angle': 0,
                'dose_weight': 0.0,
                'beam_type': 'SETUP'
            },
            {
                'name': 'VMAT_Arc1',
                'energy': 6,
                'gantry_angle': 181,
                'dose_weight': 0.6,
                'beam_type': 'DYNAMIC'
            },
            {
                'name': 'VMAT_Arc2',
                'energy': 6,
                'gantry_angle': 179,
                'dose_weight': 0.4,
                'beam_type': 'DYNAMIC'
            }
        ]
        
        fraction_sequence = rt_plan._create_fraction_scheme(
            prescription_dose, fractions, beam_configs
        )
        
        fraction_group = fraction_sequence[0]
        ref_beams = fraction_group.ReferencedBeamSequence
        
        # Should only include treatment beams
        assert len(ref_beams) == 2
        
        # Verify dose distribution matches beam weights
        beam_doses = [float(beam.BeamDose) for beam in ref_beams]
        expected_doses = [3600, 2400]  # 60% and 40% of 6000 cGy
        assert all(abs(actual - expected) < 0.1 for actual, expected in zip(beam_doses, expected_doses))


class TestFractionSchemeErrorHandling:
    """Test error handling in fraction scheme creation."""

    def test_fraction_scheme_creation_failure_handling(self):
        """Test error handling when fraction scheme creation fails."""
        rt_plan = RTPlan(patient_info={'PatientID': 'TEST001'})
        
        # This should trigger validation error
        with pytest.raises(DicomCreationError) as exc_info:
            rt_plan._create_fraction_scheme(-1000, 5, [])  # Negative dose
        
        assert "Failed to create fraction scheme" in str(exc_info.value)
        assert "Verify prescription dose is positive" in str(exc_info.value)

    def test_fraction_scheme_clinical_context_in_error(self):
        """Test that error messages include clinical context."""
        rt_plan = RTPlan(patient_info={'PatientID': 'TEST001'})
        
        with pytest.raises(DicomCreationError) as exc_info:
            rt_plan._create_fraction_scheme(0, 10, [])  # Zero dose
        
        error_str = str(exc_info.value)
        assert "prescription_dose" in error_str
        assert "fractions" in error_str
        assert "dose_per_fraction" in error_str


@pytest.fixture
def sample_beam_configs():
    """Provide sample beam configurations for testing."""
    return {
        'static_3_field': [
            {
                'name': 'AP',
                'energy': 18,
                'gantry_angle': 0,
                'dose_weight': 0.33,
                'beam_type': 'STATIC',
                'radiation_type': 'PHOTON'
            },
            {
                'name': 'RPO',
                'energy': 18,
                'gantry_angle': 120,
                'dose_weight': 0.33,
                'beam_type': 'STATIC',
                'radiation_type': 'PHOTON'
            },
            {
                'name': 'LPO',
                'energy': 18,
                'gantry_angle': 240,
                'dose_weight': 0.34,
                'beam_type': 'STATIC',
                'radiation_type': 'PHOTON'
            }
        ],
        'sbrt_with_setup': [
            {
                'name': 'Setup_kV',
                'energy': 6,
                'gantry_angle': 0,
                'dose_weight': 0.0,
                'beam_type': 'SETUP',
                'radiation_type': 'PHOTON'
            },
            {
                'name': 'Arc_CW',
                'energy': 10,
                'gantry_angle': 181,
                'dose_weight': 0.5,
                'beam_type': 'DYNAMIC',
                'radiation_type': 'PHOTON'
            },
            {
                'name': 'Arc_CCW',
                'energy': 10,
                'gantry_angle': 179,
                'dose_weight': 0.5,
                'beam_type': 'DYNAMIC',
                'radiation_type': 'PHOTON'
            }
        ]
    }