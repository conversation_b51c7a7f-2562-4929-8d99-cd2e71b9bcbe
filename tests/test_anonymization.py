"""
Unit tests for DICOM anonymization functionality.

This module provides comprehensive testing for the anonymization capabilities
of pyrt-dicom, ensuring clinical validity is preserved while removing patient
identifiers for research and data sharing.

## Test Coverage

- **Core Anonymization**: Basic anonymization functionality
- **RT-Specific Handling**: Preservation of critical RT data
- **Profile Testing**: All anonymization profiles (basic, clinical, research, HIPAA)
- **Treatment Plan Anonymization**: Complete treatment plan workflows
- **Audit Logging**: Comprehensive audit trail verification
- **Edge Cases**: Error handling and validation

## Test Data

Tests use synthetic DICOM datasets to avoid any patient data concerns.
All test datasets are generated programmatically with known values for
verification of anonymization effectiveness.
"""

import pytest
import tempfile
import shutil
from pathlib import Path
from datetime import datetime
from unittest.mock import Mock, patch

import pydicom
from pydicom import Dataset
from pydicom.uid import generate_uid

from pyrt_dicom.anonymization import (
    DicomAnonymizer,
    TreatmentPlanAnonymizer,
    AnonymizationProfiles,
    AnonymizationAuditor,
)


class TestAnonymizationProfiles:
    """Test anonymization profile functionality."""

    def test_get_basic_profile(self):
        """Test retrieval of basic anonymization profile."""
        profile = AnonymizationProfiles.get_profile("basic")

        assert profile["name"] == "basic"
        assert profile["remove_patient_identifiers"] is True
        assert profile["preserve_geometric_data"] is True
        assert profile["preserve_dose_data"] is True
        assert "DoseGridScaling" in profile["preserve_tags"]
        assert "DoseUnits" in profile["preserve_tags"]

    def test_get_clinical_profile(self):
        """Test retrieval of clinical anonymization profile."""
        profile = AnonymizationProfiles.get_profile("clinical")

        assert profile["name"] == "clinical"
        assert profile["preserve_dates"] is True
        assert profile["preserve_structure_names"] is True
        assert profile["date_shift_range"] == (30, 365)
        assert "ROIName" in profile["preserve_tags"]

    def test_get_research_profile(self):
        """Test retrieval of research anonymization profile."""
        profile = AnonymizationProfiles.get_profile("research")

        assert profile["name"] == "research"
        assert profile["preserve_institution_info"] is False
        assert profile["anonymize_study_descriptions"] is True
        assert profile["date_shift_range"] == (30, 730)

    def test_get_hipaa_profile(self):
        """Test retrieval of HIPAA anonymization profile."""
        profile = AnonymizationProfiles.get_profile("hipaa")

        assert profile["name"] == "hipaa"
        assert profile["remove_dates"] is True
        assert profile["preserve_structure_names"] is False
        assert profile["age_threshold"] == 89
        assert profile["compliance_level"] == "HIPAA"

    def test_invalid_profile_name(self):
        """Test error handling for invalid profile names."""
        with pytest.raises(ValueError, match="Unknown profile"):
            AnonymizationProfiles.get_profile("invalid_profile")

    def test_list_profiles(self):
        """Test listing all available profiles."""
        profiles = AnonymizationProfiles.list_profiles()

        assert len(profiles) == 4
        profile_names = [p["name"] for p in profiles]
        assert "basic" in profile_names
        assert "clinical" in profile_names
        assert "research" in profile_names
        assert "hipaa" in profile_names

    def test_validate_profile(self):
        """Test profile validation functionality."""
        valid_profile = {
            "name": "test",
            "description": "Test profile",
            "remove_patient_identifiers": True,
            "preserve_geometric_data": True,
            "preserve_dose_data": True,
            "preserve_tags": ["DoseGridScaling", "DoseUnits"],
        }

        assert AnonymizationProfiles.validate_profile(valid_profile) is True

    def test_validate_invalid_profile(self):
        """Test validation of invalid profile."""
        invalid_profile = {
            "name": "test",
            # Missing required fields
        }

        with pytest.raises(ValueError, match="missing required fields"):
            AnonymizationProfiles.validate_profile(invalid_profile)


class TestAnonymizationAuditor:
    """Test audit logging functionality."""

    def setup_method(self):
        """Set up test fixtures."""
        self.temp_dir = tempfile.mkdtemp()
        self.log_file = Path(self.temp_dir) / "test_audit.log"
        self.auditor = AnonymizationAuditor(log_file=self.log_file)

    def teardown_method(self):
        """Clean up test fixtures."""
        shutil.rmtree(self.temp_dir)

    def test_auditor_initialization(self):
        """Test auditor initialization."""
        assert self.auditor.log_file == self.log_file
        assert self.auditor.include_metadata is True
        assert self.log_file.parent.exists()

    def test_log_anonymization(self):
        """Test logging of anonymization operation."""
        # Create test datasets
        original_ds = self._create_test_dataset(
            patient_name="Doe^John", patient_id="12345", modality="CT"
        )

        anonymized_ds = self._create_test_dataset(
            patient_name="ANONYMOUS", patient_id="ANON", modality="CT"
        )

        # Log anonymization
        audit_entry = self.auditor.log_anonymization(
            original_ds, anonymized_ds, profile_name="basic"
        )

        # Verify audit entry
        assert "log_id" in audit_entry
        assert audit_entry["operation"] == "anonymization"
        assert audit_entry["profile_used"] == "basic"
        assert "PatientName" in audit_entry["tags_modified"]
        assert "PatientID" in audit_entry["tags_modified"]
        assert audit_entry["privacy_risk_score"] >= 0.0

    def test_extract_audit_metadata(self):
        """Test extraction of audit metadata."""
        dataset = self._create_test_dataset(
            patient_name="Test^Patient", patient_id="TEST001", modality="RTDOSE"
        )
        dataset.DoseGridScaling = 0.001
        dataset.DoseUnits = "GY"

        metadata = self.auditor._extract_audit_metadata(dataset)

        assert metadata["modality"] == "RTDOSE"
        assert metadata["patient_id"] == "TEST001"
        assert metadata["dose_grid_scaling"] == "0.001"
        assert metadata["dose_units"] == "GY"

    def test_privacy_risk_calculation(self):
        """Test privacy risk score calculation."""
        # High risk dataset (many identifiers)
        high_risk_ds = self._create_test_dataset(
            patient_name="Doe^John^Middle", patient_id="REAL_ID_123", modality="CT"
        )
        high_risk_ds.InstitutionName = "Real Hospital"
        high_risk_ds.StudyDate = "20240101"

        high_risk_score = self.auditor._calculate_privacy_risk(high_risk_ds)

        # Low risk dataset (anonymized)
        low_risk_ds = self._create_test_dataset(
            patient_name="ANONYMOUS", patient_id="ANON", modality="CT"
        )

        low_risk_score = self.auditor._calculate_privacy_risk(low_risk_ds)

        assert high_risk_score > low_risk_score
        assert low_risk_score == 0.0

    def _create_test_dataset(
        self, patient_name="Test", patient_id="TEST", modality="CT"
    ):
        """Create test DICOM dataset."""
        ds = Dataset()
        ds.PatientName = patient_name
        ds.PatientID = patient_id
        ds.Modality = modality
        ds.SOPClassUID = "1.2.840.10008.*******.1.2"  # CT Image Storage
        ds.SOPInstanceUID = generate_uid()
        ds.StudyInstanceUID = generate_uid()
        ds.SeriesInstanceUID = generate_uid()

        # Add required DICOM metadata for proper encoding
        ds.is_little_endian = True
        ds.is_implicit_VR = True
        ds.file_meta = Dataset()
        ds.file_meta.TransferSyntaxUID = "1.2.840.10008.1.2"
        ds.file_meta.MediaStorageSOPClassUID = ds.SOPClassUID
        ds.file_meta.MediaStorageSOPInstanceUID = ds.SOPInstanceUID

        return ds


class TestDicomAnonymizer:
    """Test core DICOM anonymization functionality."""

    def setup_method(self):
        """Set up test fixtures."""
        self.temp_dir = tempfile.mkdtemp()
        self.auditor = AnonymizationAuditor(
            log_file=Path(self.temp_dir) / "test_audit.log"
        )
        self.anonymizer = DicomAnonymizer(profile="basic", auditor=self.auditor)

    def teardown_method(self):
        """Clean up test fixtures."""
        shutil.rmtree(self.temp_dir)

    def test_anonymizer_initialization(self):
        """Test anonymizer initialization."""
        assert self.anonymizer.profile["name"] == "basic"
        assert self.anonymizer.auditor is not None
        assert isinstance(self.anonymizer._uid_mapping, dict)

    def test_basic_anonymization(self):
        """Test basic patient identifier anonymization."""
        # Create test dataset
        original_ds = self._create_test_dataset(
            patient_name="Doe^John",
            patient_id="REAL_ID_123",
            patient_birth_date="19800101",
        )

        # Anonymize
        anonymized_ds = self.anonymizer.anonymize_dataset(original_ds)

        # Verify anonymization
        assert anonymized_ds.PatientName == "ANONYMOUS"
        assert anonymized_ds.PatientID.startswith("ANON_")
        assert anonymized_ds.PatientBirthDate == ""

        # Verify original is unchanged
        assert original_ds.PatientName == "Doe^John"
        assert original_ds.PatientID == "REAL_ID_123"

    def test_rt_dose_preservation(self):
        """Test preservation of critical RT dose data."""
        # Create RT Dose dataset
        dose_ds = self._create_test_dataset(modality="RTDOSE")
        dose_ds.DoseGridScaling = 0.001
        dose_ds.DoseUnits = "GY"
        dose_ds.PixelSpacing = [2.0, 2.0]

        # Anonymize
        anonymized_ds = self.anonymizer.anonymize_dataset(dose_ds)

        # Verify critical RT data is preserved
        assert anonymized_ds.DoseGridScaling == 0.001
        assert anonymized_ds.DoseUnits == "GY"
        assert anonymized_ds.PixelSpacing == [2.0, 2.0]
        assert anonymized_ds.Modality == "RTDOSE"

    def test_uid_consistency(self):
        """Test consistent UID mapping across multiple datasets."""
        # Create two datasets with same Study UID
        study_uid = generate_uid()

        ds1 = self._create_test_dataset()
        ds1.StudyInstanceUID = study_uid

        ds2 = self._create_test_dataset()
        ds2.StudyInstanceUID = study_uid

        # Anonymize both
        anon_ds1 = self.anonymizer.anonymize_dataset(ds1)
        anon_ds2 = self.anonymizer.anonymize_dataset(ds2)

        # Verify UIDs are consistently mapped
        assert anon_ds1.StudyInstanceUID == anon_ds2.StudyInstanceUID
        assert anon_ds1.StudyInstanceUID != study_uid

    def test_clinical_profile_date_shifting(self):
        """Test date shifting with clinical profile."""
        clinical_anonymizer = DicomAnonymizer(profile="clinical", auditor=self.auditor)

        # Create dataset with dates
        ds = self._create_test_dataset()
        ds.StudyDate = "20240101"
        ds.SeriesDate = "20240101"

        # Anonymize
        anonymized_ds = clinical_anonymizer.anonymize_dataset(ds)

        # Verify dates are shifted but not removed
        assert anonymized_ds.StudyDate != "20240101"
        assert anonymized_ds.StudyDate != ""
        assert len(anonymized_ds.StudyDate) == 8  # YYYYMMDD format

    def test_hipaa_profile_strict_anonymization(self):
        """Test HIPAA profile strict anonymization."""
        hipaa_anonymizer = DicomAnonymizer(profile="hipaa", auditor=self.auditor)

        # Create dataset with various identifiers
        ds = self._create_test_dataset()
        ds.StudyDate = "20240101"
        ds.InstitutionName = "Test Hospital"
        ds.ReferringPhysicianName = "Dr. Smith"

        # Anonymize
        anonymized_ds = hipaa_anonymizer.anonymize_dataset(ds)

        # Verify strict anonymization
        assert anonymized_ds.StudyDate == ""
        assert anonymized_ds.InstitutionName == ""
        assert anonymized_ds.ReferringPhysicianName == ""

    def _create_test_dataset(
        self,
        patient_name="Test",
        patient_id="TEST",
        patient_birth_date="19800101",
        modality="CT",
    ):
        """Create test DICOM dataset."""
        ds = Dataset()
        ds.PatientName = patient_name
        ds.PatientID = patient_id
        ds.PatientBirthDate = patient_birth_date
        ds.Modality = modality
        ds.SOPClassUID = "1.2.840.10008.*******.1.2"
        ds.SOPInstanceUID = generate_uid()
        ds.StudyInstanceUID = generate_uid()
        ds.SeriesInstanceUID = generate_uid()
        ds.FrameOfReferenceUID = generate_uid()

        # Add required DICOM metadata for proper encoding
        ds.is_little_endian = True
        ds.is_implicit_VR = True
        ds.file_meta = Dataset()
        ds.file_meta.TransferSyntaxUID = (
            "1.2.840.10008.1.2"  # Implicit VR Little Endian
        )
        ds.file_meta.MediaStorageSOPClassUID = ds.SOPClassUID
        ds.file_meta.MediaStorageSOPInstanceUID = ds.SOPInstanceUID

        return ds


class TestTreatmentPlanAnonymizer:
    """Test treatment plan anonymization functionality."""

    def setup_method(self):
        """Set up test fixtures."""
        self.temp_dir = tempfile.mkdtemp()
        self.input_dir = Path(self.temp_dir) / "input"
        self.output_dir = Path(self.temp_dir) / "output"
        self.input_dir.mkdir()
        self.output_dir.mkdir()

        self.auditor = AnonymizationAuditor(
            log_file=Path(self.temp_dir) / "test_audit.log"
        )
        self.plan_anonymizer = TreatmentPlanAnonymizer(
            profile="clinical", auditor=self.auditor
        )

    def teardown_method(self):
        """Clean up test fixtures."""
        shutil.rmtree(self.temp_dir)

    def test_plan_anonymizer_initialization(self):
        """Test treatment plan anonymizer initialization."""
        assert self.plan_anonymizer.profile["name"] == "clinical"
        assert isinstance(self.plan_anonymizer._plan_uid_mapping, dict)

    def test_create_plan_anonymization_map(self):
        """Test creation of consistent anonymization mapping."""
        # Create test files with shared UIDs
        study_uid = generate_uid()
        frame_uid = generate_uid()

        ct_ds = self._create_test_dataset(modality="CT")
        ct_ds.StudyInstanceUID = study_uid
        ct_ds.FrameOfReferenceUID = frame_uid

        struct_ds = self._create_test_dataset(modality="RTSTRUCT")
        struct_ds.StudyInstanceUID = study_uid
        struct_ds.FrameOfReferenceUID = frame_uid

        # Save test files
        ct_file = self.input_dir / "ct.dcm"
        struct_file = self.input_dir / "struct.dcm"
        ct_ds.save_as(ct_file)
        struct_ds.save_as(struct_file)

        # Create anonymization mapping
        plan_files = {"ct": ct_file, "struct": struct_file}
        self.plan_anonymizer._create_plan_anonymization_map(plan_files)

        # Verify consistent mapping
        assert study_uid in self.plan_anonymizer._uid_mapping
        assert frame_uid in self.plan_anonymizer._uid_mapping
        assert self.plan_anonymizer._uid_mapping[study_uid] != study_uid
        assert self.plan_anonymizer._uid_mapping[frame_uid] != frame_uid

    def test_anonymize_treatment_plan(self):
        """Test complete treatment plan anonymization."""
        # Create test treatment plan files
        study_uid = generate_uid()
        frame_uid = generate_uid()

        # CT dataset
        ct_ds = self._create_test_dataset(modality="CT")
        ct_ds.StudyInstanceUID = study_uid
        ct_ds.FrameOfReferenceUID = frame_uid
        ct_ds.PixelSpacing = [1.0, 1.0]

        # Structure set
        struct_ds = self._create_test_dataset(modality="RTSTRUCT")
        struct_ds.StudyInstanceUID = study_uid
        struct_ds.FrameOfReferenceUID = frame_uid

        # Dose dataset
        dose_ds = self._create_test_dataset(modality="RTDOSE")
        dose_ds.StudyInstanceUID = study_uid
        dose_ds.FrameOfReferenceUID = frame_uid
        dose_ds.DoseGridScaling = 0.001
        dose_ds.DoseUnits = "GY"

        # Save test files
        ct_file = self.input_dir / "ct.dcm"
        struct_file = self.input_dir / "struct.dcm"
        dose_file = self.input_dir / "dose.dcm"

        ct_ds.save_as(ct_file)
        struct_ds.save_as(struct_file)
        dose_ds.save_as(dose_file)

        # Anonymize treatment plan
        plan_files = {"ct": ct_file, "struct": struct_file, "dose": dose_file}

        result = self.plan_anonymizer.anonymize_treatment_plan(
            plan_files, self.output_dir
        )

        # Verify all files were anonymized
        assert "ct" in result
        assert "struct" in result
        assert "dose" in result
        assert all(path.exists() for path in result.values())

        # Verify anonymized files have consistent UIDs
        anon_ct = pydicom.dcmread(result["ct"])
        anon_struct = pydicom.dcmread(result["struct"])
        anon_dose = pydicom.dcmread(result["dose"])

        assert anon_ct.StudyInstanceUID == anon_struct.StudyInstanceUID
        assert anon_ct.StudyInstanceUID == anon_dose.StudyInstanceUID
        assert anon_ct.FrameOfReferenceUID == anon_struct.FrameOfReferenceUID
        assert anon_ct.FrameOfReferenceUID == anon_dose.FrameOfReferenceUID

        # Verify critical data is preserved
        assert anon_dose.DoseGridScaling == 0.001
        assert anon_dose.DoseUnits == "GY"
        assert anon_ct.PixelSpacing == [1.0, 1.0]

    def test_validate_plan_relationships(self):
        """Test validation of treatment plan relationships."""
        # Create test files with consistent UIDs
        study_uid = generate_uid()
        frame_uid = generate_uid()

        ct_ds = self._create_test_dataset(modality="CT")
        ct_ds.StudyInstanceUID = study_uid
        ct_ds.FrameOfReferenceUID = frame_uid

        struct_ds = self._create_test_dataset(modality="RTSTRUCT")
        struct_ds.StudyInstanceUID = study_uid
        struct_ds.FrameOfReferenceUID = frame_uid

        # Save test files
        ct_file = self.output_dir / "ct.dcm"
        struct_file = self.output_dir / "struct.dcm"
        ct_ds.save_as(ct_file)
        struct_ds.save_as(struct_file)

        # Validate relationships (should pass)
        anonymized_files = {"ct": ct_file, "struct": struct_file}
        self.plan_anonymizer._validate_plan_relationships(anonymized_files)

        # Test with mismatched Frame of Reference UID (should fail)
        struct_ds.FrameOfReferenceUID = generate_uid()  # Different UID
        struct_ds.save_as(struct_file)

        with pytest.raises(ValueError, match="Frame of Reference UID mismatch"):
            self.plan_anonymizer._validate_plan_relationships(anonymized_files)

    def test_anonymize_directory(self):
        """Test batch anonymization of directory."""
        # Create multiple test files
        for i in range(3):
            ds = self._create_test_dataset(
                patient_name=f"Patient{i}", patient_id=f"ID{i}", modality="CT"
            )
            ds.save_as(self.input_dir / f"file_{i}.dcm")

        # Anonymize directory
        result = self.plan_anonymizer.anonymize_directory(
            self.input_dir, self.output_dir
        )

        # Verify results
        assert "CT" in result
        assert len(result["CT"]) == 3
        assert all(path.exists() for path in result["CT"])

        # Verify anonymization
        for anon_file in result["CT"]:
            ds = pydicom.dcmread(anon_file)
            assert ds.PatientName == "ANONYMOUS"
            assert ds.PatientID.startswith("ANON_")

    def test_missing_files_handling(self):
        """Test handling of missing files in treatment plan."""
        # Create plan with missing files
        plan_files = {
            "ct": self.input_dir / "missing_ct.dcm",
            "struct": None,
            "dose": self.input_dir / "missing_dose.dcm",
        }

        # Should handle missing files gracefully
        result = self.plan_anonymizer.anonymize_treatment_plan(
            plan_files, self.output_dir
        )

        # Should return empty result for missing files
        assert len(result) == 0

    def _create_test_dataset(
        self,
        patient_name="Test",
        patient_id="TEST",
        patient_birth_date="19800101",
        modality="CT",
    ):
        """Create test DICOM dataset."""
        ds = Dataset()
        ds.PatientName = patient_name
        ds.PatientID = patient_id
        ds.PatientBirthDate = patient_birth_date
        ds.Modality = modality
        ds.SOPClassUID = "1.2.840.10008.*******.1.2"
        ds.SOPInstanceUID = generate_uid()
        ds.StudyInstanceUID = generate_uid()
        ds.SeriesInstanceUID = generate_uid()
        ds.FrameOfReferenceUID = generate_uid()

        # Add required DICOM metadata for proper encoding
        ds.is_little_endian = True
        ds.is_implicit_VR = True
        ds.file_meta = Dataset()
        ds.file_meta.TransferSyntaxUID = "1.2.840.10008.1.2"
        ds.file_meta.MediaStorageSOPClassUID = ds.SOPClassUID
        ds.file_meta.MediaStorageSOPInstanceUID = ds.SOPInstanceUID

        return ds


class TestAnonymizationIntegration:
    """Integration tests for complete anonymization workflows."""

    def setup_method(self):
        """Set up test fixtures."""
        self.temp_dir = tempfile.mkdtemp()
        self.auditor = AnonymizationAuditor(
            log_file=Path(self.temp_dir) / "integration_audit.log"
        )

    def teardown_method(self):
        """Clean up test fixtures."""
        shutil.rmtree(self.temp_dir)

    def test_end_to_end_anonymization_workflow(self):
        """Test complete anonymization workflow from original to anonymized data."""
        # Create original dataset with realistic data
        original_ds = Dataset()
        original_ds.PatientName = "Doe^John^Middle"
        original_ds.PatientID = "REAL_PATIENT_123"
        original_ds.PatientBirthDate = "19800515"
        original_ds.StudyDate = "20240101"
        original_ds.Modality = "RTDOSE"
        original_ds.SOPClassUID = "1.2.840.10008.*******.1.481.2"  # RT Dose Storage
        original_ds.SOPInstanceUID = generate_uid()
        original_ds.StudyInstanceUID = generate_uid()
        original_ds.SeriesInstanceUID = generate_uid()
        original_ds.FrameOfReferenceUID = generate_uid()
        original_ds.DoseGridScaling = 0.001
        original_ds.DoseUnits = "GY"
        original_ds.InstitutionName = "Real Medical Center"
        original_ds.ReferringPhysicianName = "Dr. Smith^Jane"

        # Test different anonymization profiles
        profiles_to_test = ["basic", "clinical", "research", "hipaa"]

        for profile_name in profiles_to_test:
            anonymizer = DicomAnonymizer(profile=profile_name, auditor=self.auditor)
            anonymized_ds = anonymizer.anonymize_dataset(original_ds)

            # Verify patient identifiers are anonymized
            assert anonymized_ds.PatientName == "ANONYMOUS"
            assert anonymized_ds.PatientID.startswith("ANON_")

            # Verify critical RT data is preserved
            assert anonymized_ds.DoseGridScaling == 0.001
            assert anonymized_ds.DoseUnits == "GY"
            assert anonymized_ds.Modality == "RTDOSE"

            # Profile-specific verification
            if profile_name == "hipaa":
                assert anonymized_ds.StudyDate == ""
                assert anonymized_ds.InstitutionName == ""
            elif profile_name == "clinical":
                assert anonymized_ds.StudyDate != "20240101"  # Should be shifted
                assert anonymized_ds.StudyDate != ""  # But not removed

    def test_audit_trail_completeness(self):
        """Test that audit trail captures all necessary information."""
        # Create and anonymize dataset
        original_ds = self._create_test_dataset()
        anonymizer = DicomAnonymizer(profile="clinical", auditor=self.auditor)
        anonymized_ds = anonymizer.anonymize_dataset(original_ds)

        # Verify audit log file exists and contains data
        assert self.auditor.log_file.exists()

        # Generate compliance report
        report = self.auditor.generate_compliance_report()

        # Verify report structure
        assert "report_id" in report
        assert "generation_date" in report
        assert "total_operations" in report
        assert "compliance_summary" in report
        assert "risk_assessment" in report

    def _create_test_dataset(self):
        """Create test DICOM dataset."""
        ds = Dataset()
        ds.PatientName = "Test^Patient"
        ds.PatientID = "TEST_123"
        ds.PatientBirthDate = "19800101"
        ds.Modality = "CT"
        ds.SOPClassUID = "1.2.840.10008.*******.1.2"
        ds.SOPInstanceUID = generate_uid()
        ds.StudyInstanceUID = generate_uid()
        ds.SeriesInstanceUID = generate_uid()

        # Add required DICOM metadata for proper encoding
        ds.is_little_endian = True
        ds.is_implicit_VR = True
        ds.file_meta = Dataset()
        ds.file_meta.TransferSyntaxUID = "1.2.840.10008.1.2"
        ds.file_meta.MediaStorageSOPClassUID = ds.SOPClassUID
        ds.file_meta.MediaStorageSOPInstanceUID = ds.SOPInstanceUID

        return ds
