"""
Tests for RT Plan DICOM Compliance Validation.

This module tests the RT Plan IOD validation functionality including
mandatory elements, plan-specific validation, beam sequences, fraction
groups, and TPS compatibility checks.
"""

import pytest
from pydicom.dataset import Dataset
from pydicom.uid import RTPlanStorage

from pyrt_dicom.validation.dicom_compliance import (
    DicomComplianceValidator,
    IODType,
    ValidationLevel,
    validate_dicom_compliance,
)
from pyrt_dicom.core.rt_plan import RTPlan


class TestRTPlanComplianceValidation:
    """Test suite for RT Plan DICOM compliance validation."""

    def test_validate_rt_plan_iod_valid_plan(self):
        """Test validation of valid RT Plan dataset."""
        # Create valid RT Plan
        prescription = {
            'prescription_dose': 7000,
            'fractions': 35,
            'beams': [
                {
                    'name': 'AP',
                    'energy': 18,
                    'gantry_angle': 0,
                    'dose_weight': 0.33,
                    'beam_type': 'STATIC',
                    'radiation_type': 'PHOTON'
                },
                {
                    'name': 'RPO',
                    'energy': 18,
                    'gantry_angle': 120,
                    'dose_weight': 0.33,
                    'beam_type': 'STATIC',
                    'radiation_type': 'PHOTON'
                },
                {
                    'name': 'LPO',
                    'energy': 18,
                    'gantry_angle': 240,
                    'dose_weight': 0.34,
                    'beam_type': 'STATIC',
                    'radiation_type': 'PHOTON'
                }
            ]
        }
        
        rt_plan = RTPlan.from_beam_config(
            prescription=prescription,
            patient_info={'PatientID': 'TEST001', 'PatientName': 'Test^Patient'},
            plan_name='Test Plan',
            treatment_intent='CURATIVE'
        )
        
        dataset = rt_plan._create_modality_specific_dataset()
        
        validator = DicomComplianceValidator()
        results = validator.validate_rt_plan_iod(dataset)
        
        # Should have no errors, only informational messages
        errors = [r for r in results if r.level == ValidationLevel.ERROR]
        assert len(errors) == 0, f"Found validation errors: {[e.message for e in errors]}"
        
        # Check that basic validation passes
        warnings = [r for r in results if r.level == ValidationLevel.WARNING]
        assert len(warnings) == 0, f"Found validation warnings: {[w.message for w in warnings]}"

    def test_validate_rt_plan_iod_missing_mandatory_elements(self):
        """Test validation with missing mandatory elements."""
        # Create minimal dataset missing required elements
        dataset = Dataset()
        dataset.SOPClassUID = RTPlanStorage
        dataset.Modality = "RTPLAN"
        
        validator = DicomComplianceValidator()
        results = validator.validate_rt_plan_iod(dataset)
        
        # Should have multiple errors for missing mandatory elements
        errors = [r for r in results if r.level == ValidationLevel.ERROR]
        assert len(errors) > 0
        
        # Check for specific mandatory elements
        error_messages = [e.message for e in errors]
        mandatory_elements = ['SOPInstanceUID', 'PatientID', 'StudyInstanceUID']
        for element in mandatory_elements:
            assert any(element in msg for msg in error_messages), f"Missing error for {element}"

    def test_validate_plan_modules_invalid_intent(self):
        """Test validation of plan intent values."""
        dataset = Dataset()
        dataset.SOPClassUID = RTPlanStorage
        dataset.PlanIntent = "INVALID_INTENT"
        
        validator = DicomComplianceValidator()
        results = validator._validate_plan_modules(dataset)
        
        warnings = [r for r in results if r.level == ValidationLevel.WARNING]
        assert len(warnings) == 1
        assert "Unusual plan intent" in warnings[0].message
        assert warnings[0].parameter == "PlanIntent"

    def test_validate_plan_modules_invalid_geometry(self):
        """Test validation of RT Plan geometry."""
        dataset = Dataset()
        dataset.SOPClassUID = RTPlanStorage
        dataset.RTPlanGeometry = "INVALID_GEOMETRY"
        
        validator = DicomComplianceValidator()
        results = validator._validate_plan_modules(dataset)
        
        errors = [r for r in results if r.level == ValidationLevel.ERROR]
        assert len(errors) == 1
        assert "Invalid RT Plan geometry" in errors[0].message
        assert errors[0].parameter == "RTPlanGeometry"

    def test_validate_plan_modules_long_label(self):
        """Test validation of RT Plan label length."""
        dataset = Dataset()
        dataset.SOPClassUID = RTPlanStorage
        dataset.RTPlanLabel = "This is a very long plan label that exceeds 16 characters"
        
        validator = DicomComplianceValidator()
        results = validator._validate_plan_modules(dataset)
        
        errors = [r for r in results if r.level == ValidationLevel.ERROR]
        assert len(errors) == 1
        assert "RT Plan label too long" in errors[0].message
        assert errors[0].parameter == "RTPlanLabel"

    def test_validate_beam_sequences_missing(self):
        """Test validation with missing beam sequence."""
        dataset = Dataset()
        dataset.SOPClassUID = RTPlanStorage
        
        validator = DicomComplianceValidator()
        results = validator._validate_beam_sequences(dataset)
        
        errors = [r for r in results if r.level == ValidationLevel.ERROR]
        assert len(errors) == 1
        assert "Missing mandatory BeamSequence" in errors[0].message

    def test_validate_beam_sequences_empty(self):
        """Test validation with empty beam sequence."""
        dataset = Dataset()
        dataset.SOPClassUID = RTPlanStorage
        dataset.BeamSequence = []
        
        validator = DicomComplianceValidator()
        results = validator._validate_beam_sequences(dataset)
        
        errors = [r for r in results if r.level == ValidationLevel.ERROR]
        assert len(errors) == 1
        assert "Empty BeamSequence" in errors[0].message

    def test_validate_beam_sequence_item_missing_elements(self):
        """Test validation of beam sequence item with missing elements."""
        beam_item = Dataset()
        beam_item.BeamName = "Test Beam"
        # Missing other required elements
        
        validator = DicomComplianceValidator()
        results = validator._validate_beam_sequence_item(beam_item, 0)
        
        errors = [r for r in results if r.level == ValidationLevel.ERROR]
        assert len(errors) >= 3  # Missing BeamNumber, BeamType, RadiationType, PrimaryDosimeterUnit
        
        error_messages = [e.message for e in errors]
        required_elements = ['BeamNumber', 'BeamType', 'RadiationType']
        for element in required_elements:
            assert any(element in msg for msg in error_messages), f"Missing error for {element}"

    def test_validate_beam_sequence_item_invalid_types(self):
        """Test validation of beam sequence item with invalid types."""
        beam_item = Dataset()
        beam_item.BeamNumber = "1"
        beam_item.BeamName = "Test Beam"
        beam_item.BeamType = "INVALID_TYPE"
        beam_item.RadiationType = "INVALID_RADIATION"
        beam_item.PrimaryDosimeterUnit = "MU"
        
        validator = DicomComplianceValidator()
        results = validator._validate_beam_sequence_item(beam_item, 0)
        
        warnings = [r for r in results if r.level == ValidationLevel.WARNING]
        assert len(warnings) == 2  # Invalid beam type and radiation type
        
        warning_messages = [w.message for w in warnings]
        assert any("Unusual beam type" in msg for msg in warning_messages)
        assert any("Unusual radiation type" in msg for msg in warning_messages)

    def test_validate_fraction_group_sequences_missing(self):
        """Test validation with missing fraction group sequence."""
        dataset = Dataset()
        dataset.SOPClassUID = RTPlanStorage
        
        validator = DicomComplianceValidator()
        results = validator._validate_fraction_group_sequences(dataset)
        
        errors = [r for r in results if r.level == ValidationLevel.ERROR]
        assert len(errors) == 1
        assert "Missing mandatory FractionGroupSequence" in errors[0].message

    def test_validate_fraction_group_sequences_empty(self):
        """Test validation with empty fraction group sequence."""
        dataset = Dataset()
        dataset.SOPClassUID = RTPlanStorage
        dataset.FractionGroupSequence = []
        
        validator = DicomComplianceValidator()
        results = validator._validate_fraction_group_sequences(dataset)
        
        errors = [r for r in results if r.level == ValidationLevel.ERROR]
        assert len(errors) == 1
        assert "Empty FractionGroupSequence" in errors[0].message

    def test_validate_fraction_group_item_missing_elements(self):
        """Test validation of fraction group item with missing elements."""
        fraction_item = Dataset()
        # Missing required elements
        
        validator = DicomComplianceValidator()
        results = validator._validate_fraction_group_item(fraction_item, 0)
        
        errors = [r for r in results if r.level == ValidationLevel.ERROR]
        assert len(errors) == 2  # Missing FractionGroupNumber and NumberOfFractionsPlanned
        
        error_messages = [e.message for e in errors]
        required_elements = ['FractionGroupNumber', 'NumberOfFractionsPlanned']
        for element in required_elements:
            assert any(element in msg for msg in error_messages), f"Missing error for {element}"

    def test_validate_fraction_group_item_invalid_fractions(self):
        """Test validation of fraction group item with invalid fraction count."""
        # Test zero fractions
        fraction_item = Dataset()
        fraction_item.FractionGroupNumber = "1"
        fraction_item.NumberOfFractionsPlanned = "0"
        
        validator = DicomComplianceValidator()
        results = validator._validate_fraction_group_item(fraction_item, 0)
        
        errors = [r for r in results if r.level == ValidationLevel.ERROR]
        assert len(errors) == 1
        assert "Invalid number of fractions" in errors[0].message
        
        # Test very high fraction count
        fraction_item.NumberOfFractionsPlanned = "100"
        results = validator._validate_fraction_group_item(fraction_item, 0)
        
        warnings = [r for r in results if r.level == ValidationLevel.WARNING]
        assert len(warnings) == 1
        assert "Unusually high number of fractions" in warnings[0].message

    def test_validate_plan_tps_compatibility_missing_elements(self):
        """Test TPS compatibility validation with missing recommended elements."""
        dataset = Dataset()
        dataset.SOPClassUID = RTPlanStorage
        # Missing recommended TPS elements
        
        validator = DicomComplianceValidator()
        results = validator._validate_plan_tps_compatibility(dataset)
        
        info_results = [r for r in results if r.level == ValidationLevel.INFO]
        assert len(info_results) >= 2  # Missing Manufacturer, SoftwareVersions, etc.
        
        info_messages = [i.message for i in info_results]
        recommended_elements = ['Manufacturer', 'SoftwareVersions']
        for element in recommended_elements:
            assert any(element in msg for msg in info_messages), f"Missing info for {element}"

    def test_validate_plan_tps_compatibility_special_beam_names(self):
        """Test TPS compatibility validation with special characters in beam names."""
        from pydicom.sequence import Sequence
        
        dataset = Dataset()
        dataset.SOPClassUID = RTPlanStorage
        
        # Create beam with special characters
        beam_item = Dataset()
        beam_item.BeamName = "Beam@#$%"
        
        dataset.BeamSequence = Sequence([beam_item])
        
        validator = DicomComplianceValidator()
        results = validator._validate_plan_tps_compatibility(dataset)
        
        warnings = [r for r in results if r.level == ValidationLevel.WARNING]
        assert len(warnings) == 1
        assert "contains special characters" in warnings[0].message

    def test_validate_plan_tps_compatibility_duplicate_beam_names(self):
        """Test TPS compatibility validation with duplicate beam names."""
        from pydicom.sequence import Sequence
        
        dataset = Dataset()
        dataset.SOPClassUID = RTPlanStorage
        
        # Create beams with duplicate names
        beam_item1 = Dataset()
        beam_item1.BeamName = "DuplicateName"
        
        beam_item2 = Dataset()
        beam_item2.BeamName = "DuplicateName"
        
        dataset.BeamSequence = Sequence([beam_item1, beam_item2])
        
        validator = DicomComplianceValidator()
        results = validator._validate_plan_tps_compatibility(dataset)
        
        errors = [r for r in results if r.level == ValidationLevel.ERROR]
        assert len(errors) == 1
        assert "Duplicate beam names found" in errors[0].message

    def test_validate_dicom_compliance_function(self):
        """Test the validate_dicom_compliance convenience function."""
        # Create valid RT Plan
        prescription = {
            'prescription_dose': 2000,
            'fractions': 10,
            'beams': [
                {
                    'name': 'Test Beam',
                    'energy': 6,
                    'gantry_angle': 0,
                    'dose_weight': 1.0,
                    'beam_type': 'STATIC',
                    'radiation_type': 'PHOTON'
                }
            ]
        }
        
        rt_plan = RTPlan.from_beam_config(
            prescription=prescription,
            patient_info={'PatientID': 'TEST001'},
            plan_name='Test'
        )
        
        dataset = rt_plan._create_modality_specific_dataset()
        
        # Test the convenience function
        results = validate_dicom_compliance(dataset, IODType.RT_PLAN)
        
        # Should have no errors
        errors = [r for r in results if r.level == ValidationLevel.ERROR]
        assert len(errors) == 0, f"Found validation errors: {[e.message for e in errors]}"

    def test_rt_plan_iod_elements_completeness(self):
        """Test that RT Plan IOD elements are properly defined."""
        validator = DicomComplianceValidator()
        
        # Check that plan elements are built
        assert hasattr(validator, 'plan_elements')
        assert len(validator.plan_elements) > 0
        
        # Check for key RT Plan elements (using string keys as stored in lookup)
        required_tags = [
            "0008,0016",  # SOPClassUID
            "0008,0018",  # SOPInstanceUID
            "0010,0020",  # PatientID
            "300A,0002",  # RTPlanLabel
            "300A,0070",  # FractionGroupSequence
            "300A,00B0",  # BeamSequence
        ]
        
        for tag in required_tags:
            assert tag in validator.plan_elements, f"Missing RT Plan IOD element: {tag}"

    def test_rt_plan_sop_class_validation(self):
        """Test SOP Class UID validation for RT Plan."""
        dataset = Dataset()
        dataset.SOPClassUID = "1.2.840.10008.5.1.4.1.1.2"  # Wrong SOP Class (CT)
        
        validator = DicomComplianceValidator()
        results = validator._validate_sop_class(dataset, IODType.RT_PLAN)
        
        errors = [r for r in results if r.level == ValidationLevel.ERROR]
        assert len(errors) == 1
        assert "Incorrect SOP Class UID" in errors[0].message

    def test_clinical_workflow_integration(self):
        """Test RT Plan validation in complete clinical workflow."""
        # Create comprehensive RT Plan with all elements
        prescription = {
            'prescription_dose': 6000,  # 60 Gy
            'fractions': 30,
            'beams': [
                {
                    'name': 'AP',
                    'energy': 6,
                    'gantry_angle': 0,
                    'collimator_angle': 0,
                    'couch_angle': 0,
                    'dose_weight': 0.5,
                    'beam_type': 'STATIC',
                    'radiation_type': 'PHOTON'
                },
                {
                    'name': 'PA',
                    'energy': 6,
                    'gantry_angle': 180,
                    'collimator_angle': 0,
                    'couch_angle': 0,
                    'dose_weight': 0.5,
                    'beam_type': 'STATIC',
                    'radiation_type': 'PHOTON'
                }
            ]
        }
        
        rt_plan = RTPlan.from_beam_config(
            prescription=prescription,
            patient_info={
                'PatientID': 'CLINICAL001',
                'PatientName': 'Clinical^Test',
                'PatientBirthDate': '19700101',
                'PatientSex': 'M'
            },
            plan_name='Clinical Test',
            treatment_intent='CURATIVE'
        )
        
        # Create and validate dataset
        dataset = rt_plan._create_modality_specific_dataset()
        
        validator = DicomComplianceValidator(
            strict_conformance=True,
            tps_compatibility=True
        )
        results = validator.validate_rt_plan_iod(dataset)
        
        # Should pass validation with minimal issues
        errors = [r for r in results if r.level == ValidationLevel.ERROR]
        assert len(errors) == 0, f"Clinical workflow validation failed: {[e.message for e in errors]}"
        
        # Count different result types
        warnings = [r for r in results if r.level == ValidationLevel.WARNING]
        info_results = [r for r in results if r.level == ValidationLevel.INFO]
        
        # May have informational messages about optional elements (not required)
        # assert len(info_results) > 0, "Expected some informational validation messages"
        
        print(f"Clinical workflow validation: {len(errors)} errors, {len(warnings)} warnings, {len(info_results)} info")