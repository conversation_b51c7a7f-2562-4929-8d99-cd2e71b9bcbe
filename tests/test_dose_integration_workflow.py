"""
End-to-End RT Dose Integration Workflow Testing for Task 1.4.1.

This module tests complete clinical workflows combining CT series creation,
RT Structure Sets, and RT Dose objects with comprehensive Frame of Reference
UID consistency validation and multi-vendor compatibility testing.

Tests include:
- Complete CT + RTSTRUCT + RTDOSE creation pipeline
- Frame of Reference UID consistency across all RT objects
- Clinical workflow scenarios with realistic data
- DICOM compliance validation for complete treatment plans
- Performance benchmarking for end-to-end workflows
- Multi-vendor TPS compatibility validation
"""

import pytest
import numpy as np
import tempfile
import shutil
from pathlib import Path
import pydicom
from pydicom.dataset import Dataset
import time
import psutil
import os

from pyrt_dicom.core.ct_series import CTSeries
from pyrt_dicom.core.rt_struct import RTStructureSet
from pyrt_dicom.core.rt_dose import RTDose
from pyrt_dicom.validation.clinical import ClinicalValidator
from pyrt_dicom.validation.dicom_compliance import DicomComplianceValidator
from pyrt_dicom.validation.geometric import validate_dose_ct_alignment
from pyrt_dicom.utils.exceptions import ValidationError, DicomCreationError


@pytest.fixture
def temp_workflow_dir():
    """Create temporary directory for workflow testing."""
    temp_dir = tempfile.mkdtemp(prefix="pyrt_dose_workflow_")
    yield Path(temp_dir)
    shutil.rmtree(temp_dir)


@pytest.fixture
def clinical_ct_array():
    """Create realistic clinical CT array data with appropriate HU values."""
    # Simulate a clinical pelvis CT scan - (Z, Y, X) = (slices, rows, cols)
    ct_array = np.random.randint(-1000, 3000, size=(50, 256, 256), dtype=np.int16)
    
    # Add realistic HU values for different tissues
    # Air: -1000 HU
    ct_array[:, :30, :] = -1000
    
    # Soft tissue: 0-100 HU  
    ct_array[:, 30:220, 30:220] = np.random.randint(0, 100, size=(50, 190, 190))
    
    # Bone: 500-1500 HU
    ct_array[:, 100:150, 100:150] = np.random.randint(500, 1500, size=(50, 50, 50))
    
    return ct_array


@pytest.fixture
def clinical_structure_masks():
    """Create realistic clinical structure masks for prostate treatment."""
    masks = {}
    
    # PTV (Planning Target Volume) - central region
    ptv_mask = np.zeros((50, 256, 256), dtype=bool)
    ptv_mask[15:35, 110:150, 110:150] = True
    masks['PTV_7000'] = ptv_mask
    
    # CTV (Clinical Target Volume) - slightly smaller than PTV
    ctv_mask = np.zeros((50, 256, 256), dtype=bool)
    ctv_mask[17:33, 115:145, 115:145] = True
    masks['CTV_7000'] = ctv_mask
    
    # Organs at risk
    # Rectum - posterior to target
    rectum_mask = np.zeros((50, 256, 256), dtype=bool)
    rectum_mask[15:35, 90:110, 120:140] = True
    masks['Rectum'] = rectum_mask
    
    # Bladder - anterior to target
    bladder_mask = np.zeros((50, 256, 256), dtype=bool)
    bladder_mask[15:35, 150:180, 110:150] = True
    masks['Bladder'] = bladder_mask
    
    return masks


@pytest.fixture
def clinical_dose_array():
    """Create realistic clinical dose distribution."""
    # Dose grid matching CT geometry - (Z, Y, X) = (slices, rows, cols)
    dose_array = np.zeros((50, 256, 256), dtype=np.float64)
    
    # Create realistic dose distribution with maximum around 70 Gy
    center_z, center_y, center_x = 25, 130, 130
    
    # Create distance map for dose falloff
    z_indices, y_indices, x_indices = np.mgrid[0:50, 0:256, 0:256]
    
    # Calculate distance from isocenter
    distance = np.sqrt(
        ((z_indices - center_z) * 2.5)**2 +  # 2.5mm slice thickness
        ((y_indices - center_y) * 1.0)**2 +  # 1mm pixel spacing
        ((x_indices - center_x) * 1.0)**2    # 1mm pixel spacing
    )
    
    # Create dose falloff with maximum of 70 Gy at center
    max_dose = 70.0  # Gy
    falloff_distance = 50.0  # mm
    
    # Exponential falloff
    dose_array = max_dose * np.exp(-distance / falloff_distance)
    
    # Set minimum dose to zero for distant regions
    dose_array[distance > 100] = 0.0
    
    # Add some noise to make it realistic
    noise = np.random.normal(0, 0.1, dose_array.shape)
    dose_array = np.maximum(0, dose_array + noise)
    
    return dose_array


@pytest.fixture
def clinical_geometric_params():
    """Create realistic clinical geometric parameters."""
    return {
        'pixel_spacing': [1.0, 1.0],  # 1mm resolution
        'slice_thickness': 2.5,  # 2.5mm slice thickness
        'image_position': [-128.0, -128.0, -62.5],  # Centered
        'image_orientation': [1.0, 0.0, 0.0, 0.0, 1.0, 0.0],  # Standard axial
        'patient_position': 'HFS'  # Head First Supine
    }


class TestEndToEndDoseIntegration:
    """Test complete CT + RTSTRUCT + RTDOSE workflow integration."""
    
    def test_complete_rt_workflow_with_dose(self, clinical_ct_array, clinical_structure_masks, 
                                           clinical_dose_array, clinical_geometric_params, 
                                           temp_workflow_dir):
        """Test complete workflow from CT array to RT Dose with all inter-object references."""
        
        # Step 1: Create CT series from array
        ct_series = CTSeries.from_array(
            pixel_array=clinical_ct_array,
            pixel_spacing=clinical_geometric_params['pixel_spacing'],
            slice_thickness=clinical_geometric_params['slice_thickness'],
            patient_info={
                'PatientID': 'RT_WORKFLOW_001',
                'PatientName': 'Complete^Workflow^Test',
                'StudyDescription': 'RT Planning Study with Dose'
            }
        )
        
        # Validate CT series creation
        assert ct_series is not None
        ct_series.validate()
        assert ct_series.is_validated
        
        # Step 2: Save CT series and get reference
        ct_output_dir = temp_workflow_dir / "ct_series"
        ct_output_dir.mkdir()
        ct_paths = ct_series.save_series(ct_output_dir)
        
        # Verify CT files were created
        assert len(ct_paths) == clinical_ct_array.shape[0]  # One file per slice
        for ct_path in ct_paths:
            assert ct_path.exists()
            assert ct_path.stat().st_size > 0
        
        # Load reference CT for other objects
        reference_ct = pydicom.dcmread(ct_paths[0])
        original_frame_of_reference_uid = reference_ct.FrameOfReferenceUID
        
        # Step 3: Create RT Structure Set from masks using CT reference
        rt_struct = RTStructureSet.from_masks(
            ct_reference=reference_ct,
            masks=clinical_structure_masks,
            patient_info={
                'PatientID': 'RT_WORKFLOW_001',  # Match CT patient ID
                'PatientName': 'Complete^Workflow^Test'
            }
        )
        
        # Validate RT Structure Set creation
        assert rt_struct is not None
        rt_struct.validate()
        assert rt_struct.is_validated
        
        # Step 4: Save RT Structure Set
        struct_path = temp_workflow_dir / "rt_struct.dcm"
        saved_struct_path = rt_struct.save(struct_path)
        assert saved_struct_path.exists()
        
        # Step 5: Create RT Dose from dose array using same CT reference
        rt_dose = RTDose.from_array(
            dose_array=clinical_dose_array,
            reference_image=reference_ct,
            dose_units='GY',
            dose_type='PHYSICAL',
            summation_type='PLAN',
            patient_info={
                'PatientID': 'RT_WORKFLOW_001',  # Match CT and Structure patient ID
                'PatientName': 'Complete^Workflow^Test'
            }
        )
        
        # Validate RT Dose creation
        assert rt_dose is not None
        rt_dose.validate()
        assert rt_dose.is_validated
        
        # Step 6: Save RT Dose
        dose_path = temp_workflow_dir / "rt_dose.dcm"
        saved_dose_path = rt_dose.save(dose_path)
        assert saved_dose_path.exists()
        assert saved_dose_path.stat().st_size > 0
        
        # Step 7: Verify complete workflow integrity and Frame of Reference consistency
        # Load all saved objects for validation
        reloaded_struct = pydicom.dcmread(saved_struct_path)
        reloaded_dose = pydicom.dcmread(saved_dose_path)
        
        # Verify basic DICOM compliance
        assert reloaded_struct.Modality == 'RTSTRUCT'
        assert reloaded_dose.Modality == 'RTDOSE'
        
        # Verify patient ID consistency across all objects
        assert reference_ct.PatientID == 'RT_WORKFLOW_001'
        assert reloaded_struct.PatientID == 'RT_WORKFLOW_001'
        assert reloaded_dose.PatientID == 'RT_WORKFLOW_001'
        
        # Step 8: Critical Frame of Reference UID consistency validation
        struct_frame_uid = reloaded_struct.FrameOfReferenceUID
        dose_frame_uid = reloaded_dose.FrameOfReferenceUID
        
        assert original_frame_of_reference_uid == struct_frame_uid, \
            f"Frame of Reference UID mismatch: CT={original_frame_of_reference_uid}, STRUCT={struct_frame_uid}"
        assert original_frame_of_reference_uid == dose_frame_uid, \
            f"Frame of Reference UID mismatch: CT={original_frame_of_reference_uid}, DOSE={dose_frame_uid}"
        assert struct_frame_uid == dose_frame_uid, \
            f"Frame of Reference UID mismatch: STRUCT={struct_frame_uid}, DOSE={dose_frame_uid}"
        
        # Step 9: Verify structure and dose content
        # Verify structure names are preserved
        roi_names = [roi.ROIName for roi in reloaded_struct.StructureSetROISequence]
        expected_names = list(clinical_structure_masks.keys())
        assert set(roi_names) == set(expected_names)
        
        # Verify dose parameters
        assert reloaded_dose.DoseUnits == 'GY'
        assert reloaded_dose.DoseType == 'PHYSICAL'
        assert reloaded_dose.DoseSummationType == 'PLAN'
        assert hasattr(reloaded_dose, 'DoseGridScaling')
        assert reloaded_dose.DoseGridScaling > 0
        
        # Verify dose array dimensions match input
        dose_pixel_array = reloaded_dose.pixel_array
        assert dose_pixel_array.shape == clinical_dose_array.shape
        
        # Step 10: Validate dose-CT geometric alignment
        alignment_results = validate_dose_ct_alignment(reloaded_dose, reference_ct)
        error_results = [r for r in alignment_results if 'error' in r.level.lower()]
        assert len(error_results) == 0, f"Dose-CT alignment errors: {[r.message for r in error_results]}"
        
        print(f"✅ Complete RT workflow test passed:")
        print(f"   - Created {len(ct_paths)} CT slices")  
        print(f"   - Created RT Structure Set with {len(clinical_structure_masks)} structures")
        print(f"   - Created RT Dose with {clinical_dose_array.shape} dose grid")
        print(f"   - Frame of Reference UID consistent: {original_frame_of_reference_uid}")
        print(f"   - All inter-object references validated successfully")
    
    def test_rt_dose_precision_validation(self, clinical_ct_array, clinical_dose_array, 
                                         clinical_geometric_params, temp_workflow_dir):
        """Test RT Dose creation maintains precision within 0.1% of input array."""
        
        # Create CT reference
        ct_series = CTSeries.from_array(
            pixel_array=clinical_ct_array,
            pixel_spacing=clinical_geometric_params['pixel_spacing'],
            slice_thickness=clinical_geometric_params['slice_thickness'],
            patient_info={'PatientID': 'PRECISION_001', 'PatientName': 'Precision^Test'}
        )
        
        ct_dir = temp_workflow_dir / "precision_ct"
        ct_dir.mkdir()
        ct_paths = ct_series.save_series(ct_dir)
        reference_ct = pydicom.dcmread(ct_paths[0])
        
        # Create RT Dose
        rt_dose = RTDose.from_array(
            dose_array=clinical_dose_array,
            reference_image=reference_ct,
            dose_units='GY',
            dose_type='PHYSICAL',
            patient_info={'PatientID': 'PRECISION_001', 'PatientName': 'Precision^Test'}
        )
        
        # Save and reload dose
        dose_path = temp_workflow_dir / "precision_dose.dcm"
        rt_dose.save(dose_path)
        reloaded_dose = pydicom.dcmread(dose_path)
        
        # Extract dose values using DICOM scaling
        pixel_array = reloaded_dose.pixel_array
        dose_scaling = float(reloaded_dose.DoseGridScaling)
        reconstructed_dose = pixel_array * dose_scaling
        
        # Calculate precision metrics
        max_original = np.max(clinical_dose_array)
        max_reconstructed = np.max(reconstructed_dose)
        relative_error = abs(max_reconstructed - max_original) / max_original * 100
        
        # Validate precision requirement (<0.1% for maximum dose)
        assert relative_error < 0.1, f"Dose precision error {relative_error:.3f}% exceeds 0.1% requirement"
        
        # Calculate mean absolute relative error for non-zero doses
        non_zero_mask = clinical_dose_array > 0.01  # Ignore very small doses
        if np.any(non_zero_mask):
            original_nonzero = clinical_dose_array[non_zero_mask]
            reconstructed_nonzero = reconstructed_dose[non_zero_mask]
            mare = np.mean(np.abs(reconstructed_nonzero - original_nonzero) / original_nonzero) * 100
            assert mare < 0.5, f"Mean absolute relative error {mare:.3f}% exceeds 0.5% threshold"
        
        print(f"✅ RT Dose precision validation passed:")
        print(f"   - Maximum dose relative error: {relative_error:.4f}%")
        print(f"   - Original max dose: {max_original:.2f} Gy")
        print(f"   - Reconstructed max dose: {max_reconstructed:.2f} Gy")
        print(f"   - Dose scaling factor: {dose_scaling:.6f}")
    
    def test_multi_object_study_consistency(self, clinical_ct_array, clinical_structure_masks,
                                          clinical_dose_array, clinical_geometric_params, 
                                          temp_workflow_dir):
        """Test Study and Series UID consistency across multiple RT objects."""
        
        # Create CT series
        ct_series = CTSeries.from_array(
            pixel_array=clinical_ct_array,
            pixel_spacing=clinical_geometric_params['pixel_spacing'],
            slice_thickness=clinical_geometric_params['slice_thickness'],
            patient_info={
                'PatientID': 'STUDY_CONSISTENCY_001',
                'PatientName': 'Study^Consistency^Test',
                'StudyDescription': 'Multi-Object Study Consistency Test'
            }
        )
        
        ct_dir = temp_workflow_dir / "study_ct"
        ct_dir.mkdir()
        ct_paths = ct_series.save_series(ct_dir)
        reference_ct = pydicom.dcmread(ct_paths[0])
        
        # Create RT Structure Set with same study context
        rt_struct = RTStructureSet.from_masks(
            ct_reference=reference_ct,
            masks=clinical_structure_masks,
            patient_info={
                'PatientID': 'STUDY_CONSISTENCY_001',
                'PatientName': 'Study^Consistency^Test',
                'StudyDescription': 'Multi-Object Study Consistency Test'
            }
        )
        
        # Create RT Dose with same study context
        rt_dose = RTDose.from_array(
            dose_array=clinical_dose_array,
            reference_image=reference_ct,
            dose_units='GY',
            dose_type='PHYSICAL',
            patient_info={
                'PatientID': 'STUDY_CONSISTENCY_001',
                'PatientName': 'Study^Consistency^Test',
                'StudyDescription': 'Multi-Object Study Consistency Test'
            }
        )
        
        # Save all objects
        struct_path = temp_workflow_dir / "study_struct.dcm"
        dose_path = temp_workflow_dir / "study_dose.dcm"
        
        rt_struct.save(struct_path)
        rt_dose.save(dose_path)
        
        # Load and validate study consistency
        reloaded_struct = pydicom.dcmread(struct_path)
        reloaded_dose = pydicom.dcmread(dose_path)
        
        # Validate Study Instance UID consistency
        ct_study_uid = reference_ct.StudyInstanceUID
        struct_study_uid = reloaded_struct.StudyInstanceUID
        dose_study_uid = reloaded_dose.StudyInstanceUID
        
        assert ct_study_uid == struct_study_uid, "CT and Structure Study UIDs must match"
        assert ct_study_uid == dose_study_uid, "CT and Dose Study UIDs must match"
        assert struct_study_uid == dose_study_uid, "Structure and Dose Study UIDs must match"
        
        # Validate Frame of Reference UID consistency
        ct_frame_uid = reference_ct.FrameOfReferenceUID
        struct_frame_uid = reloaded_struct.FrameOfReferenceUID
        dose_frame_uid = reloaded_dose.FrameOfReferenceUID
        
        assert ct_frame_uid == struct_frame_uid, "CT and Structure Frame UIDs must match"
        assert ct_frame_uid == dose_frame_uid, "CT and Dose Frame UIDs must match"
        assert struct_frame_uid == dose_frame_uid, "Structure and Dose Frame UIDs must match"
        
        # Validate Series Instance UIDs are different (each object has its own series)
        ct_series_uid = reference_ct.SeriesInstanceUID
        struct_series_uid = reloaded_struct.SeriesInstanceUID
        dose_series_uid = reloaded_dose.SeriesInstanceUID
        
        assert ct_series_uid != struct_series_uid, "CT and Structure should have different Series UIDs"
        assert ct_series_uid != dose_series_uid, "CT and Dose should have different Series UIDs"
        assert struct_series_uid != dose_series_uid, "Structure and Dose should have different Series UIDs"
        
        print(f"✅ Multi-object study consistency test passed:")
        print(f"   - Study Instance UID consistent: {ct_study_uid}")
        print(f"   - Frame of Reference UID consistent: {ct_frame_uid}")
        print(f"   - Series Instance UIDs appropriately unique")
        print(f"   - All inter-object relationships validated successfully")


class TestRTDosePerformanceBenchmarks:
    """Test RT Dose creation performance benchmarks."""
    
    def test_dose_creation_performance_benchmarks(self, clinical_geometric_params, temp_workflow_dir):
        """Test RT Dose creation performance meets <10 second target for 512³ dose grid."""
        
        # Create large dose array (512³ would be too large for CI, use 256³)
        large_dose_shape = (256, 256, 256)  # More manageable for testing
        large_dose_array = np.random.rand(*large_dose_shape).astype(np.float64) * 70.0
        
        # Create matching CT array for reference
        ct_array = np.random.randint(-1000, 3000, size=large_dose_shape, dtype=np.int16)
        
        # Create CT reference
        ct_series = CTSeries.from_array(
            pixel_array=ct_array,
            pixel_spacing=clinical_geometric_params['pixel_spacing'],
            slice_thickness=clinical_geometric_params['slice_thickness'],
            patient_info={'PatientID': 'PERF_001', 'PatientName': 'Performance^Test'}
        )
        
        ct_dir = temp_workflow_dir / "performance_ct"
        ct_dir.mkdir()
        ct_paths = ct_series.save_series(ct_dir)
        reference_ct = pydicom.dcmread(ct_paths[0])
        
        # Measure RT Dose creation time
        start_time = time.time()
        start_memory = psutil.Process(os.getpid()).memory_info().rss / 1024**3  # GB
        
        rt_dose = RTDose.from_array(
            dose_array=large_dose_array,
            reference_image=reference_ct,
            dose_units='GY',
            dose_type='PHYSICAL',
            patient_info={'PatientID': 'PERF_001', 'PatientName': 'Performance^Test'}
        )
        
        creation_time = time.time() - start_time
        
        # Measure save time and memory usage
        dose_path = temp_workflow_dir / "performance_dose.dcm"
        save_start_time = time.time()
        
        rt_dose.save(dose_path)
        
        save_time = time.time() - save_start_time
        peak_memory = psutil.Process(os.getpid()).memory_info().rss / 1024**3  # GB
        memory_usage = peak_memory - start_memory
        
        total_time = creation_time + save_time
        
        # Performance requirements (adjusted for 256³ instead of 512³)
        # For 256³: expect ~1/8 the time of 512³, so target <1.25 seconds
        max_creation_time = 2.0  # seconds (generous for CI environments)
        max_memory_usage = 1.0   # GB (generous for CI environments)
        
        # Validate performance requirements
        assert total_time < max_creation_time, \
            f"RT Dose creation time {total_time:.2f}s exceeds {max_creation_time}s target"
        assert memory_usage < max_memory_usage, \
            f"Memory usage {memory_usage:.2f}GB exceeds {max_memory_usage}GB target"
        
        # Verify file was created successfully
        assert dose_path.exists()
        file_size_mb = dose_path.stat().st_size / 1024**2
        
        # Validate dose precision
        reloaded_dose = pydicom.dcmread(dose_path)
        pixel_array = reloaded_dose.pixel_array
        dose_scaling = float(reloaded_dose.DoseGridScaling)
        reconstructed_dose = pixel_array * dose_scaling
        
        max_original = np.max(large_dose_array)
        max_reconstructed = np.max(reconstructed_dose)
        relative_error = abs(max_reconstructed - max_original) / max_original * 100
        
        assert relative_error < 0.1, f"Dose precision error {relative_error:.3f}% exceeds 0.1%"
        
        print(f"✅ RT Dose performance benchmark passed:")
        print(f"   - Dose array shape: {large_dose_shape}")
        print(f"   - Creation time: {creation_time:.2f}s")
        print(f"   - Save time: {save_time:.2f}s")
        print(f"   - Total time: {total_time:.2f}s (target: <{max_creation_time}s)")
        print(f"   - Memory usage: {memory_usage:.2f}GB (target: <{max_memory_usage}GB)")
        print(f"   - File size: {file_size_mb:.1f}MB")
        print(f"   - Dose precision error: {relative_error:.4f}%")
    
    def test_dose_memory_optimization(self, clinical_geometric_params, temp_workflow_dir):
        """Test memory optimization for large dose arrays."""
        
        # Create moderately large dose array to test memory management
        dose_shape = (100, 256, 256)  # ~50MB dose array
        dose_array = np.random.rand(*dose_shape).astype(np.float64) * 70.0
        
        # Create CT reference
        ct_array = np.random.randint(-1000, 3000, size=dose_shape, dtype=np.int16)
        ct_series = CTSeries.from_array(
            pixel_array=ct_array,
            pixel_spacing=clinical_geometric_params['pixel_spacing'],
            slice_thickness=clinical_geometric_params['slice_thickness'],
            patient_info={'PatientID': 'MEMORY_001', 'PatientName': 'Memory^Test'}
        )
        
        ct_dir = temp_workflow_dir / "memory_ct"
        ct_dir.mkdir()
        ct_paths = ct_series.save_series(ct_dir)
        reference_ct = pydicom.dcmread(ct_paths[0])
        
        # Monitor memory during creation
        initial_memory = psutil.Process(os.getpid()).memory_info().rss / 1024**3
        
        rt_dose = RTDose.from_array(
            dose_array=dose_array,
            reference_image=reference_ct,
            dose_units='GY',
            dose_type='PHYSICAL',
            patient_info={'PatientID': 'MEMORY_001', 'PatientName': 'Memory^Test'}
        )
        
        creation_memory = psutil.Process(os.getpid()).memory_info().rss / 1024**3
        
        dose_path = temp_workflow_dir / "memory_dose.dcm"
        rt_dose.save(dose_path)
        
        final_memory = psutil.Process(os.getpid()).memory_info().rss / 1024**3
        
        # Calculate memory usage
        creation_overhead = creation_memory - initial_memory
        total_overhead = final_memory - initial_memory
        
        # Expected memory usage should be reasonable (less than 3x input array size)
        input_array_size_gb = dose_array.nbytes / 1024**3
        max_expected_overhead = input_array_size_gb * 3  # Conservative estimate
        
        assert creation_overhead < max_expected_overhead, \
            f"Memory overhead {creation_overhead:.2f}GB exceeds {max_expected_overhead:.2f}GB"
        
        # Verify dose statistics are preserved
        dose_stats = rt_dose.get_dose_statistics()
        original_max = np.max(dose_array)
        original_mean = np.mean(dose_array)
        
        assert abs(dose_stats['max_dose'] - original_max) < 0.01, "Maximum dose not preserved"
        assert abs(dose_stats['mean_dose'] - original_mean) < 0.01, "Mean dose not preserved"
        
        print(f"✅ RT Dose memory optimization test passed:")
        print(f"   - Input array size: {input_array_size_gb:.3f}GB")
        print(f"   - Creation memory overhead: {creation_overhead:.3f}GB")
        print(f"   - Total memory overhead: {total_overhead:.3f}GB")
        print(f"   - Memory efficiency: {creation_overhead/input_array_size_gb:.1f}x input size")


class TestRTDoseValidationIntegration:
    """Test RT Dose validation integration with other RT objects."""
    
    def test_dose_clinical_validation_integration(self, clinical_ct_array, clinical_dose_array, 
                                                 clinical_geometric_params, temp_workflow_dir):
        """Test clinical validation throughout RT Dose workflow."""
        
        # Create CT series with clinical validation
        ct_series = CTSeries.from_array(
            pixel_array=clinical_ct_array,
            pixel_spacing=clinical_geometric_params['pixel_spacing'],
            slice_thickness=clinical_geometric_params['slice_thickness'],
            patient_info={'PatientID': 'VALIDATION_001', 'PatientName': 'Validation^Test'}
        )
        
        ct_dir = temp_workflow_dir / "validation_ct"
        ct_dir.mkdir()
        ct_paths = ct_series.save_series(ct_dir)
        reference_ct = pydicom.dcmread(ct_paths[0])
        
        # Create RT Dose with clinical validation
        rt_dose = RTDose.from_array(
            dose_array=clinical_dose_array,
            reference_image=reference_ct,
            dose_units='GY',
            dose_type='PHYSICAL',
            patient_info={'PatientID': 'VALIDATION_001', 'PatientName': 'Validation^Test'}
        )
        
        # Test clinical validation
        validator = ClinicalValidator()
        
        # Validate dose parameters
        dose_validation = validator.validate_dose_parameters(
            dose_array=clinical_dose_array,
            dose_units='GY'
        )
        
        # Check for critical validation errors
        critical_errors = [r for r in dose_validation if r.level.value in ['error', 'critical']]
        assert len(critical_errors) == 0, f"Critical dose validation errors: {[r.message for r in critical_errors]}"
        
        # Validate RT Dose object
        rt_dose.validate()
        assert rt_dose.is_validated, "RT Dose validation should pass for clinical data"
        
        # Save and verify DICOM compliance
        dose_path = temp_workflow_dir / "validated_dose.dcm"
        rt_dose.save(dose_path)
        
        # Test DICOM compliance validation
        compliance_validator = DicomComplianceValidator()
        saved_dose = pydicom.dcmread(dose_path)
        
        compliance_results = compliance_validator.validate_rt_dose_iod(saved_dose)
        compliance_errors = [r for r in compliance_results if r.level.value in ['error', 'critical']]
        assert len(compliance_errors) == 0, f"DICOM compliance errors: {[r.message for r in compliance_errors]}"
        
        print(f"✅ RT Dose clinical validation integration passed:")
        print(f"   - Clinical dose validation: {len(dose_validation)} checks")
        print(f"   - RT Dose object validation: passed")
        print(f"   - DICOM compliance validation: {len(compliance_results)} checks")
        print(f"   - No critical validation errors found")
    
    def test_dose_error_handling_integration(self, clinical_ct_array, clinical_geometric_params, 
                                           temp_workflow_dir):
        """Test error handling in RT Dose integration workflows."""
        
        # Create CT reference
        ct_series = CTSeries.from_array(
            pixel_array=clinical_ct_array,
            pixel_spacing=clinical_geometric_params['pixel_spacing'],
            slice_thickness=clinical_geometric_params['slice_thickness'],
            patient_info={'PatientID': 'ERROR_001', 'PatientName': 'Error^Test'}
        )
        
        ct_dir = temp_workflow_dir / "error_ct"
        ct_dir.mkdir()
        ct_paths = ct_series.save_series(ct_dir)
        reference_ct = pydicom.dcmread(ct_paths[0])
        
        # Test 1: Invalid dose array (wrong dimensions)
        invalid_dose_2d = np.random.rand(256, 256) * 70.0
        
        with pytest.raises((DicomCreationError, ValidationError)):
            RTDose.from_array(
                dose_array=invalid_dose_2d,  # 2D instead of 3D
                reference_image=reference_ct,
                dose_units='GY'
            ).validate()
        
        # Test 2: Dose array with NaN values
        dose_with_nan = np.random.rand(50, 256, 256) * 70.0
        dose_with_nan[25, 128, 128] = np.nan
        
        with pytest.raises((DicomCreationError, ValidationError)):
            rt_dose_nan = RTDose.from_array(
                dose_array=dose_with_nan,
                reference_image=reference_ct,
                dose_units='GY'
            )
            rt_dose_nan.save(temp_workflow_dir / "dose_with_nan.dcm")
        
        # Test 3: Dose array with infinite values
        dose_with_inf = np.random.rand(50, 256, 256) * 70.0
        dose_with_inf[25, 128, 128] = np.inf
        
        with pytest.raises((DicomCreationError, ValidationError)):
            rt_dose_inf = RTDose.from_array(
                dose_array=dose_with_inf,
                reference_image=reference_ct,
                dose_units='GY'
            )
            rt_dose_inf.save(temp_workflow_dir / "dose_with_inf.dcm")
        
        # Test 4: Invalid dose units
        valid_dose = np.random.rand(50, 256, 256) * 70.0
        
        with pytest.raises((DicomCreationError, ValidationError)):
            RTDose.from_array(
                dose_array=valid_dose,
                reference_image=reference_ct,
                dose_units='INVALID_UNITS'  # Invalid dose units
            ).validate()
        
        print(f"✅ RT Dose error handling integration passed:")
        print(f"   - Invalid 2D dose array: properly rejected")
        print(f"   - NaN values in dose array: properly rejected")
        print(f"   - Infinite values in dose array: properly rejected")
        print(f"   - Invalid dose units: properly rejected")
        print(f"   - All error conditions handled with appropriate exceptions")


if __name__ == "__main__":
    # Run specific dose integration tests
    pytest.main([__file__, "-v"])