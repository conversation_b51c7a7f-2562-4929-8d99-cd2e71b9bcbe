{"permissions": {"allow": ["mcp__sequential-thinking__sequentialthinking", "Bash(python -m pytest tests/test_clinical_docs.py::TestClinicalDocumentation::test_base_dicom_creator_clinical_context -v)", "Bash(ruff check:*)", "Bash(python -m pytest tests/test_docstring_format.py tests/test_clinical_docs.py -v)", "Bash(python -m pytest --tb=short -q)", "Bash(git push:*)", "Bash(git add:*)", "mcp__context7__resolve-library-id", "Bash(python -m pytest tests/core/test_plan_base.py::TestRTPlanBase::test_from_beam_config_basic -v)"], "deny": []}}