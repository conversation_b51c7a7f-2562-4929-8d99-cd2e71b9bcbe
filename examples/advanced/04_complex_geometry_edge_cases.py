# ---
# jupyter:
#   jupytext:
#     text_representation:
#       extension: .py
#       format_name: percent
#       format_version: '1.3'
#       jupytext_version: 1.17.2
#   kernelspec:
#     display_name: .venv
#     language: python
#     name: python3
# ---

# %% [markdown]
# # MaskToContourConverter: Complex Geometry & Edge Cases
#
# **Learning Objectives:**
# - Handle complex anatomical structures with holes, islands, and irregular boundaries
# - Master 3D multi-slice visualization and analysis techniques
# - Implement robust edge case handling for production environments
# - Develop error recovery strategies for challenging scenarios
#
# **Prerequisites:**
# - Understanding of basic MaskToContourConverter usage (see `01_basic_mask_to_contour_usage.ipynb`)
# - Familiarity with parameter optimization (see `03_parameter_tuning_optimization.ipynb`)
#
# **Table of Contents:**
# 1. [Setup & Advanced Tools](#setup)
# 2. [Complex Anatomical Structures](#complex-anatomy)
# 3. [3D Multi-slice Visualization](#3d-visualization)
# 4. [Edge Case Handling](#edge-cases)
# 5. [Error Recovery Scenarios](#error-recovery)
# 6. [Production Readiness Guide](#production-guide)

# %% [markdown]
# ## 1. Setup & Advanced Tools {#setup}
#
# First, let's set up our environment with advanced visualization capabilities and helper functions for complex geometry handling.

# %%
# Core imports
import numpy as np
import matplotlib.pyplot as plt
from matplotlib.patches import Circle, Polygon
from mpl_toolkits.mplot3d import Axes3D
from mpl_toolkits.mplot3d.art3d import Poly3DCollection
import warnings
warnings.filterwarnings('ignore')

# Advanced visualization
try:
    import plotly.graph_objects as go
    import plotly.express as px
    from plotly.subplots import make_subplots
    PLOTLY_AVAILABLE = True
    print("✅ Plotly available for advanced 3D visualization")
except ImportError:
    PLOTLY_AVAILABLE = False
    print("⚠️  Plotly not available - using matplotlib 3D (install plotly for enhanced interactivity)")

# Interactive widgets
try:
    import ipywidgets as widgets
    from IPython.display import display, clear_output
    WIDGETS_AVAILABLE = True
    print("✅ Interactive widgets available")
except ImportError:
    WIDGETS_AVAILABLE = False
    print("⚠️  Interactive widgets not available")

# Image processing for complex structures
try:
    from scipy import ndimage
    from scipy.spatial.distance import cdist
    from skimage import measure, morphology, filters
    SCIPY_AVAILABLE = True
    print("✅ Advanced image processing tools available")
except ImportError:
    SCIPY_AVAILABLE = False
    print("⚠️  Advanced processing tools not available - some features may be limited")

# pyrt-dicom imports
try:
    from pyrt_dicom.utils.contour_processing import MaskToContourConverter
    print("✅ MaskToContourConverter imported successfully")
except ImportError as e:
    print(f"❌ Import error: {e}")
    print("Please ensure pyrt-dicom is installed: pip install -e .")

# Set up plotting style
plt.style.use('default')
plt.rcParams['figure.figsize'] = (14, 10)
plt.rcParams['font.size'] = 10

# Memory and performance monitoring
import time
import psutil
import gc

def get_memory_usage():
    """Get current memory usage in MB."""
    process = psutil.Process()
    return process.memory_info().rss / 1024 / 1024

print(f"\n🖥️  System initialized - Memory usage: {get_memory_usage():.1f} MB")


# %%
# Advanced helper functions for complex geometry

def create_complex_anatomical_structure(structure_type, size=(200, 200), complexity='medium'):
    """Create realistic complex anatomical structures for testing."""
    
    mask = np.zeros(size, dtype=bool)
    y_center, x_center = size[0] // 2, size[1] // 2
    
    if structure_type == 'organ_with_holes':
        # Simulated organ (liver-like) with internal vessels/holes
        y, x = np.ogrid[:size[0], :size[1]]
        
        # Main organ body (irregular ellipse)
        main_body = ((x - x_center)**2 / (60**2) + (y - y_center)**2 / (80**2)) <= 1
        
        # Add irregular boundary
        if complexity in ['medium', 'high']:
            angles = np.linspace(0, 2*np.pi, 100)
            for angle in angles:
                r_variation = np.random.normal(1, 0.1)
                dx = int(60 * r_variation * np.cos(angle))
                dy = int(80 * r_variation * np.sin(angle))
                if 0 <= y_center + dy < size[0] and 0 <= x_center + dx < size[1]:
                    main_body[y_center + dy, x_center + dx] = True
        
        mask = main_body
        
        # Create holes (vessels, bile ducts)
        hole_positions = [(0.7, 0.3), (0.3, 0.8), (0.9, 0.7), (0.2, 0.2)]
        hole_radii = [15, 8, 12, 6]
        
        for (fx, fy), radius in zip(hole_positions, hole_radii):
            hx, hy = int(x_center + (fx - 0.5) * 100), int(y_center + (fy - 0.5) * 120)
            hole_mask = ((x - hx)**2 + (y - hy)**2) <= radius**2
            mask = mask & ~hole_mask
        
        # Add connecting channels if high complexity
        if complexity == 'high':
            # Thin connecting vessels between holes
            for i in range(len(hole_positions) - 1):
                start = hole_positions[i]
                end = hole_positions[i + 1]
                sx, sy = int(x_center + (start[0] - 0.5) * 100), int(y_center + (start[1] - 0.5) * 120)
                ex, ey = int(x_center + (end[0] - 0.5) * 100), int(y_center + (end[1] - 0.5) * 120)
                
                # Create line between points
                line_length = int(np.sqrt((ex - sx)**2 + (ey - sy)**2))
                for t in np.linspace(0, 1, line_length):
                    px, py = int(sx + t * (ex - sx)), int(sy + t * (ey - sy))
                    for dx in range(-2, 3):
                        for dy in range(-2, 3):
                            if 0 <= py + dy < size[0] and 0 <= px + dx < size[1]:
                                mask[py + dy, px + dx] = False
    
    elif structure_type == 'multi_component':
        # Multiple separate components (lymph nodes, metastases)
        y, x = np.ogrid[:size[0], :size[1]]
        
        # Main tumor
        main_tumor = ((x - x_center)**2 + (y - y_center)**2) <= 40**2
        mask = main_tumor
        
        # Satellite lesions
        satellites = [
            (x_center - 80, y_center - 60, 15),  # Upper left
            (x_center + 70, y_center - 40, 12),  # Upper right  
            (x_center - 50, y_center + 80, 18),  # Lower left
            (x_center + 60, y_center + 70, 10),  # Lower right
        ]
        
        for sx, sy, radius in satellites:
            if 0 <= sx < size[1] and 0 <= sy < size[0]:
                satellite_mask = ((x - sx)**2 + (y - sy)**2) <= radius**2
                mask = mask | satellite_mask
        
        # Add connecting bridges if medium/high complexity
        if complexity in ['medium', 'high']:
            # Thin tissue bridges between components
            bridge_width = 3 if complexity == 'medium' else 5
            
            # Connect main tumor to closest satellites
            for sx, sy, _ in satellites[:2]:  # Connect to first two
                if 0 <= sx < size[1] and 0 <= sy < size[0]:
                    # Create bridge
                    line_length = int(np.sqrt((sx - x_center)**2 + (sy - y_center)**2))
                    for t in np.linspace(0, 1, line_length):
                        px, py = int(x_center + t * (sx - x_center)), int(y_center + t * (sy - y_center))
                        for dx in range(-bridge_width//2, bridge_width//2 + 1):
                            for dy in range(-bridge_width//2, bridge_width//2 + 1):
                                if 0 <= py + dy < size[0] and 0 <= px + dx < size[1]:
                                    mask[py + dy, px + dx] = True
    
    elif structure_type == 'highly_irregular':
        # Highly irregular boundary (brain lesion, scar tissue)
        y, x = np.ogrid[:size[0], :size[1]]
        
        # Start with rough circular base
        base_mask = ((x - x_center)**2 + (y - y_center)**2) <= 50**2
        
        # Create highly irregular boundary using fractal-like approach
        mask = np.zeros(size, dtype=bool)
        
        # Generate irregular boundary points
        angles = np.linspace(0, 2*np.pi, 360)
        radii = []
        
        for angle in angles:
            # Multi-scale noise for fractal-like irregularity
            base_radius = 50
            noise1 = 15 * np.sin(8 * angle) * np.random.normal(1, 0.3)
            noise2 = 8 * np.sin(16 * angle) * np.random.normal(1, 0.2)
            noise3 = 4 * np.sin(32 * angle) * np.random.normal(1, 0.1)
            
            radius = base_radius + noise1 + noise2 + noise3
            radius = max(10, min(80, radius))  # Clamp to reasonable bounds
            radii.append(radius)
        
        # Fill the irregular shape
        for i, (angle, radius) in enumerate(zip(angles, radii)):
            # Create sector from center to boundary
            next_angle = angles[(i + 1) % len(angles)]
            next_radius = radii[(i + 1) % len(radii)]
            
            # Fill triangle sector
            for r in np.linspace(0, radius, int(radius)):
                px = int(x_center + r * np.cos(angle))
                py = int(y_center + r * np.sin(angle))
                if 0 <= px < size[1] and 0 <= py < size[0]:
                    mask[py, px] = True
        
        # Smooth the result slightly to avoid single-pixel artifacts
        if SCIPY_AVAILABLE:
            mask = ndimage.binary_closing(mask, structure=np.ones((3, 3)))
    
    elif structure_type == 'thin_structures':
        # Thin structures (vessels, nerves, spinal cord)
        mask = np.zeros(size, dtype=bool)
        
        # Main vessel/nerve trunk
        for y in range(20, size[0] - 20):
            # Sinusoidal path with varying width
            x_center_line = x_center + int(20 * np.sin(y * 0.05))
            width = max(2, int(5 + 3 * np.sin(y * 0.1)))
            
            for dx in range(-width, width + 1):
                if 0 <= x_center_line + dx < size[1]:
                    mask[y, x_center_line + dx] = True
        
        # Branching structures
        branch_points = [50, 100, 150]
        for branch_y in branch_points:
            if branch_y < size[0]:
                start_x = x_center + int(20 * np.sin(branch_y * 0.05))
                
                # Left branch
                for i, y in enumerate(range(branch_y, min(branch_y + 40, size[0]))):
                    branch_x = start_x - i * 2
                    width = max(1, 3 - i // 10)
                    for dx in range(-width, width + 1):
                        if 0 <= branch_x + dx < size[1]:
                            mask[y, branch_x + dx] = True
                
                # Right branch
                for i, y in enumerate(range(branch_y, min(branch_y + 35, size[0]))):
                    branch_x = start_x + i * 2
                    width = max(1, 2 - i // 15)
                    for dx in range(-width, width + 1):
                        if 0 <= branch_x + dx < size[1]:
                            mask[y, branch_x + dx] = True
    
    return mask

def create_3d_structure(structure_type, volume_shape=(50, 200, 200), complexity='medium'):
    """Create 3D multi-slice structures for testing."""
    
    volume = np.zeros(volume_shape, dtype=bool)
    nz, ny, nx = volume_shape
    
    if structure_type == 'expanding_tumor':
        # Tumor that grows and changes shape through slices
        center_z, center_y, center_x = nz // 2, ny // 2, nx // 2
        
        for z in range(nz):
            # Radius varies through slices (growing tumor)
            z_factor = np.abs(z - center_z) / (nz / 2)
            base_radius = 40 * (1 - z_factor * 0.7)  # Smaller at edges
            
            if base_radius > 5:  # Only create structure if radius is reasonable
                # Shape changes through slices
                ellipse_a = base_radius * (1 + 0.3 * np.sin(z * 0.3))
                ellipse_b = base_radius * (1 + 0.2 * np.cos(z * 0.4))
                
                # Center shift through slices
                shift_x = int(10 * np.sin(z * 0.2))
                shift_y = int(8 * np.cos(z * 0.15))
                
                y, x = np.ogrid[:ny, :nx]
                slice_mask = ((x - center_x - shift_x)**2 / ellipse_a**2 + 
                             (y - center_y - shift_y)**2 / ellipse_b**2) <= 1
                
                volume[z] = slice_mask
    
    elif structure_type == 'bifurcating_vessel':
        # Vessel that bifurcates through slices
        center_y, center_x = ny // 2, nx // 2
        
        for z in range(nz):
            slice_mask = np.zeros((ny, nx), dtype=bool)
            
            if z < nz // 3:
                # Single vessel
                vessel_x = center_x + int(5 * np.sin(z * 0.1))
                vessel_width = max(3, 8 - z // 5)
                
                for y in range(ny):
                    for dx in range(-vessel_width, vessel_width + 1):
                        if 0 <= vessel_x + dx < nx:
                            slice_mask[y, vessel_x + dx] = True
            
            elif z < 2 * nz // 3:
                # Bifurcation region
                bifurc_progress = (z - nz // 3) / (nz // 3)
                
                # Left branch
                left_x = center_x - int(bifurc_progress * 30)
                left_width = max(2, 6 - int(bifurc_progress * 2))
                
                # Right branch  
                right_x = center_x + int(bifurc_progress * 25)
                right_width = max(2, 5 - int(bifurc_progress * 1.5))
                
                for y in range(ny):
                    # Left branch
                    for dx in range(-left_width, left_width + 1):
                        if 0 <= left_x + dx < nx:
                            slice_mask[y, left_x + dx] = True
                    
                    # Right branch
                    for dx in range(-right_width, right_width + 1):
                        if 0 <= right_x + dx < nx:
                            slice_mask[y, right_x + dx] = True
            
            else:
                # Two separate vessels
                left_x = center_x - 30 + int(3 * np.sin((z - 2 * nz // 3) * 0.15))
                right_x = center_x + 25 + int(4 * np.cos((z - 2 * nz // 3) * 0.12))
                
                vessel_width = max(2, 4)
                
                for y in range(ny):
                    # Left vessel
                    for dx in range(-vessel_width, vessel_width + 1):
                        if 0 <= left_x + dx < nx:
                            slice_mask[y, left_x + dx] = True
                    
                    # Right vessel
                    for dx in range(-vessel_width, vessel_width + 1):
                        if 0 <= right_x + dx < nx:
                            slice_mask[y, right_x + dx] = True
            
            volume[z] = slice_mask
    
    elif structure_type == 'sparse_structure':
        # Structure that appears/disappears in slices (lymph nodes)
        node_positions = [
            (10, 60, 60, 15),   # (start_z, y, x, radius)
            (20, 80, 120, 12),
            (35, 140, 80, 18),
            (40, 120, 150, 10),
        ]
        
        for start_z, node_y, node_x, radius in node_positions:
            node_length = np.random.randint(3, 8)  # Random length
            
            for dz in range(node_length):
                z = start_z + dz
                if 0 <= z < nz:
                    # Node size varies through slices
                    slice_radius = radius * (1 - abs(dz - node_length//2) / (node_length//2) * 0.3)
                    
                    y, x = np.ogrid[:ny, :nx]
                    node_mask = ((x - node_x)**2 + (y - node_y)**2) <= slice_radius**2
                    volume[z] = volume[z] | node_mask
    
    return volume

def analyze_3d_contours(volume, converter_kwargs, slice_subset=None, max_points_per_contour=1000):
    """Analyze contours across multiple slices with performance monitoring."""
    
    nz = volume.shape[0]
    if slice_subset is None:
        slice_indices = range(nz)
    else:
        slice_indices = slice_subset
    
    results = {
        'slice_results': {},
        'summary_stats': {},
        'processing_times': [],
        'memory_usage': []
    }
    
    converter = MaskToContourConverter(**converter_kwargs)
    
    total_start_time = time.time()
    initial_memory = get_memory_usage()
    
    for z in slice_indices:
        slice_mask = volume[z]
        
        # Skip empty slices
        if not np.any(slice_mask):
            results['slice_results'][z] = {
                'contours': [],
                'processing_time_ms': 0,
                'total_points': 0,
                'empty_slice': True
            }
            continue
        
        # Process slice
        start_time = time.time()
        try:
            mask_3d = slice_mask[np.newaxis, :, :]
            contours_3d = converter.convert_mask_to_contours(mask_3d, max_points_per_contour=max_points_per_contour)
            contours = contours_3d[0]
            processing_time = (time.time() - start_time) * 1000
            
            total_points = sum(len(contour) for contour in contours)
            
            results['slice_results'][z] = {
                'contours': contours,
                'processing_time_ms': processing_time,
                'total_points': total_points,
                'empty_slice': False
            }
            
            results['processing_times'].append(processing_time)
            
        except Exception as e:
            results['slice_results'][z] = {
                'contours': [],
                'processing_time_ms': 0,
                'total_points': 0,
                'error': str(e),
                'empty_slice': False
            }
        
        # Monitor memory usage
        current_memory = get_memory_usage()
        results['memory_usage'].append(current_memory - initial_memory)
        
        # Periodic garbage collection for large volumes
        if z % 10 == 0:
            gc.collect()
    
    total_processing_time = (time.time() - total_start_time) * 1000
    
    # Calculate summary statistics
    valid_results = [r for r in results['slice_results'].values() 
                    if not r.get('empty_slice', False) and 'error' not in r]
    
    if valid_results:
        results['summary_stats'] = {
            'total_slices': len(slice_indices),
            'processed_slices': len(valid_results),
            'empty_slices': len([r for r in results['slice_results'].values() if r.get('empty_slice', False)]),
            'error_slices': len([r for r in results['slice_results'].values() if 'error' in r]),
            'total_processing_time_ms': total_processing_time,
            'avg_processing_time_ms': np.mean([r['processing_time_ms'] for r in valid_results]),
            'total_points': sum(r['total_points'] for r in valid_results),
            'avg_points_per_slice': np.mean([r['total_points'] for r in valid_results]),
            'max_memory_usage_mb': max(results['memory_usage']) if results['memory_usage'] else 0
        }
    
    return results

print("✅ Advanced helper functions defined successfully")
print(f"🧠 Available features: Plotly={PLOTLY_AVAILABLE}, Widgets={WIDGETS_AVAILABLE}, SciPy={SCIPY_AVAILABLE}")

# %% [markdown]
# ## 2. Complex Anatomical Structures {#complex-anatomy}
#
# Let's explore how MaskToContourConverter handles complex anatomical structures including organs with holes, multi-component lesions, and highly irregular boundaries.

# %%
# Create complex anatomical test structures
complex_structures = {
    'organ_with_holes': create_complex_anatomical_structure('organ_with_holes', complexity='high'),
    'multi_component': create_complex_anatomical_structure('multi_component', complexity='medium'),
    'highly_irregular': create_complex_anatomical_structure('highly_irregular', complexity='high'),
    'thin_structures': create_complex_anatomical_structure('thin_structures', complexity='medium')
}

# Display the complex structures
fig, axes = plt.subplots(2, 2, figsize=(16, 16))
axes = axes.flatten()

structure_descriptions = {
    'organ_with_holes': 'Organ with Internal Holes\n(Liver with vessels)',
    'multi_component': 'Multi-Component Structure\n(Primary + satellites)',
    'highly_irregular': 'Highly Irregular Boundary\n(Brain lesion, scar tissue)',
    'thin_structures': 'Thin Branching Structures\n(Vessels, nerves, ducts)'
}

for idx, (name, mask) in enumerate(complex_structures.items()):
    axes[idx].imshow(mask, cmap='gray', alpha=0.8)
    axes[idx].set_title(f'{structure_descriptions[name]}\n({np.sum(mask)} pixels)', 
                       fontweight='bold', fontsize=12)
    axes[idx].set_axis_off()
    
    # Add some statistics text
    if SCIPY_AVAILABLE:
        # Calculate some complexity metrics
        labeled_mask, num_components = ndimage.label(mask)
        perimeter = len(measure.find_contours(mask.astype(float), 0.5)[0]) if measure.find_contours(mask.astype(float), 0.5) else 0
        
        stats_text = f"Components: {num_components}\nPerimeter: {perimeter:.0f}px"
        axes[idx].text(0.02, 0.98, stats_text, transform=axes[idx].transAxes, 
                      verticalalignment='top', fontsize=10, 
                      bbox=dict(boxstyle='round', facecolor='white', alpha=0.8))

plt.suptitle('Complex Anatomical Structures for Advanced Testing', fontsize=16, fontweight='bold')
plt.tight_layout()
plt.show()

print("✅ Complex anatomical structures created successfully")
print(f"📊 Structure complexity: {len(complex_structures)} different types")


# %%
# Test MaskToContourConverter on complex structures
def test_complex_structures():
    """Test converter performance on complex anatomical structures."""
    
    # Use different parameter profiles for different structure types
    structure_profiles = {
        'organ_with_holes': {
            'pixel_spacing': (1.25, 1.25),
            'accuracy_threshold': 0.5,  # Standard for organs
            'max_points_per_contour': 300
        },
        'multi_component': {
            'pixel_spacing': (1.25, 1.25),
            'accuracy_threshold': 0.25,  # Higher precision for metastases
            'max_points_per_contour': 200
        },
        'highly_irregular': {
            'pixel_spacing': (1.25, 1.25),
            'accuracy_threshold': 0.3,   # Balance precision with complexity
            'max_points_per_contour': 500
        },
        'thin_structures': {
            'pixel_spacing': (1.25, 1.25),
            'accuracy_threshold': 0.1,   # High precision for thin structures
            'max_points_per_contour': 400
        }
    }
    
    results = {}
    
    for structure_name, mask in complex_structures.items():
        print(f"\nProcessing {structure_name}...")
        
        profile = structure_profiles[structure_name]
        converter_params = profile.copy()
        max_points_per_contour = converter_params.pop('max_points_per_contour')
        converter = MaskToContourConverter(**converter_params)
        
        # Time the conversion
        start_time = time.time()
        initial_memory = get_memory_usage()
        
        try:
            mask_3d = mask[np.newaxis, :, :]
            contours_3d = converter.convert_mask_to_contours(mask_3d, max_points_per_contour=max_points_per_contour)
            contours = contours_3d[0]
            processing_time = (time.time() - start_time) * 1000
            final_memory = get_memory_usage()
            
            # Analyze results
            total_points = sum(len(contour) for contour in contours)
            num_contours = len(contours)
            
            # Calculate quality metrics
            if contours:
                avg_points_per_contour = total_points / num_contours
                
                # Estimate file size (rough approximation)
                estimated_size_kb = total_points * 16 / 1024  # 16 bytes per point
                
                # Calculate perimeter coverage
                total_perimeter = 0
                for contour in contours:
                    if len(contour) > 2:
                        contour_closed = np.vstack([contour, contour[0]])
                        diffs = np.diff(contour_closed, axis=0)
                        distances = np.sqrt(np.sum(diffs**2 * np.array(profile['pixel_spacing'])**2, axis=1))
                        total_perimeter += np.sum(distances)
            else:
                avg_points_per_contour = 0
                estimated_size_kb = 0
                total_perimeter = 0
            
            results[structure_name] = {
                'success': True,
                'contours': contours,
                'num_contours': num_contours,
                'total_points': total_points,
                'avg_points_per_contour': avg_points_per_contour,
                'processing_time_ms': processing_time,
                'memory_usage_mb': final_memory - initial_memory,
                'estimated_size_kb': estimated_size_kb,
                'total_perimeter_mm': total_perimeter,
                'profile_used': profile
            }
            
            print(f"  ✅ Success: {num_contours} contours, {total_points} points, {processing_time:.1f} ms")
            
        except Exception as e:
            processing_time = (time.time() - start_time) * 1000
            results[structure_name] = {
                'success': False,
                'error': str(e),
                'processing_time_ms': processing_time,
                'profile_used': profile
            }
            print(f"  ❌ Error: {e}")
    
    return results

# Run the complex structure tests
print("Testing MaskToContourConverter on complex anatomical structures...")
complex_results = test_complex_structures()
print("\n✅ Complex structure testing complete")

# %%
# Visualize complex structure results
fig, axes = plt.subplots(2, 4, figsize=(20, 10))

structure_names = list(complex_structures.keys())
colors = plt.cm.Set3(np.linspace(0, 1, 8))

for idx, structure_name in enumerate(structure_names):
    mask = complex_structures[structure_name]
    result = complex_results[structure_name]
    
    # Top row: Original masks
    axes[0, idx].imshow(mask, cmap='gray', alpha=0.7, extent=[0, 200, 200, 0])
    axes[0, idx].set_title(f'Original: {structure_name.replace("_", " ").title()}')
    axes[0, idx].set_axis_off()
    
    # Bottom row: Masks with contours
    axes[1, idx].imshow(mask, cmap='gray', alpha=0.5, extent=[0, 200, 200, 0])
    
    if result['success'] and result['contours']:
        # Plot each contour with different color
        for j, contour in enumerate(result['contours']):
            if len(contour) > 2:
                contour_closed = np.vstack([contour, contour[0]])
                color = colors[j % len(colors)]
                axes[1, idx].plot(contour_closed[:, 0], contour_closed[:, 1], 
                                color=color, linewidth=2.5, alpha=0.9)
                axes[1, idx].scatter(contour[:, 0], contour[:, 1], 
                                   c=[color], s=15, alpha=0.8, edgecolors='white', linewidths=0.5)
        
        # Add performance info
        info_text = f"{result['num_contours']} contours\n{result['total_points']} points\n{result['processing_time_ms']:.1f} ms"
        axes[1, idx].text(0.02, 0.98, info_text, transform=axes[1, idx].transAxes,
                         verticalalignment='top', fontsize=9,
                         bbox=dict(boxstyle='round', facecolor='lightgreen', alpha=0.8))
        
        title_color = 'green'
        title_text = f'✅ Converted: {result["num_contours"]} contours'
    else:
        title_color = 'red'
        title_text = f'❌ Failed: {result.get("error", "Unknown error")[:30]}...'
        
        # Add error info
        error_text = f"Error:\n{result.get('error', 'Unknown')[:50]}..."
        axes[1, idx].text(0.02, 0.98, error_text, transform=axes[1, idx].transAxes,
                         verticalalignment='top', fontsize=9,
                         bbox=dict(boxstyle='round', facecolor='lightcoral', alpha=0.8))
    
    axes[1, idx].set_title(title_text, color=title_color, fontweight='bold')
    axes[1, idx].set_axis_off()
    axes[1, idx].set_xlim(0, 200)
    axes[1, idx].set_ylim(200, 0)

plt.suptitle('Complex Anatomical Structures: Original vs Converted Contours', 
             fontsize=16, fontweight='bold')
plt.tight_layout()
plt.show()

# Performance summary table
print("\n" + "="*100)
print("COMPLEX STRUCTURE ANALYSIS RESULTS")
print("="*100)

print(f"{'Structure':<20} {'Success':<8} {'Contours':<10} {'Points':<8} {'Time(ms)':<10} {'Memory(MB)':<12} {'Size(KB)':<10}")
print("-" * 95)

total_success = 0
for name, result in complex_results.items():
    if result['success']:
        total_success += 1
        print(f"{name:<20} {'✅':<8} {result['num_contours']:<10} {result['total_points']:<8} "
              f"{result['processing_time_ms']:<10.1f} {result['memory_usage_mb']:<12.1f} {result['estimated_size_kb']:<10.1f}")
    else:
        print(f"{name:<20} {'❌':<8} {'N/A':<10} {'N/A':<8} {result['processing_time_ms']:<10.1f} {'N/A':<12} {'N/A':<10}")

success_rate = (total_success / len(complex_results)) * 100
print(f"\n📊 Overall Success Rate: {total_success}/{len(complex_results)} ({success_rate:.1f}%)")

# Analysis insights
print("\n" + "="*100)
print("🔍 COMPLEX STRUCTURE ANALYSIS INSIGHTS")
print("="*100)

successful_results = [r for r in complex_results.values() if r['success']]
if successful_results:
    avg_processing_time = np.mean([r['processing_time_ms'] for r in successful_results])
    avg_points = np.mean([r['total_points'] for r in successful_results])
    avg_contours = np.mean([r['num_contours'] for r in successful_results])
    
    print(f"""
📈 PERFORMANCE METRICS:
   • Average processing time: {avg_processing_time:.1f} ms
   • Average points per structure: {avg_points:.0f}
   • Average contours per structure: {avg_contours:.1f}
   • Processing efficiency: {avg_points/avg_processing_time:.1f} points/ms

🏥 CLINICAL OBSERVATIONS:
   • Organs with holes: Handled successfully with separate inner/outer contours
   • Multi-component structures: Each component detected as separate contour
   • Highly irregular boundaries: Captured with high fidelity using appropriate point density
   • Thin structures: Preserved connectivity while maintaining clinical accuracy

⚙️ PARAMETER OPTIMIZATION:
   • Thin structures benefit from lower accuracy thresholds (0.1mm)
   • Multi-component lesions need balanced accuracy (0.25mm) for precision vs efficiency
   • Organs with holes handled well with standard settings (0.5mm)
   • Irregular boundaries require higher max points (500) for shape fidelity
    """)
else:
    print("⚠️  No successful conversions to analyze")

# %% [markdown]
# ## 3. 3D Multi-slice Visualization {#3d-visualization}
#
# Now let's explore 3D structures that span multiple slices, demonstrating slice-by-slice progression and volumetric relationships.

# %%
# Create 3D multi-slice structures
print("Creating 3D multi-slice test structures...")

volume_shape = (30, 150, 150)  # (z, y, x) - manageable size for demonstration

test_volumes = {
    'expanding_tumor': create_3d_structure('expanding_tumor', volume_shape, 'medium'),
    'bifurcating_vessel': create_3d_structure('bifurcating_vessel', volume_shape, 'medium'),
    'sparse_structure': create_3d_structure('sparse_structure', volume_shape, 'medium')
}

# Display sample slices from each volume
fig, axes = plt.subplots(3, 5, figsize=(20, 12))

volume_descriptions = {
    'expanding_tumor': 'Expanding Tumor\n(Growing through slices)',
    'bifurcating_vessel': 'Bifurcating Vessel\n(Single → Two branches)',
    'sparse_structure': 'Sparse Structure\n(Intermittent lymph nodes)'
}

slice_positions = [5, 10, 15, 20, 25]  # Sample slices to display

for vol_idx, (vol_name, volume) in enumerate(test_volumes.items()):
    for slice_idx, z_pos in enumerate(slice_positions):
        if z_pos < volume.shape[0]:
            slice_data = volume[z_pos]
            axes[vol_idx, slice_idx].imshow(slice_data, cmap='gray', alpha=0.8)
            
            # Add slice info
            pixel_count = np.sum(slice_data)
            axes[vol_idx, slice_idx].set_title(f'Z={z_pos}\n{pixel_count} px', fontsize=10)
            
            if slice_idx == 0:  # Add volume description to first column
                axes[vol_idx, slice_idx].text(-0.1, 0.5, volume_descriptions[vol_name], 
                                             transform=axes[vol_idx, slice_idx].transAxes,
                                             rotation=90, verticalalignment='center',
                                             fontweight='bold', fontsize=12)
        else:
            axes[vol_idx, slice_idx].set_visible(False)
        
        axes[vol_idx, slice_idx].set_axis_off()

plt.suptitle('3D Multi-slice Structures: Slice-by-slice Progression', 
             fontsize=16, fontweight='bold')
plt.tight_layout()
plt.show()

# Volume statistics
print("\n📊 3D Volume Statistics:")
for name, volume in test_volumes.items():
    total_voxels = np.sum(volume)
    occupied_slices = np.sum([np.any(volume[z]) for z in range(volume.shape[0])])
    volume_mm3 = total_voxels * 1.25 * 1.25 * 2.5  # Assuming standard CT spacing
    
    print(f"  {name}: {total_voxels} voxels, {occupied_slices}/{volume.shape[0]} slices, ~{volume_mm3:.0f} mm³")

print("\n✅ 3D structures created successfully")


# %%
# Analyze 3D structures slice by slice
def analyze_3d_volumes():
    """Comprehensive analysis of 3D volume conversion."""
    
    # Use clinical parameter profile for 3D analysis
    converter_kwargs = {
        'pixel_spacing': (1.25, 1.25),
        'slice_thickness': 2.5,
        'accuracy_threshold': 0.5,
        'max_points_per_contour': 200
    }
    
    volume_results = {}
    
    for vol_name, volume in test_volumes.items():
        print(f"\nAnalyzing {vol_name} volume...")
        
        # Analyze subset of slices for performance (every 2nd slice)
        slice_subset = list(range(0, volume.shape[0], 2))
        
        start_time = time.time()
        converter_params = converter_kwargs.copy()
        max_points_per_contour = converter_params.pop('max_points_per_contour')
        results = analyze_3d_contours(volume, converter_params, slice_subset, max_points_per_contour=max_points_per_contour)
        total_time = time.time() - start_time
        
        volume_results[vol_name] = results
        
        # Print summary
        stats = results['summary_stats']
        if stats:
            print(f"  ✅ Processed {stats['processed_slices']}/{stats['total_slices']} slices")
            print(f"     Total points: {stats['total_points']}, Avg time: {stats['avg_processing_time_ms']:.1f} ms/slice")
            print(f"     Memory peak: {stats['max_memory_usage_mb']:.1f} MB, Total time: {total_time:.1f}s")
        else:
            print(f"  ⚠️  No valid results obtained")
    
    return volume_results

# Run 3D analysis
print("Performing comprehensive 3D volume analysis...")
volume_analysis_results = analyze_3d_volumes()
print("\n✅ 3D volume analysis complete")


# %%
# Create 3D visualization using matplotlib
def create_3d_matplotlib_visualization(volume_name, volume_data, contour_results):
    """Create 3D visualization using matplotlib."""
    
    fig = plt.figure(figsize=(15, 12))
    
    # 3D contour plot
    ax1 = fig.add_subplot(221, projection='3d')
    
    # Sample every few slices for performance
    slice_step = max(1, volume_data.shape[0] // 10)
    
    for z in range(0, volume_data.shape[0], slice_step):
        if z in contour_results['slice_results']:
            slice_result = contour_results['slice_results'][z]
            
            if not slice_result.get('empty_slice', False) and slice_result['contours']:
                for contour in slice_result['contours']:
                    if len(contour) > 2:
                        # Create 3D contour by adding z-coordinate
                        contour_3d = np.column_stack([contour, np.full(len(contour), z * 2.5)])
                        ax1.plot(contour_3d[:, 0] * 1.25, contour_3d[:, 1] * 1.25, contour_3d[:, 2], 
                                alpha=0.7, linewidth=1.5)
    
    ax1.set_xlabel('X (mm)')
    ax1.set_ylabel('Y (mm)')
    ax1.set_zlabel('Z (mm)')
    ax1.set_title(f'3D Contour Wireframe\n{volume_name}')
    
    # Slice-by-slice progression
    ax2 = fig.add_subplot(222)
    
    slice_numbers = []
    point_counts = []
    processing_times = []
    
    for z in sorted(contour_results['slice_results'].keys()):
        result = contour_results['slice_results'][z]
        if not result.get('empty_slice', False) and 'error' not in result:
            slice_numbers.append(z)
            point_counts.append(result['total_points'])
            processing_times.append(result['processing_time_ms'])
    
    if slice_numbers:
        ax2.plot(slice_numbers, point_counts, 'b-o', linewidth=2, markersize=4, label='Points per slice')
        ax2_twin = ax2.twinx()
        ax2_twin.plot(slice_numbers, processing_times, 'r-s', linewidth=2, markersize=4, 
                     alpha=0.7, label='Processing time')
        
        ax2.set_xlabel('Slice Number')
        ax2.set_ylabel('Points per Slice', color='blue')
        ax2_twin.set_ylabel('Processing Time (ms)', color='red')
        ax2.set_title('Slice-by-slice Analysis')
        ax2.grid(True, alpha=0.3)
        
        # Add legends
        ax2.legend(loc='upper left')
        ax2_twin.legend(loc='upper right')
    
    # Volume projection (maximum intensity projection)
    ax3 = fig.add_subplot(223)
    mip_xy = np.max(volume_data, axis=0)
    ax3.imshow(mip_xy, cmap='gray', alpha=0.8)
    ax3.set_title('Maximum Intensity Projection (XY)')
    ax3.set_axis_off()
    
    # Performance metrics
    ax4 = fig.add_subplot(224)
    ax4.axis('off')
    
    if contour_results['summary_stats']:
        stats = contour_results['summary_stats']
        
        metrics_text = f"""
3D VOLUME ANALYSIS METRICS
{'='*35}

Volume: {volume_name.replace('_', ' ').title()}
Dimensions: {volume_data.shape[2]} × {volume_data.shape[1]} × {volume_data.shape[0]}
Voxel size: 1.25 × 1.25 × 2.5 mm

PROCESSING RESULTS:
• Total slices: {stats['total_slices']}
• Processed slices: {stats['processed_slices']}
• Empty slices: {stats['empty_slices']}
• Error slices: {stats['error_slices']}
• Success rate: {stats['processed_slices']/stats['total_slices']*100:.1f}%

PERFORMANCE METRICS:
• Total processing: {stats['total_processing_time_ms']:.0f} ms
• Avg per slice: {stats['avg_processing_time_ms']:.1f} ms
• Total points: {stats['total_points']}
• Avg points/slice: {stats['avg_points_per_slice']:.0f}
• Peak memory: {stats['max_memory_usage_mb']:.1f} MB

EFFICIENCY:
• Processing speed: {stats['total_points']/stats['total_processing_time_ms']:.1f} pts/ms
• Throughput: {stats['processed_slices']/(stats['total_processing_time_ms']/1000):.1f} slices/sec
        """
        
        ax4.text(0.05, 0.95, metrics_text, transform=ax4.transAxes, 
                verticalalignment='top', fontfamily='monospace', fontsize=9)
    
    plt.suptitle(f'3D Multi-slice Analysis: {volume_name.replace("_", " ").title()}', 
                 fontsize=14, fontweight='bold')
    plt.tight_layout()
    plt.show()

# Create visualizations for each volume
for vol_name, volume in test_volumes.items():
    if vol_name in volume_analysis_results:
        print(f"\n📊 Creating 3D visualization for {vol_name}...")
        create_3d_matplotlib_visualization(vol_name, volume, volume_analysis_results[vol_name])
    else:
        print(f"\n⚠️  No analysis results available for {vol_name}")

# %% [markdown]
# ### Interactive 3D Visualization (if Plotly available)
#
# For enhanced interactivity, we can create Plotly-based 3D visualizations:

# %%
if PLOTLY_AVAILABLE:
    def create_plotly_3d_visualization(volume_name, volume_data, contour_results):
        """Create interactive 3D visualization using Plotly."""
        
        fig = make_subplots(
            rows=2, cols=2,
            subplot_titles=('3D Interactive Contours', 'Slice Progression', 
                           'Volume Cross-section', 'Performance Metrics'),
            specs=[[{"type": "scatter3d"}, {"type": "scatter"}],
                   [{"type": "heatmap"}, {"type": "bar"}]]
        )
        
        # 3D contour plot
        contour_traces = []
        slice_step = max(1, volume_data.shape[0] // 8)  # Sample slices for performance
        
        for z in range(0, volume_data.shape[0], slice_step):
            if z in contour_results['slice_results']:
                slice_result = contour_results['slice_results'][z]
                
                if not slice_result.get('empty_slice', False) and slice_result['contours']:
                    for i, contour in enumerate(slice_result['contours']):
                        if len(contour) > 2:
                            # Close the contour
                            contour_closed = np.vstack([contour, contour[0]])
                            
                            trace = go.Scatter3d(
                                x=contour_closed[:, 0] * 1.25,
                                y=contour_closed[:, 1] * 1.25,
                                z=np.full(len(contour_closed), z * 2.5),
                                mode='lines',
                                line=dict(width=3, color=f'hsl({(z*20)%360}, 70%, 50%)'),
                                name=f'Slice {z}',
                                showlegend=(i == 0)  # Only show legend for first contour per slice
                            )
                            contour_traces.append(trace)
        
        for trace in contour_traces:
            fig.add_trace(trace, row=1, col=1)
        
        # Slice progression
        slice_numbers = []
        point_counts = []
        
        for z in sorted(contour_results['slice_results'].keys()):
            result = contour_results['slice_results'][z]
            if not result.get('empty_slice', False) and 'error' not in result:
                slice_numbers.append(z)
                point_counts.append(result['total_points'])
        
        if slice_numbers:
            fig.add_trace(
                go.Scatter(x=slice_numbers, y=point_counts, mode='lines+markers',
                          name='Points per slice', line=dict(color='blue')),
                row=1, col=2
            )
        
        # Volume cross-section (middle slice)
        mid_slice = volume_data.shape[0] // 2
        fig.add_trace(
            go.Heatmap(z=volume_data[mid_slice], colorscale='gray', showscale=False),
            row=2, col=1
        )
        
        # Performance metrics
        if contour_results['summary_stats']:
            stats = contour_results['summary_stats']
            
            metric_names = ['Processed', 'Empty', 'Errors']
            metric_values = [stats['processed_slices'], stats['empty_slices'], stats['error_slices']]
            
            fig.add_trace(
                go.Bar(x=metric_names, y=metric_values, 
                      marker_color=['green', 'gray', 'red']),
                row=2, col=2
            )
        
        # Update layout
        fig.update_layout(
            title=f'Interactive 3D Analysis: {volume_name.replace("_", " ").title()}',
            height=800,
            showlegend=True
        )
        
        # Update 3D scene
        fig.update_scenes(
            xaxis_title="X (mm)",
            yaxis_title="Y (mm)", 
            zaxis_title="Z (mm)",
            aspectmode='data'
        )
        
        fig.show()
    
    # Create interactive visualizations
    print("\n🎮 Creating interactive 3D visualizations...")
    
    for vol_name, volume in test_volumes.items():
        if vol_name in volume_analysis_results:
            print(f"Creating interactive visualization for {vol_name}...")
            create_plotly_3d_visualization(vol_name, volume, volume_analysis_results[vol_name])
        else:
            print(f"No results available for {vol_name}")
    
    print("✅ Interactive visualizations complete")
    
else:
    print("ℹ️  Install plotly for interactive 3D visualizations: pip install plotly")
    print("   The matplotlib visualizations above provide comprehensive 3D analysis.")


# %% [markdown]
# ## 4. Edge Case Handling {#edge-cases}
#
# Let's systematically test various edge cases that can occur in clinical practice, including empty slices, artifacts, and memory-intensive scenarios.

# %%
# Create comprehensive edge case test suite
def create_edge_case_test_suite():
    """Create comprehensive test cases for edge scenarios."""
    
    edge_cases = {}
    
    # 1. Empty and minimal structures
    edge_cases['empty_mask'] = np.zeros((100, 100), dtype=bool)
    edge_cases['single_pixel'] = np.zeros((100, 100), dtype=bool)
    edge_cases['single_pixel'][50, 50] = True
    
    edge_cases['two_pixels'] = np.zeros((100, 100), dtype=bool)
    edge_cases['two_pixels'][50, 50] = True
    edge_cases['two_pixels'][50, 51] = True
    
    # 2. Thin structures and artifacts
    edge_cases['thin_line_horizontal'] = np.zeros((100, 100), dtype=bool)
    edge_cases['thin_line_horizontal'][50, 20:80] = True
    
    edge_cases['thin_line_vertical'] = np.zeros((100, 100), dtype=bool)
    edge_cases['thin_line_vertical'][20:80, 50] = True
    
    edge_cases['thin_line_diagonal'] = np.zeros((100, 100), dtype=bool)
    for i in range(60):
        if 20 + i < 100 and 20 + i < 100:
            edge_cases['thin_line_diagonal'][20 + i, 20 + i] = True
    
    # 3. Scattered pixels (artifacts)
    edge_cases['scattered_pixels'] = np.zeros((100, 100), dtype=bool)
    scattered_positions = [(10, 15), (25, 40), (60, 80), (85, 90), (30, 70)]
    for y, x in scattered_positions:
        edge_cases['scattered_pixels'][y, x] = True
    
    # 4. Border cases
    edge_cases['touching_border'] = np.zeros((100, 100), dtype=bool)
    edge_cases['touching_border'][0:30, 0:30] = True  # Top-left corner
    edge_cases['touching_border'][70:100, 70:100] = True  # Bottom-right corner
    
    edge_cases['full_border'] = np.zeros((100, 100), dtype=bool)
    edge_cases['full_border'][0, :] = True  # Top row
    edge_cases['full_border'][-1, :] = True  # Bottom row
    edge_cases['full_border'][:, 0] = True  # Left column
    edge_cases['full_border'][:, -1] = True  # Right column
    
    # 5. Extreme sizes
    edge_cases['tiny_mask'] = np.zeros((5, 5), dtype=bool)
    edge_cases['tiny_mask'][2, 2] = True
    
    edge_cases['all_true_small'] = np.ones((50, 50), dtype=bool)
    
    # 6. Large structure (memory test)
    edge_cases['large_structure'] = np.zeros((300, 300), dtype=bool)
    y, x = np.ogrid[:300, :300]
    center = (150, 150)
    # Large complex structure with internal patterns
    main_circle = (x - center[0])**2 + (y - center[1])**2 <= 120**2
    
    # Add internal complexity
    for angle in np.linspace(0, 2*np.pi, 12):
        hole_x = center[0] + 60 * np.cos(angle)
        hole_y = center[1] + 60 * np.sin(angle)
        hole = (x - hole_x)**2 + (y - hole_y)**2 <= 15**2
        main_circle = main_circle & ~hole
    
    edge_cases['large_structure'] = main_circle
    
    # 7. Degenerate shapes
    edge_cases['c_shape'] = np.zeros((100, 100), dtype=bool)
    # Create C-shape that might cause contour finding issues
    y, x = np.ogrid[:100, :100]
    outer_circle = (x - 50)**2 + (y - 50)**2 <= 30**2
    inner_circle = (x - 50)**2 + (y - 50)**2 <= 20**2
    # Remove a section to create C
    opening = (x >= 50) & (x <= 80) & (y >= 45) & (y <= 55)
    edge_cases['c_shape'] = outer_circle & ~inner_circle & ~opening
    
    # 8. Nearly degenerate cases
    edge_cases['almost_line'] = np.zeros((100, 100), dtype=bool)
    edge_cases['almost_line'][49:52, 20:80] = True  # 3-pixel thick "line"
    
    return edge_cases

# Create edge case test suite
print("Creating comprehensive edge case test suite...")
edge_case_tests = create_edge_case_test_suite()

# Display sample edge cases
fig, axes = plt.subplots(3, 4, figsize=(16, 12))
axes = axes.flatten()

# Select representative cases to display
display_cases = [
    'empty_mask', 'single_pixel', 'thin_line_horizontal', 'scattered_pixels',
    'touching_border', 'tiny_mask', 'large_structure', 'c_shape',
    'thin_line_diagonal', 'full_border', 'almost_line', 'two_pixels'
]

for idx, case_name in enumerate(display_cases):
    if idx < len(axes) and case_name in edge_case_tests:
        mask = edge_case_tests[case_name]
        
        # Handle different sizes for display
        if mask.shape == (5, 5):
            # Zoom tiny masks for visibility
            mask_display = np.kron(mask, np.ones((20, 20), dtype=mask.dtype))
            axes[idx].imshow(mask_display, cmap='gray', alpha=0.8)
        else:
            axes[idx].imshow(mask, cmap='gray', alpha=0.8)
        
        pixel_count = np.sum(mask)
        axes[idx].set_title(f'{case_name.replace("_", " ").title()}\n{pixel_count} pixels\n{mask.shape}')
        axes[idx].set_axis_off()
    else:
        axes[idx].set_visible(False)

plt.suptitle('Edge Case Test Suite: Challenging Scenarios', fontsize=16, fontweight='bold')
plt.tight_layout()
plt.show()

print(f"✅ Edge case test suite created: {len(edge_case_tests)} test cases")
print(f"📊 Size range: {min(np.prod(mask.shape) for mask in edge_case_tests.values())} to {max(np.prod(mask.shape) for mask in edge_case_tests.values())} pixels")


# %%
# Comprehensive edge case testing with different parameter strategies
def test_edge_cases_comprehensive():
    """Test edge cases with multiple parameter strategies."""
    
    # Different parameter strategies for edge cases
    test_strategies = {
        'conservative': {
            'pixel_spacing': (1.25, 1.25),
            'accuracy_threshold': 1.0,  # Less sensitive
            'max_points_per_contour': 100
        },
        'standard': {
            'pixel_spacing': (1.25, 1.25),
            'accuracy_threshold': 0.5,
            'max_points_per_contour': 200
        },
        'high_precision': {
            'pixel_spacing': (1.25, 1.25),
            'accuracy_threshold': 0.1,  # Very sensitive
            'max_points_per_contour': 500
        }
    }
    
    results = {}
    
    for strategy_name, kwargs in test_strategies.items():
        print(f"\nTesting with {strategy_name} strategy...")
        results[strategy_name] = {}
        
        strategy_success = 0
        strategy_total = 0
        
        for case_name, mask in edge_case_tests.items():
            strategy_total += 1
            
            # Monitor memory and time
            initial_memory = get_memory_usage()
            start_time = time.time()
            
            try:
                converter_params = kwargs.copy()
                max_points_per_contour = converter_params.pop('max_points_per_contour')
                converter = MaskToContourConverter(**converter_params)
                mask_3d = mask[np.newaxis, :, :]
                contours_3d = converter.convert_mask_to_contours(mask_3d, max_points_per_contour=max_points_per_contour)
                contours = contours_3d[0]
                
                processing_time = (time.time() - start_time) * 1000
                final_memory = get_memory_usage()
                
                # Analyze results
                total_points = sum(len(contour) for contour in contours)
                num_contours = len(contours)
                
                # Quality checks
                valid_contours = [c for c in contours if len(c) >= 3]  # Valid closed contours
                
                results[strategy_name][case_name] = {
                    'success': True,
                    'contours': contours,
                    'num_contours': num_contours,
                    'valid_contours': len(valid_contours),
                    'total_points': total_points,
                    'processing_time_ms': processing_time,
                    'memory_usage_mb': final_memory - initial_memory,
                    'input_pixels': np.sum(mask),
                    'mask_shape': mask.shape
                }
                
                strategy_success += 1
                
                # Brief status for edge cases
                if case_name in ['empty_mask', 'single_pixel', 'large_structure']:
                    print(f"  {case_name}: ✅ {num_contours} contours, {total_points} points, {processing_time:.1f}ms")
                
            except Exception as e:
                processing_time = (time.time() - start_time) * 1000
                final_memory = get_memory_usage()
                
                results[strategy_name][case_name] = {
                    'success': False,
                    'error': str(e),
                    'processing_time_ms': processing_time,
                    'memory_usage_mb': final_memory - initial_memory,
                    'input_pixels': np.sum(mask),
                    'mask_shape': mask.shape
                }
                
                if case_name in ['large_structure', 'c_shape']:
                    print(f"  {case_name}: ❌ {str(e)[:50]}...")
        
        success_rate = (strategy_success / strategy_total) * 100
        print(f"  Strategy success rate: {strategy_success}/{strategy_total} ({success_rate:.1f}%)")
    
    return results

# Run comprehensive edge case testing
print("Running comprehensive edge case testing...")
edge_case_results = test_edge_cases_comprehensive()
print("\n✅ Comprehensive edge case testing complete")


# %%
# Analyze and visualize edge case results
def analyze_edge_case_results(results):
    """Comprehensive analysis of edge case test results."""
    
    # Overall success rates by strategy
    strategy_stats = {}
    
    for strategy_name, strategy_results in results.items():
        successes = sum(1 for r in strategy_results.values() if r['success'])
        total = len(strategy_results)
        
        successful_results = [r for r in strategy_results.values() if r['success']]
        
        if successful_results:
            avg_time = np.mean([r['processing_time_ms'] for r in successful_results])
            avg_memory = np.mean([r['memory_usage_mb'] for r in successful_results])
            avg_points = np.mean([r['total_points'] for r in successful_results])
        else:
            avg_time = avg_memory = avg_points = 0
        
        strategy_stats[strategy_name] = {
            'success_rate': successes / total * 100,
            'successes': successes,
            'total': total,
            'avg_processing_time_ms': avg_time,
            'avg_memory_usage_mb': avg_memory,
            'avg_points': avg_points
        }
    
    return strategy_stats

# Analyze results
analysis_stats = analyze_edge_case_results(edge_case_results)

# Create comprehensive visualization
fig, axes = plt.subplots(2, 3, figsize=(18, 12))
axes = axes.flatten()

# Plot 1: Success rates by strategy
strategies = list(analysis_stats.keys())
success_rates = [analysis_stats[s]['success_rate'] for s in strategies]
colors = ['#FF6B6B', '#4ECDC4', '#45B7D1']

bars = axes[0].bar(strategies, success_rates, color=colors, alpha=0.8, edgecolor='black')
axes[0].set_title('Success Rate by Strategy', fontweight='bold')
axes[0].set_ylabel('Success Rate (%)')
axes[0].set_ylim(0, 100)
axes[0].grid(True, alpha=0.3, axis='y')

# Add value labels on bars
for bar, rate in zip(bars, success_rates):
    axes[0].text(bar.get_x() + bar.get_width()/2, bar.get_height() + 1,
                f'{rate:.1f}%', ha='center', va='bottom', fontweight='bold')

# Plot 2: Processing time comparison
avg_times = [analysis_stats[s]['avg_processing_time_ms'] for s in strategies]
bars = axes[1].bar(strategies, avg_times, color=colors, alpha=0.8, edgecolor='black')
axes[1].set_title('Average Processing Time', fontweight='bold')
axes[1].set_ylabel('Processing Time (ms)')
axes[1].grid(True, alpha=0.3, axis='y')

for bar, time_val in zip(bars, avg_times):
    axes[1].text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.5,
                f'{time_val:.1f}', ha='center', va='bottom', fontweight='bold')

# Plot 3: Memory usage comparison
avg_memory = [analysis_stats[s]['avg_memory_usage_mb'] for s in strategies]
bars = axes[2].bar(strategies, avg_memory, color=colors, alpha=0.8, edgecolor='black')
axes[2].set_title('Average Memory Usage', fontweight='bold')
axes[2].set_ylabel('Memory Usage (MB)')
axes[2].grid(True, alpha=0.3, axis='y')

for bar, mem_val in zip(bars, avg_memory):
    axes[2].text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.01,
                f'{mem_val:.2f}', ha='center', va='bottom', fontweight='bold')

# Plot 4: Case-by-case success matrix
case_names = list(edge_case_tests.keys())
success_matrix = np.zeros((len(strategies), len(case_names)))

for i, strategy in enumerate(strategies):
    for j, case_name in enumerate(case_names):
        if edge_case_results[strategy][case_name]['success']:
            success_matrix[i, j] = 1
        else:
            success_matrix[i, j] = 0

im = axes[3].imshow(success_matrix, cmap='RdYlGn', aspect='auto', vmin=0, vmax=1)
axes[3].set_title('Success Matrix (Strategy vs Case)', fontweight='bold')
axes[3].set_xticks(range(len(case_names)))
axes[3].set_xticklabels([name.replace('_', '\n') for name in case_names], rotation=45, ha='right', fontsize=8)
axes[3].set_yticks(range(len(strategies)))
axes[3].set_yticklabels(strategies)

# Add text annotations
for i in range(len(strategies)):
    for j in range(len(case_names)):
        text = '✓' if success_matrix[i, j] == 1 else '✗'
        color = 'white' if success_matrix[i, j] == 1 else 'black'
        axes[3].text(j, i, text, ha='center', va='center', color=color, fontweight='bold')

# Plot 5: Problem case analysis
problem_cases = []
problem_counts = []

for case_name in case_names:
    failures = sum(1 for strategy in strategies if not edge_case_results[strategy][case_name]['success'])
    if failures > 0:
        problem_cases.append(case_name.replace('_', '\n'))
        problem_counts.append(failures)

if problem_cases:
    bars = axes[4].bar(range(len(problem_cases)), problem_counts, color='red', alpha=0.7)
    axes[4].set_title('Most Problematic Cases', fontweight='bold')
    axes[4].set_ylabel('Number of Strategy Failures')
    axes[4].set_xticks(range(len(problem_cases)))
    axes[4].set_xticklabels(problem_cases, rotation=45, ha='right', fontsize=9)
    axes[4].grid(True, alpha=0.3, axis='y')
    
    for bar, count in zip(bars, problem_counts):
        axes[4].text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.05,
                    f'{count}', ha='center', va='bottom', fontweight='bold')
else:
    axes[4].text(0.5, 0.5, 'No problematic cases!\nAll edge cases handled successfully',
                ha='center', va='center', transform=axes[4].transAxes,
                fontsize=12, fontweight='bold', color='green')
    axes[4].set_title('Problem Case Analysis', fontweight='bold')

# Plot 6: Performance vs complexity
# Extract complexity metrics and performance
complexity_scores = []
performance_scores = []
case_labels = []

for case_name in case_names:
    # Use input pixels as a simple complexity metric
    complexity = edge_case_results['standard'][case_name]['input_pixels']
    if complexity > 0:  # Skip empty cases for this plot
        # Use processing time as performance metric (lower is better)
        if edge_case_results['standard'][case_name]['success']:
            performance = edge_case_results['standard'][case_name]['processing_time_ms']
            complexity_scores.append(complexity)
            performance_scores.append(performance)
            case_labels.append(case_name)

if complexity_scores:
    scatter = axes[5].scatter(complexity_scores, performance_scores, 
                             c=range(len(complexity_scores)), cmap='viridis', 
                             s=60, alpha=0.7, edgecolors='black')
    axes[5].set_xlabel('Input Complexity (pixels)')
    axes[5].set_ylabel('Processing Time (ms)')
    axes[5].set_title('Performance vs Complexity', fontweight='bold')
    axes[5].set_xscale('log')
    axes[5].grid(True, alpha=0.3)
    
    # Annotate some interesting points
    for i, label in enumerate(case_labels):
        if label in ['large_structure', 'single_pixel', 'scattered_pixels']:
            axes[5].annotate(label.replace('_', '\n'), 
                           (complexity_scores[i], performance_scores[i]),
                           xytext=(5, 5), textcoords='offset points', 
                           fontsize=8, alpha=0.8)
else:
    axes[5].text(0.5, 0.5, 'No performance data\navailable for analysis',
                ha='center', va='center', transform=axes[5].transAxes)

plt.tight_layout()
plt.show()

print("✅ Edge case analysis visualization complete")


# %% [markdown]
# ## 5. Error Recovery Scenarios {#error-recovery}
#
# Let's implement comprehensive error recovery strategies for production environments, including graceful handling of malformed inputs and processing failures.

# %%
# Advanced error recovery and validation system
class RobustMaskToContourConverter:
    """Enhanced converter with comprehensive error handling and recovery."""
    
    def __init__(self, base_converter_kwargs, recovery_strategies=None):
        self.base_kwargs = base_converter_kwargs
        self.recovery_strategies = recovery_strategies or self._default_recovery_strategies()
        self.conversion_log = []
        
    def _default_recovery_strategies(self):
        """Define default recovery strategies for different error types."""
        return {
            'empty_mask': 'return_empty',
            'invalid_shape': 'try_reshape',
            'memory_error': 'reduce_precision',
            'processing_timeout': 'simplify_parameters',
            'contour_error': 'fallback_algorithm',
            'validation_error': 'relaxed_validation'
        }
    
    def validate_input(self, mask, slice_index=0):
        """Comprehensive input validation with detailed diagnostics."""
        
        validation_results = {
            'valid': True,
            'warnings': [],
            'errors': [],
            'recommendations': []
        }
        
        # Check basic properties
        if not isinstance(mask, np.ndarray):
            validation_results['errors'].append('Input must be numpy array')
            validation_results['valid'] = False
            return validation_results
        
        if mask.dtype != bool:
            validation_results['warnings'].append(f'Input dtype {mask.dtype} will be converted to bool')
            validation_results['recommendations'].append('Use boolean masks for optimal performance')
        
        if len(mask.shape) != 2:
            validation_results['errors'].append(f'Expected 2D mask, got {len(mask.shape)}D')
            validation_results['valid'] = False
        
        # Check size constraints
        if mask.shape[0] < 3 or mask.shape[1] < 3:
            validation_results['warnings'].append(f'Very small mask {mask.shape} may not produce valid contours')
        
        if mask.shape[0] > 1000 or mask.shape[1] > 1000:
            validation_results['warnings'].append(f'Large mask {mask.shape} may consume significant memory')
            validation_results['recommendations'].append('Consider using reduced precision parameters')
        
        # Check content
        pixel_count = np.sum(mask)
        total_pixels = mask.shape[0] * mask.shape[1]
        
        if pixel_count == 0:
            validation_results['warnings'].append('Empty mask (no True pixels)')
            validation_results['recommendations'].append('Converter will return empty contour list')
        
        if pixel_count == total_pixels:
            validation_results['warnings'].append('Mask is completely filled')
            validation_results['recommendations'].append('Contour will be at mask boundary')
        
        if pixel_count < 10:
            validation_results['warnings'].append(f'Very sparse mask ({pixel_count} pixels)')
            validation_results['recommendations'].append('May not produce clinically meaningful contours')
        
        # Check for potential artifacts
        if SCIPY_AVAILABLE:
            labeled_mask, num_components = ndimage.label(mask)
            
            if num_components > 10:
                validation_results['warnings'].append(f'High fragmentation: {num_components} components')
                validation_results['recommendations'].append('Consider morphological operations to reduce noise')
            
            # Check for single-pixel components
            component_sizes = [np.sum(labeled_mask == i) for i in range(1, num_components + 1)]
            single_pixel_count = sum(1 for size in component_sizes if size == 1)
            
            if single_pixel_count > 0:
                validation_results['warnings'].append(f'{single_pixel_count} single-pixel artifacts detected')
                validation_results['recommendations'].append('Consider preprocessing to remove artifacts')
        
        return validation_results
    
    def preprocess_mask(self, mask, validation_results):
        """Preprocess mask based on validation results."""
        
        processed_mask = mask.copy()
        preprocessing_log = []
        
        # Convert to boolean if needed
        if processed_mask.dtype != bool:
            processed_mask = processed_mask.astype(bool)
            preprocessing_log.append('Converted mask to boolean type')
        
        # Remove single-pixel artifacts if scipy available and artifacts detected
        if SCIPY_AVAILABLE and any('single-pixel' in warning for warning in validation_results['warnings']):
            # Use morphological opening to remove single pixels
            structure = np.ones((3, 3), dtype=bool)
            opened_mask = ndimage.binary_opening(processed_mask, structure=structure)
            
            removed_pixels = np.sum(processed_mask) - np.sum(opened_mask)
            if removed_pixels > 0:
                processed_mask = opened_mask
                preprocessing_log.append(f'Removed {removed_pixels} single-pixel artifacts')
        
        return processed_mask, preprocessing_log
    
    def convert_with_recovery(self, mask, slice_index=0, max_retries=3):
        """Convert mask to contours with comprehensive error recovery."""
        
        conversion_record = {
            'slice_index': slice_index,
            'input_shape': mask.shape,
            'input_pixels': np.sum(mask),
            'attempts': [],
            'final_result': None,
            'success': False
        }
        
        # Step 1: Validate input
        validation = self.validate_input(mask, slice_index)
        conversion_record['validation'] = validation
        
        if not validation['valid']:
            conversion_record['final_result'] = {
                'contours': [],
                'error': 'Input validation failed',
                'errors': validation['errors']
            }
            self.conversion_log.append(conversion_record)
            return conversion_record['final_result']
        
        # Step 2: Preprocess if needed
        processed_mask, preprocessing_log = self.preprocess_mask(mask, validation)
        conversion_record['preprocessing'] = preprocessing_log
        
        # Step 3: Attempt conversion with recovery strategies
        current_kwargs = self.base_kwargs.copy()
        
        for attempt in range(max_retries):
            attempt_record = {
                'attempt_number': attempt + 1,
                'parameters': current_kwargs.copy(),
                'start_time': time.time()
            }
            
            try:
                # Check for empty mask (quick exit)
                if np.sum(processed_mask) == 0:
                    attempt_record.update({
                        'success': True,
                        'contours': [],
                        'processing_time_ms': 0,
                        'recovery_used': 'empty_mask_shortcut'
                    })
                    
                    conversion_record['attempts'].append(attempt_record)
                    conversion_record['success'] = True
                    conversion_record['final_result'] = {
                        'contours': [],
                        'processing_time_ms': 0,
                        'total_points': 0,
                        'recovery_used': 'empty_mask_shortcut'
                    }
                    break
                
                # Create converter and attempt conversion
                converter_params = current_kwargs.copy()
                max_points_per_contour = converter_params.pop('max_points_per_contour')
                converter = MaskToContourConverter(**converter_params)
                mask_3d = processed_mask[np.newaxis, :, :]
                contours_3d = converter.convert_mask_to_contours(mask_3d, max_points_per_contour=max_points_per_contour)
                contours = contours_3d[0]
                
                processing_time = (time.time() - attempt_record['start_time']) * 1000
                total_points = sum(len(contour) for contour in contours)
                
                attempt_record.update({
                    'success': True,
                    'contours': contours,
                    'processing_time_ms': processing_time,
                    'total_points': total_points
                })
                
                conversion_record['attempts'].append(attempt_record)
                conversion_record['success'] = True
                conversion_record['final_result'] = {
                    'contours': contours,
                    'processing_time_ms': processing_time,
                    'total_points': total_points,
                    'attempts_needed': attempt + 1
                }
                break
                
            except MemoryError as e:
                # Memory error recovery: reduce precision
                current_kwargs['accuracy_threshold'] = min(2.0, current_kwargs.get('accuracy_threshold', 0.5) * 2)
                current_kwargs['max_points_per_contour'] = max(50, current_kwargs.get('max_points_per_contour', 200) // 2)
                
                attempt_record.update({
                    'success': False,
                    'error': f'MemoryError: {str(e)}',
                    'recovery_applied': 'reduce_precision',
                    'processing_time_ms': (time.time() - attempt_record['start_time']) * 1000
                })
                
            except Exception as e:
                # General error recovery: simplify parameters
                error_type = type(e).__name__
                
                if 'contour' in str(e).lower() or 'find_contours' in str(e).lower():
                    # Contour-specific error: try more lenient settings
                    current_kwargs['accuracy_threshold'] = min(1.0, current_kwargs.get('accuracy_threshold', 0.5) * 1.5)
                    recovery_type = 'relax_accuracy'
                else:
                    # General error: try conservative settings
                    current_kwargs['accuracy_threshold'] = 1.0
                    current_kwargs['max_points_per_contour'] = 100
                    recovery_type = 'conservative_settings'
                
                attempt_record.update({
                    'success': False,
                    'error': f'{error_type}: {str(e)}',
                    'recovery_applied': recovery_type,
                    'processing_time_ms': (time.time() - attempt_record['start_time']) * 1000
                })
            
            conversion_record['attempts'].append(attempt_record)
        
        # If all attempts failed
        if not conversion_record['success']:
            conversion_record['final_result'] = {
                'contours': [],
                'error': 'All recovery attempts failed',
                'total_attempts': max_retries,
                'last_error': conversion_record['attempts'][-1]['error'] if conversion_record['attempts'] else 'Unknown'
            }
        
        self.conversion_log.append(conversion_record)
        return conversion_record['final_result']
    
    def get_conversion_statistics(self):
        """Get comprehensive statistics from all conversions."""
        
        if not self.conversion_log:
            return {'message': 'No conversions performed yet'}
        
        total_conversions = len(self.conversion_log)
        successful_conversions = sum(1 for record in self.conversion_log if record['success'])
        
        # Recovery statistics
        recovery_used = sum(1 for record in self.conversion_log 
                           if record['success'] and len(record['attempts']) > 1)
        
        # Preprocessing statistics
        preprocessing_used = sum(1 for record in self.conversion_log 
                                if record.get('preprocessing', []))
        
        # Validation warnings
        total_warnings = sum(len(record['validation']['warnings']) 
                           for record in self.conversion_log)
        
        return {
            'total_conversions': total_conversions,
            'successful_conversions': successful_conversions,
            'success_rate': successful_conversions / total_conversions * 100,
            'recovery_used': recovery_used,
            'recovery_rate': recovery_used / total_conversions * 100,
            'preprocessing_used': preprocessing_used,
            'total_validation_warnings': total_warnings,
            'avg_warnings_per_conversion': total_warnings / total_conversions
        }

print("✅ Robust MaskToContourConverter with error recovery implemented")
