# ---
# jupyter:
#   jupytext:
#     text_representation:
#       extension: .py
#       format_name: percent
#       format_version: '1.3'
#       jupytext_version: 1.17.2
#   kernelspec:
#     display_name: pyrt-dicom
#     language: python
#     name: python3
# ---

# %% [markdown]
# # MaskToContourConverter: Parameter Tuning & Optimization
#
# **Learning Objectives:**
# - Master parameter optimization for different clinical scenarios
# - Understand the trade-offs between accuracy, performance, and file size
# - Learn interactive parameter tuning with real-time visualization
# - Implement clinical parameter guidelines for optimal workflow
#
# **Prerequisites:**
# - Basic familiarity with MaskToContourConverter (see `01_basic_mask_to_contour_usage.ipynb`)
# - Understanding of clinical structures (see `02_clinical_scenarios.ipynb`)
#
# **Table of Contents:**
# 1. [Setup & Environment](#setup)
# 2. [Accuracy Threshold Analysis](#accuracy-analysis)
# 3. [Point Optimization Strategies](#point-optimization)
# 4. [Clinical Parameter Guidelines](#clinical-guidelines)
# 5. [Advanced Configuration](#advanced-config)
# 6. [Summary & Best Practices](#summary)

# %% [markdown]
# ## 1. Setup & Environment {#setup}
#
# First, let's set up our environment with all necessary imports and create helper functions for parameter analysis.

# %%
# Core imports
import numpy as np
import matplotlib.pyplot as plt
from matplotlib.patches import Circle, Rectangle
import time
import warnings

warnings.filterwarnings("ignore")

# Interactive widgets
try:
    import ipywidgets as widgets
    from IPython.display import display, clear_output

    WIDGETS_AVAILABLE = True
    print("✅ Interactive widgets available")
except ImportError:
    WIDGETS_AVAILABLE = False
    print(
        "⚠️  Interactive widgets not available - install ipywidgets for full functionality"
    )

# pyrt-dicom imports
try:
    from pyrt_dicom.utils.contour_processing import MaskToContourConverter

    print("✅ MaskToContourConverter imported successfully")
except ImportError as e:
    print(f"❌ Import error: {e}")
    print("Please ensure pyrt-dicom is installed: pip install -e .")

# Set up plotting style
plt.style.use("default")
plt.rcParams["figure.figsize"] = (12, 8)
plt.rcParams["font.size"] = 10


# %%
# Helper functions for parameter analysis


def create_test_mask(shape="circle", size=100, complexity="simple"):
    """Create test masks with varying complexity for parameter testing."""
    mask = np.zeros((200, 200), dtype=bool)
    center = (100, 100)

    if shape == "circle":
        y, x = np.ogrid[:200, :200]
        dist_from_center = np.sqrt((x - center[0]) ** 2 + (y - center[1]) ** 2)

        if complexity == "simple":
            mask = dist_from_center <= size / 2
        elif complexity == "irregular":
            # Add irregular boundary with noise
            noise = np.random.normal(0, 5, (200, 200))
            mask = dist_from_center <= (size / 2 + noise)
        elif complexity == "complex":
            # Multiple circles with holes
            mask1 = dist_from_center <= size / 2
            mask2 = dist_from_center <= size / 4
            mask3 = np.sqrt((x - 80) ** 2 + (y - 80) ** 2) <= 15
            mask = mask1 & ~mask2 | mask3

    elif shape == "rectangle":
        if complexity == "simple":
            mask[
                center[1] - size // 2 : center[1] + size // 2,
                center[0] - size // 2 : center[0] + size // 2,
            ] = True
        elif complexity == "irregular":
            # Rectangle with jagged edges
            for i in range(center[1] - size // 2, center[1] + size // 2):
                jitter = int(np.random.normal(0, 3))
                mask[
                    i, center[0] - size // 2 + jitter : center[0] + size // 2 + jitter
                ] = True

    return mask


def analyze_contour_quality(mask, contours, pixel_spacing=(1.0, 1.0)):
    """Analyze contour quality metrics."""
    metrics = {}

    # Calculate total points
    total_points = sum(len(contour) for contour in contours)
    metrics["total_points"] = total_points

    # Estimate file size impact (rough approximation)
    # Each point ~16 bytes in DICOM (x,y,z as 8-byte doubles)
    estimated_size_bytes = total_points * 16
    metrics["estimated_size_kb"] = estimated_size_bytes / 1024

    # Calculate perimeter accuracy
    if contours:
        perimeter = 0
        for contour in contours:
            if len(contour) > 2:
                # Calculate perimeter of this contour
                # Convert contour to numpy array for proper operations
                contour_array = np.array(contour)
                contour_closed = np.vstack(
                    [contour_array, contour_array[0]]
                )  # Close the contour
                diffs = np.diff(contour_closed, axis=0)
                # Only use x,y components for distance calculation (ignore z for perimeter)
                diffs_xy = diffs[:, :2]  # Take only x,y coordinates
                distances = np.sqrt(
                    np.sum(diffs_xy**2 * np.array(pixel_spacing) ** 2, axis=1)
                )
                perimeter += np.sum(distances)
        metrics["perimeter_mm"] = perimeter
    else:
        metrics["perimeter_mm"] = 0

    # Calculate area preservation
    mask_area_pixels = np.sum(mask)
    mask_area_mm2 = mask_area_pixels * pixel_spacing[0] * pixel_spacing[1]
    metrics["mask_area_mm2"] = mask_area_mm2

    return metrics


def time_conversion(mask, converter_kwargs):
    """Time the conversion process."""
    # Separate converter parameters from method parameters
    method_kwargs = {}
    init_kwargs = converter_kwargs.copy()

    # Extract max_points_per_contour for the method call
    if "max_points_per_contour" in init_kwargs:
        max_points = init_kwargs.pop("max_points_per_contour")
        # Handle None case by using a very large number (effectively unlimited)
        if max_points is not None:
            method_kwargs["max_points_per_contour"] = max_points
        else:
            method_kwargs["max_points_per_contour"] = 10000  # Effectively unlimited

    converter = MaskToContourConverter(**init_kwargs)

    start_time = time.time()
    # Convert 2D mask to 3D mask with single slice for compatibility
    mask_3d = mask[np.newaxis, :, :] if mask.ndim == 2 else mask
    contour_sequences = converter.convert_mask_to_contours(
        mask=mask_3d, slice_positions=[0.0], **method_kwargs  # Single slice at z=0
    )
    # Extract contours from the first (and only) slice
    contours = contour_sequences[0] if contour_sequences else []
    end_time = time.time()

    return contours, (end_time - start_time) * 1000  # Return time in milliseconds


print("✅ Helper functions defined successfully")

# %% [markdown]
# ## 2. Accuracy Threshold Analysis {#accuracy-analysis}
#
# The accuracy threshold is one of the most critical parameters affecting contour quality, file size, and processing time. Let's explore its impact interactively.

# %%
# Create test masks for accuracy analysis
test_masks = {
    "Simple Circle": create_test_mask("circle", 80, "simple"),
    "Irregular Circle": create_test_mask("circle", 80, "irregular"),
    "Complex Shape": create_test_mask("circle", 80, "complex"),
}

# Display test masks
fig, axes = plt.subplots(1, 3, figsize=(15, 5))
for idx, (name, mask) in enumerate(test_masks.items()):
    axes[idx].imshow(mask, cmap="gray", alpha=0.8)
    axes[idx].set_title(f"{name}\n({np.sum(mask)} pixels)")
    axes[idx].set_axis_off()

plt.suptitle("Test Masks for Accuracy Analysis", fontsize=14, fontweight="bold")
plt.tight_layout()
plt.show()

print("Test masks created successfully")


# %%
# Static accuracy threshold analysis (fallback if widgets not available)
def analyze_accuracy_thresholds(accuracy_thresholds, pixel_spacing):
    """Analyze the impact of different accuracy thresholds."""

    results = {}

    for mask_name, mask in test_masks.items():
        results[mask_name] = []

        for accuracy in accuracy_thresholds:
            converter_kwargs = {
                "pixel_spacing": pixel_spacing,
                "accuracy_threshold": accuracy,
            }

            contours, processing_time = time_conversion(mask, converter_kwargs)
            metrics = analyze_contour_quality(mask, contours, pixel_spacing)

            result = {
                "accuracy_threshold": accuracy,
                "contours": contours,
                "processing_time_ms": processing_time,
                **metrics,
            }
            results[mask_name].append(result)

    return results


# Run the analysis
print("Analyzing accuracy thresholds...")

# Test different accuracy thresholds
accuracy_thresholds = [0.1, 0.25, 0.5, 1.0, 2.0]
pixel_spacing = (1.25, 1.25)  # Typical CT spacing
accuracy_results = analyze_accuracy_thresholds(accuracy_thresholds, pixel_spacing)
print("✅ Analysis complete")

# %%
# Visualize accuracy threshold analysis results
fig, axes = plt.subplots(2, 3, figsize=(18, 12))
axes = axes.flatten()

mask_names = list(accuracy_results.keys())
colors = ["#1f77b4", "#ff7f0e", "#2ca02c"]

# Extract data for plotting
for i, mask_name in enumerate(mask_names):
    results = accuracy_results[mask_name]

    thresholds = [r["accuracy_threshold"] for r in results]
    points = [r["total_points"] for r in results]
    times = [r["processing_time_ms"] for r in results]
    sizes = [r["estimated_size_kb"] for r in results]

    # Plot 1: Points vs Accuracy
    axes[0].plot(
        thresholds,
        points,
        "o-",
        color=colors[i],
        label=mask_name,
        linewidth=2,
        markersize=6,
    )

    # Plot 2: Processing Time vs Accuracy
    axes[1].plot(
        thresholds,
        times,
        "s-",
        color=colors[i],
        label=mask_name,
        linewidth=2,
        markersize=6,
    )

    # Plot 3: File Size vs Accuracy
    axes[2].plot(
        thresholds,
        sizes,
        "^-",
        color=colors[i],
        label=mask_name,
        linewidth=2,
        markersize=6,
    )

# Configure subplots
titles = [
    "Total Points vs Accuracy Threshold",
    "Processing Time vs Accuracy Threshold",
    "Estimated File Size vs Accuracy Threshold",
]
ylabels = ["Total Points", "Processing Time (ms)", "Estimated Size (KB)"]

for i in range(3):
    axes[i].set_title(titles[i], fontweight="bold")
    axes[i].set_xlabel("Accuracy Threshold (mm)")
    axes[i].set_ylabel(ylabels[i])
    axes[i].legend()
    axes[i].grid(True, alpha=0.3)
    axes[i].set_xscale("log")

# Visual comparison at different thresholds
comparison_thresholds = [0.1, 0.5, 2.0]
mask_to_show = "Complex Shape"
mask_data = test_masks[mask_to_show]

for i, threshold in enumerate(comparison_thresholds):
    ax = axes[3 + i]

    # Find the result for this threshold
    result = next(
        r
        for r in accuracy_results[mask_to_show]
        if r["accuracy_threshold"] == threshold
    )
    contours = result["contours"]

    # Plot mask and contours
    ax.imshow(
        mask_data,
        cmap="gray",
        alpha=0.6,
        extent=[0, 200 * pixel_spacing[0], 200 * pixel_spacing[1], 0],
    )

    for contour in contours:
        if len(contour) > 2:
            # Convert contour to numpy array for easier indexing
            contour_array = np.array(contour)
            contour_closed = np.vstack([contour_array, contour_array[0]])
            ax.plot(contour_closed[:, 0], contour_closed[:, 1], "r-", linewidth=2)
            ax.scatter(
                contour_array[:, 0], contour_array[:, 1], c="red", s=20, alpha=0.7
            )

    ax.set_title(
        f'Threshold: {threshold} mm\n{result["total_points"]} points, {result["processing_time_ms"]:.1f} ms'
    )
    ax.set_aspect("equal")
    ax.set_xlim(50 * pixel_spacing[0], 150 * pixel_spacing[0])
    ax.set_ylim(150 * pixel_spacing[1], 50 * pixel_spacing[1])

plt.tight_layout()
plt.show()

# Summary table
print("\n" + "=" * 80)
print("ACCURACY THRESHOLD ANALYSIS SUMMARY")
print("=" * 80)

for mask_name in mask_names:
    print(f"\n{mask_name.upper()}:")
    print(
        f"{'Threshold':<12} {'Points':<8} {'Time(ms)':<10} {'Size(KB)':<10} {'Efficiency':<12}"
    )
    print("-" * 55)

    results = accuracy_results[mask_name]
    for result in results:
        efficiency = (
            result["total_points"] / result["processing_time_ms"]
            if result["processing_time_ms"] > 0
            else 0
        )
        print(
            f"{result['accuracy_threshold']:<12.1f} {result['total_points']:<8} {result['processing_time_ms']:<10.1f} {result['estimated_size_kb']:<10.1f} {efficiency:<12.1f}"
        )

# %% [markdown]
# ### Interactive Accuracy Threshold Widget
#
# If you have ipywidgets installed, you can interactively explore the accuracy threshold parameter:

# %%
if WIDGETS_AVAILABLE:

    def interactive_accuracy_demo():
        """Interactive widget for accuracy threshold exploration."""

        # Widget controls
        accuracy_slider = widgets.FloatLogSlider(
            value=0.5,
            base=10,
            min=-1,  # 0.1
            max=0.5,  # ~3.16
            step=0.1,
            description="Accuracy:",
            style={"description_width": "100px"},
        )

        mask_dropdown = widgets.Dropdown(
            options=list(test_masks.keys()),
            value="Complex Shape",
            description="Test Mask:",
            style={"description_width": "100px"},
        )

        output = widgets.Output()

        def update_plot(accuracy, mask_name):
            with output:
                clear_output(wait=True)

                # Get mask and convert
                mask = test_masks[mask_name]
                pixel_spacing = (1.25, 1.25)

                converter_kwargs = {
                    "pixel_spacing": pixel_spacing,
                    "accuracy_threshold": accuracy,
                }

                contours, processing_time = time_conversion(mask, converter_kwargs)
                metrics = analyze_contour_quality(mask, contours, pixel_spacing)

                # Create visualization
                fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 6))

                # Left plot: Mask with contours
                ax1.imshow(
                    mask,
                    cmap="gray",
                    alpha=0.6,
                    extent=[0, 200 * pixel_spacing[0], 200 * pixel_spacing[1], 0],
                )

                for contour in contours:
                    if len(contour) > 2:
                        # Convert contour to numpy array for indexing
                        contour_array = np.array(contour)
                        contour_closed = np.vstack([contour_array, contour_array[0]])
                        ax1.plot(
                            contour_closed[:, 0],
                            contour_closed[:, 1],
                            "r-",
                            linewidth=2,
                        )
                        ax1.scatter(
                            contour_array[:, 0],
                            contour_array[:, 1],
                            c="red",
                            s=15,
                            alpha=0.7,
                        )

                ax1.set_title(f"{mask_name} - Accuracy: {accuracy:.2f} mm")
                ax1.set_aspect("equal")
                ax1.set_xlim(20 * pixel_spacing[0], 180 * pixel_spacing[0])
                ax1.set_ylim(180 * pixel_spacing[1], 20 * pixel_spacing[1])

                # Right plot: Metrics
                ax2.axis("off")
                metrics_text = f"""
CONVERSION METRICS
{'='*30}
Accuracy Threshold: {accuracy:.2f} mm
Total Points: {metrics['total_points']}
Processing Time: {processing_time:.1f} ms
Estimated Size: {metrics['estimated_size_kb']:.1f} KB
Perimeter: {metrics['perimeter_mm']:.1f} mm
Mask Area: {metrics['mask_area_mm2']:.1f} mm²

PERFORMANCE RATING
{'='*30}
Points/mm perimeter: {metrics['total_points']/metrics['perimeter_mm']:.2f}
Processing Speed: {1000/processing_time:.0f} conversions/sec
Storage Efficiency: {metrics['mask_area_mm2']/metrics['estimated_size_kb']:.1f} mm²/KB
"""
                ax2.text(
                    0.05,
                    0.95,
                    metrics_text,
                    transform=ax2.transAxes,
                    verticalalignment="top",
                    fontfamily="monospace",
                    fontsize=11,
                )

                plt.tight_layout()
                plt.show()

        # Set up interactive widget
        interactive_widget = widgets.interactive(
            update_plot, accuracy=accuracy_slider, mask_name=mask_dropdown
        )

        display(interactive_widget, output)

    print("🎛️  Interactive accuracy threshold demo:")
    interactive_accuracy_demo()

else:
    print(
        "ℹ️  Install ipywidgets to enable interactive parameter tuning: pip install ipywidgets"
    )
    print(
        "   Jupyter Lab users may also need: jupyter labextension install @jupyter-widgets/jupyterlab-manager"
    )


# %% [markdown]
# ## 3. Point Optimization Strategies {#point-optimization}
#
# Managing the number of points in contours is crucial for balancing accuracy with file size and processing performance.


# %%
# Test different max_points_per_contour settings
def analyze_point_optimization():
    """Analyze the impact of point count limitations."""

    max_points_settings = [50, 100, 200, 500, 1000, None]  # None = unlimited
    test_mask = test_masks["Complex Shape"]  # Use complex shape for this analysis
    pixel_spacing = (1.25, 1.25)

    results = []

    for max_points in max_points_settings:
        converter_kwargs = {
            "pixel_spacing": pixel_spacing,
            "accuracy_threshold": 0.5,  # Fixed accuracy for this test
            "max_points_per_contour": max_points,
        }

        contours, processing_time = time_conversion(test_mask, converter_kwargs)
        metrics = analyze_contour_quality(test_mask, contours, pixel_spacing)

        result = {
            "max_points": max_points if max_points else "Unlimited",
            "max_points_numeric": max_points if max_points else 2000,  # For plotting
            "contours": contours,
            "processing_time_ms": processing_time,
            **metrics,
        }
        results.append(result)

    return results


print("Analyzing point optimization strategies...")
point_results = analyze_point_optimization()
print("✅ Point optimization analysis complete")

# %%
# Visualize point optimization results
fig, axes = plt.subplots(2, 3, figsize=(18, 12))
axes = axes.flatten()

# Extract data
max_points_values = [r["max_points_numeric"] for r in point_results]
total_points = [r["total_points"] for r in point_results]
processing_times = [r["processing_time_ms"] for r in point_results]
file_sizes = [r["estimated_size_kb"] for r in point_results]
perimeters = [r["perimeter_mm"] for r in point_results]

# Plot 1: Total Points vs Max Points Setting
axes[0].plot(
    max_points_values, total_points, "o-", linewidth=2, markersize=8, color="#2E86AB"
)
axes[0].set_title("Total Points vs Max Points Limit", fontweight="bold")
axes[0].set_xlabel("Max Points per Contour")
axes[0].set_ylabel("Actual Total Points")
axes[0].grid(True, alpha=0.3)
axes[0].set_xscale("log")

# Plot 2: Processing Time vs Max Points
axes[1].plot(
    max_points_values,
    processing_times,
    "s-",
    linewidth=2,
    markersize=8,
    color="#A23B72",
)
axes[1].set_title("Processing Time vs Max Points Limit", fontweight="bold")
axes[1].set_xlabel("Max Points per Contour")
axes[1].set_ylabel("Processing Time (ms)")
axes[1].grid(True, alpha=0.3)
axes[1].set_xscale("log")

# Plot 3: File Size vs Max Points
axes[2].plot(
    max_points_values, file_sizes, "^-", linewidth=2, markersize=8, color="#F18F01"
)
axes[2].set_title("Estimated File Size vs Max Points Limit", fontweight="bold")
axes[2].set_xlabel("Max Points per Contour")
axes[2].set_ylabel("Estimated Size (KB)")
axes[2].grid(True, alpha=0.3)
axes[2].set_xscale("log")

# Visual comparison at different point limits
comparison_indices = [0, 2, 5]  # 50, 200, unlimited
test_mask = test_masks["Complex Shape"]

for i, idx in enumerate(comparison_indices):
    ax = axes[3 + i]
    result = point_results[idx]
    contours = result["contours"]

    # Plot mask and contours
    ax.imshow(test_mask, cmap="gray", alpha=0.6, extent=[0, 200, 200, 0])

    colors = plt.cm.viridis(np.linspace(0, 1, len(contours)))
    for j, contour in enumerate(contours):
        if len(contour) > 2:
            # Convert contour to numpy array for indexing
            contour_array = np.array(contour)
            contour_closed = np.vstack([contour_array, contour_array[0]])
            ax.plot(
                contour_closed[:, 0],
                contour_closed[:, 1],
                color=colors[j],
                linewidth=2.5,
                alpha=0.8,
            )
            ax.scatter(
                contour_array[:, 0],
                contour_array[:, 1],
                c=[colors[j]],
                s=25,
                alpha=0.9,
                edgecolors="white",
                linewidths=0.5,
            )

    ax.set_title(
        f'Max Points: {result["max_points"]}\n{result["total_points"]} actual points'
    )
    ax.set_aspect("equal")
    ax.set_xlim(50, 150)
    ax.set_ylim(150, 50)

plt.tight_layout()
plt.show()

# Efficiency analysis
print("\n" + "=" * 80)
print("POINT OPTIMIZATION ANALYSIS SUMMARY")
print("=" * 80)

print(
    f"{'Max Points':<12} {'Actual':<8} {'Time(ms)':<10} {'Size(KB)':<10} {'Points/mm':<12} {'Efficiency':<12}"
)
print("-" * 75)

for result in point_results:
    points_per_mm = (
        result["total_points"] / result["perimeter_mm"]
        if result["perimeter_mm"] > 0
        else 0
    )
    efficiency = (
        result["total_points"] / result["processing_time_ms"]
        if result["processing_time_ms"] > 0
        else 0
    )

    print(
        f"{str(result['max_points']):<12} {result['total_points']:<8} {result['processing_time_ms']:<10.1f} "
        f"{result['estimated_size_kb']:<10.1f} {points_per_mm:<12.2f} {efficiency:<12.1f}"
    )

# Recommendations
print("\n" + "=" * 80)
print("POINT OPTIMIZATION RECOMMENDATIONS")
print("=" * 80)

print(
    """
📋 CLINICAL GUIDELINES:

🔬 STEREOTACTIC/HIGH-PRECISION STRUCTURES:
   • Max Points: 500-1000 per contour
   • Use Case: Critical organs, small lesions, SRS targets
   • Trade-off: Higher accuracy at cost of file size

🏥 CONVENTIONAL RT STRUCTURES:
   • Max Points: 200-500 per contour  
   • Use Case: Standard PTV, most OARs
   • Trade-off: Balanced accuracy and performance

⚡ LARGE ORGAN STRUCTURES:
   • Max Points: 100-200 per contour
   • Use Case: Body outline, lungs, liver
   • Trade-off: Optimized for speed and file size

📊 SCREENING/BATCH PROCESSING:
   • Max Points: 50-100 per contour
   • Use Case: Research, automated screening
   • Trade-off: Maximum speed, minimal accuracy loss
"""
)

# %% [markdown]
# ## 4. Clinical Parameter Guidelines {#clinical-guidelines}
#
# Based on our analysis, let's establish evidence-based parameter guidelines for different clinical scenarios.

# %%
# Define clinical parameter profiles
CLINICAL_PROFILES = {
    "stereotactic_critical": {
        "name": "Stereotactic Critical Organs",
        "description": "Ultra-high precision for critical structures in SRS/SBRT",
        "accuracy_threshold": 0.1,  # 0.1 mm
        "max_points_per_contour": 1000,
        "simplification_tolerance": 0.05,
        "use_cases": ["Brainstem", "Spinal Cord", "Optic Chiasm", "Critical nerves"],
        "typical_structures": "Small, critical OARs",
        "priority": "Accuracy > Speed > File Size",
    },
    "stereotactic_target": {
        "name": "Stereotactic Targets",
        "description": "High precision for SRS/SBRT treatment targets",
        "accuracy_threshold": 0.25,  # 0.25 mm
        "max_points_per_contour": 500,
        "simplification_tolerance": 0.1,
        "use_cases": ["GTV", "PTV for SRS", "Small metastases"],
        "typical_structures": "Treatment targets <5cm",
        "priority": "Accuracy > File Size > Speed",
    },
    "conventional_critical": {
        "name": "Conventional Critical",
        "description": "Standard precision for important normal tissues",
        "accuracy_threshold": 0.5,  # 0.5 mm
        "max_points_per_contour": 300,
        "simplification_tolerance": 0.2,
        "use_cases": ["Heart", "Lungs", "Kidneys", "Eyes"],
        "typical_structures": "Important OARs",
        "priority": "Balanced accuracy and performance",
    },
    "conventional_target": {
        "name": "Conventional Targets",
        "description": "Standard precision for conventional RT targets",
        "accuracy_threshold": 0.5,  # 0.5 mm
        "max_points_per_contour": 200,
        "simplification_tolerance": 0.25,
        "use_cases": ["CTV", "PTV for conventional RT", "Boost volumes"],
        "typical_structures": "Treatment volumes >5cm",
        "priority": "Balanced accuracy and speed",
    },
    "large_organ": {
        "name": "Large Organ Structures",
        "description": "Performance-optimized for large anatomical structures",
        "accuracy_threshold": 1.0,  # 1.0 mm
        "max_points_per_contour": 150,
        "simplification_tolerance": 0.5,
        "use_cases": ["Body outline", "Liver", "Large bones", "External contour"],
        "typical_structures": "Large anatomical regions",
        "priority": "Speed > File Size > Accuracy",
    },
    "research_batch": {
        "name": "Research/Batch Processing",
        "description": "Maximum speed for large-scale analysis",
        "accuracy_threshold": 2.0,  # 2.0 mm
        "max_points_per_contour": 100,
        "simplification_tolerance": 1.0,
        "use_cases": ["Population studies", "Automated screening", "Atlas creation"],
        "typical_structures": "Any structures for research",
        "priority": "Speed >> File Size >> Accuracy",
    },
}


def create_converter_from_profile(
    profile_name, pixel_spacing=(1.25, 1.25), slice_thickness=2.5
):
    """Create a MaskToContourConverter with clinical profile settings."""

    if profile_name not in CLINICAL_PROFILES:
        raise ValueError(
            f"Unknown profile: {profile_name}. Available: {list(CLINICAL_PROFILES.keys())}"
        )

    profile = CLINICAL_PROFILES[profile_name]

    converter_kwargs = {
        "pixel_spacing": pixel_spacing,
        "slice_thickness": slice_thickness,
        "accuracy_threshold": profile["accuracy_threshold"],
        "max_points_per_contour": profile["max_points_per_contour"],
    }

    return MaskToContourConverter(**converter_kwargs), profile


print("✅ Clinical parameter profiles defined")
print(f"Available profiles: {list(CLINICAL_PROFILES.keys())}")


# %%
# Test all clinical profiles
def test_clinical_profiles():
    """Test all clinical profiles on various mask types."""

    profile_results = {}
    pixel_spacing = (1.25, 1.25)

    for profile_name in CLINICAL_PROFILES.keys():
        profile_results[profile_name] = {}

        for mask_name, mask in test_masks.items():
            # Create converter with profile settings
            profile = CLINICAL_PROFILES[profile_name]
            converter_kwargs = {
                "pixel_spacing": pixel_spacing,
                "accuracy_threshold": profile["accuracy_threshold"],
                "max_points_per_contour": profile["max_points_per_contour"],
            }

            contours, processing_time = time_conversion(mask, converter_kwargs)
            metrics = analyze_contour_quality(mask, contours, pixel_spacing)

            profile_results[profile_name][mask_name] = {
                "contours": contours,
                "processing_time_ms": processing_time,
                **metrics,
            }

    return profile_results


print("Testing all clinical profiles...")
profile_test_results = test_clinical_profiles()
print("✅ Clinical profile testing complete")

# %%
# Visualize clinical profile comparison
fig, axes = plt.subplots(2, 3, figsize=(20, 14))
axes = axes.flatten()

profile_names = list(CLINICAL_PROFILES.keys())
colors = plt.cm.Set3(np.linspace(0, 1, len(profile_names)))

# Aggregate metrics across all mask types
for i, mask_name in enumerate(test_masks.keys()):
    points_data = []
    times_data = []
    sizes_data = []
    profile_labels = []

    for j, profile_name in enumerate(profile_names):
        result = profile_test_results[profile_name][mask_name]
        points_data.append(result["total_points"])
        times_data.append(result["processing_time_ms"])
        sizes_data.append(result["estimated_size_kb"])
        profile_labels.append(profile_name.replace("_", "\n"))

    # Bar plots for each mask type
    x_pos = np.arange(len(profile_names))

    if i < 3:  # Points comparison
        bars = axes[i].bar(
            x_pos,
            points_data,
            color=colors,
            alpha=0.8,
            edgecolor="black",
            linewidth=0.5,
        )
        axes[i].set_title(f"{mask_name}\nTotal Points by Profile", fontweight="bold")
        axes[i].set_ylabel("Total Points")
        axes[i].set_yscale("log")

        # Add value labels on bars
        for bar, value in zip(bars, points_data):
            axes[i].text(
                bar.get_x() + bar.get_width() / 2,
                bar.get_height() * 1.1,
                f"{value}",
                ha="center",
                va="bottom",
                fontsize=8,
                fontweight="bold",
            )

    else:  # Time comparison
        bars = axes[i].bar(
            x_pos, times_data, color=colors, alpha=0.8, edgecolor="black", linewidth=0.5
        )
        axes[i].set_title(f"{mask_name}\nProcessing Time by Profile", fontweight="bold")
        axes[i].set_ylabel("Processing Time (ms)")

        # Add value labels on bars
        for bar, value in zip(bars, times_data):
            axes[i].text(
                bar.get_x() + bar.get_width() / 2,
                bar.get_height() * 1.05,
                f"{value:.1f}",
                ha="center",
                va="bottom",
                fontsize=8,
                fontweight="bold",
            )

    axes[i].set_xticks(x_pos)
    axes[i].set_xticklabels(profile_labels, rotation=45, ha="right", fontsize=9)
    axes[i].grid(True, alpha=0.3, axis="y")

plt.tight_layout()
plt.show()

# Clinical Profile Summary Table
print("\n" + "=" * 120)
print("CLINICAL PARAMETER PROFILES SUMMARY")
print("=" * 120)

for profile_name, profile in CLINICAL_PROFILES.items():
    print(f"\n🏥 {profile['name'].upper()}")
    print(f"   Description: {profile['description']}")
    print(
        f"   Accuracy: {profile['accuracy_threshold']} mm | Max Points: {profile['max_points_per_contour']} | Priority: {profile['priority']}"
    )
    print(f"   Use Cases: {', '.join(profile['use_cases'])}")

    # Show performance across test masks
    print(f"   Performance:")
    for mask_name in test_masks.keys():
        result = profile_test_results[profile_name][mask_name]
        print(
            f"     {mask_name}: {result['total_points']} points, {result['processing_time_ms']:.1f} ms, {result['estimated_size_kb']:.1f} KB"
        )
    print("-" * 100)


# %% [markdown]
# ## 5. Advanced Configuration {#advanced-config}
#
# Explore advanced configuration options including custom coordinate transformations and preprocessing strategies.


# %%
# Advanced configuration examples
def demonstrate_advanced_config():
    """Demonstrate advanced configuration options."""

    # Create a more complex test mask for advanced testing
    complex_mask = np.zeros((300, 300), dtype=bool)

    # Create multiple components with different characteristics
    # Main structure
    y, x = np.ogrid[:300, :300]
    main_structure = ((x - 150) ** 2 + (y - 150) ** 2) <= 80**2

    # Internal hole
    hole = ((x - 150) ** 2 + (y - 150) ** 2) <= 30**2

    # Separate small structure
    small_structure = ((x - 100) ** 2 + (y - 100) ** 2) <= 15**2

    # Thin connecting bridge
    bridge = (np.abs(x - y) <= 5) & (x >= 100) & (x <= 150) & (y >= 100) & (y <= 150)

    complex_mask = (main_structure & ~hole) | small_structure | bridge

    return complex_mask


# Create advanced test mask
advanced_mask = demonstrate_advanced_config()

# Display the complex mask
plt.figure(figsize=(10, 8))
plt.imshow(advanced_mask, cmap="gray", alpha=0.8)
plt.title(
    "Advanced Test Mask\n(Multi-component structure with hole and bridge)",
    fontweight="bold",
    fontsize=14,
)
plt.axis("off")
plt.tight_layout()
plt.show()

print(f"Advanced test mask created: {np.sum(advanced_mask)} pixels")


# %%
# Test different coordinate configurations
def analyze_coordinate_configurations():
    """Analyze impact of different coordinate system configurations."""

    coordinate_configs = [
        {
            "name": "High-res CT",
            "pixel_spacing": (0.625, 0.625),  # 0.625 mm pixels
            "slice_thickness": 1.25,
            "description": "High-resolution diagnostic CT",
        },
        {
            "name": "Standard CT",
            "pixel_spacing": (1.25, 1.25),  # 1.25 mm pixels
            "slice_thickness": 2.5,
            "description": "Standard treatment planning CT",
        },
        {
            "name": "Low-res CT",
            "pixel_spacing": (2.0, 2.0),  # 2.0 mm pixels
            "slice_thickness": 5.0,
            "description": "Low-resolution or older CT protocol",
        },
        {
            "name": "MR T1",
            "pixel_spacing": (0.9, 0.9),  # 0.9 mm pixels
            "slice_thickness": 3.0,
            "description": "Typical MR T1-weighted sequence",
        },
    ]

    results = []

    for config in coordinate_configs:
        # Use conventional_critical profile as baseline
        profile = CLINICAL_PROFILES["conventional_critical"]

        converter_kwargs = {
            "pixel_spacing": config["pixel_spacing"],
            "slice_thickness": config["slice_thickness"],
            "accuracy_threshold": profile["accuracy_threshold"],
            "max_points_per_contour": profile["max_points_per_contour"],
        }

        contours, processing_time = time_conversion(advanced_mask, converter_kwargs)
        metrics = analyze_contour_quality(
            advanced_mask, contours, config["pixel_spacing"]
        )

        result = {
            **config,
            "contours": contours,
            "processing_time_ms": processing_time,
            **metrics,
        }
        results.append(result)

    return results


print("Analyzing coordinate system configurations...")
coord_results = analyze_coordinate_configurations()
print("✅ Coordinate configuration analysis complete")

# %%
# Visualize coordinate configuration results
fig, axes = plt.subplots(2, 2, figsize=(16, 12))

# Extract data for plotting
config_names = [r["name"] for r in coord_results]
pixel_sizes = [r["pixel_spacing"][0] for r in coord_results]  # Assuming square pixels
total_points = [r["total_points"] for r in coord_results]
perimeters = [r["perimeter_mm"] for r in coord_results]
processing_times = [r["processing_time_ms"] for r in coord_results]
file_sizes = [r["estimated_size_kb"] for r in coord_results]

colors = ["#FF6B6B", "#4ECDC4", "#45B7D1", "#96CEB4"]

# Plot 1: Points vs Pixel Size
axes[0, 0].scatter(
    pixel_sizes,
    total_points,
    c=colors,
    s=150,
    alpha=0.8,
    edgecolors="black",
    linewidths=1,
)
for i, name in enumerate(config_names):
    axes[0, 0].annotate(
        name,
        (pixel_sizes[i], total_points[i]),
        xytext=(5, 5),
        textcoords="offset points",
        fontsize=10,
        fontweight="bold",
    )
axes[0, 0].set_xlabel("Pixel Size (mm)")
axes[0, 0].set_ylabel("Total Points")
axes[0, 0].set_title("Total Points vs Pixel Size", fontweight="bold")
axes[0, 0].grid(True, alpha=0.3)

# Plot 2: Perimeter vs Pixel Size
axes[0, 1].scatter(
    pixel_sizes,
    perimeters,
    c=colors,
    s=150,
    alpha=0.8,
    edgecolors="black",
    linewidths=1,
)
for i, name in enumerate(config_names):
    axes[0, 1].annotate(
        name,
        (pixel_sizes[i], perimeters[i]),
        xytext=(5, 5),
        textcoords="offset points",
        fontsize=10,
        fontweight="bold",
    )
axes[0, 1].set_xlabel("Pixel Size (mm)")
axes[0, 1].set_ylabel("Perimeter (mm)")
axes[0, 1].set_title("Measured Perimeter vs Pixel Size", fontweight="bold")
axes[0, 1].grid(True, alpha=0.3)

# Plot 3: Processing Time vs Pixel Size
axes[1, 0].scatter(
    pixel_sizes,
    processing_times,
    c=colors,
    s=150,
    alpha=0.8,
    edgecolors="black",
    linewidths=1,
)
for i, name in enumerate(config_names):
    axes[1, 0].annotate(
        name,
        (pixel_sizes[i], processing_times[i]),
        xytext=(5, 5),
        textcoords="offset points",
        fontsize=10,
        fontweight="bold",
    )
axes[1, 0].set_xlabel("Pixel Size (mm)")
axes[1, 0].set_ylabel("Processing Time (ms)")
axes[1, 0].set_title("Processing Time vs Pixel Size", fontweight="bold")
axes[1, 0].grid(True, alpha=0.3)

# Plot 4: Visual comparison of contours
axes[1, 1].imshow(advanced_mask, cmap="gray", alpha=0.6, extent=[0, 300, 300, 0])

# Show contours from two different resolutions for comparison
high_res_contours = coord_results[0]["contours"]  # High-res CT
low_res_contours = coord_results[2]["contours"]  # Low-res CT

# Plot high-res contours in blue
for contour in high_res_contours:
    if len(contour) > 2:
        # Convert contour to numpy array for proper operations
        contour_array = np.array(contour)
        contour_closed = np.vstack([contour_array, contour_array[0]])
        axes[1, 1].plot(
            contour_closed[:, 0],
            contour_closed[:, 1],
            "b-",
            linewidth=2,
            alpha=0.8,
            label="High-res (0.625mm)",
        )

# Plot low-res contours in red
for contour in low_res_contours:
    if len(contour) > 2:
        # Convert contour to numpy array for proper operations
        contour_array = np.array(contour)
        contour_closed = np.vstack([contour_array, contour_array[0]])
        axes[1, 1].plot(
            contour_closed[:, 0],
            contour_closed[:, 1],
            "r--",
            linewidth=2,
            alpha=0.8,
            label="Low-res (2.0mm)",
        )

axes[1, 1].set_title("Contour Comparison: High-res vs Low-res", fontweight="bold")
axes[1, 1].set_xlim(50, 250)
axes[1, 1].set_ylim(250, 50)
axes[1, 1].legend()

plt.tight_layout()
plt.show()

# Detailed results table
print("\n" + "=" * 100)
print("COORDINATE CONFIGURATION ANALYSIS")
print("=" * 100)

print(
    f"{'Configuration':<15} {'Pixel(mm)':<12} {'Slice(mm)':<12} {'Points':<8} {'Perimeter':<12} {'Time(ms)':<10} {'Size(KB)':<10}"
)
print("-" * 95)

for result in coord_results:
    print(
        f"{result['name']:<15} {result['pixel_spacing'][0]:<12.3f} {result['slice_thickness']:<12.1f} "
        f"{result['total_points']:<8} {result['perimeter_mm']:<12.1f} {result['processing_time_ms']:<10.1f} "
        f"{result['estimated_size_kb']:<10.1f}"
    )

print("\n📋 KEY OBSERVATIONS:")
print("• Higher resolution → More points, better geometric accuracy")
print("• Lower resolution → Faster processing, smaller files, some accuracy loss")
print("• Perimeter measurements affected by pixel size resolution")
print("• Processing time scales roughly with number of detected edge pixels")


# %% [markdown]
# ### Error Handling and Recovery Strategies
#
# Let's demonstrate robust error handling for various edge cases:


# %%
# Demonstrate error handling and recovery
def demonstrate_error_handling():
    """Test error handling with various problematic inputs."""

    test_cases = {
        "empty_mask": np.zeros((100, 100), dtype=bool),
        "single_pixel": np.zeros((100, 100), dtype=bool),
        "thin_line": np.zeros((100, 100), dtype=bool),
        "all_true": np.ones((100, 100), dtype=bool),
        "tiny_mask": np.zeros((5, 5), dtype=bool),
    }

    # Set up specific problematic cases
    test_cases["single_pixel"][50, 50] = True
    test_cases["thin_line"][50, 20:80] = True  # 1-pixel wide line
    test_cases["tiny_mask"][2, 2] = True

    results = {}

    for case_name, mask in test_cases.items():
        print(f"\nTesting: {case_name} ({np.sum(mask)} pixels)")

        try:
            # Use conventional_target profile
            profile = CLINICAL_PROFILES["conventional_target"]
            converter_kwargs = {
                "pixel_spacing": (1.25, 1.25),
                "accuracy_threshold": profile["accuracy_threshold"],
                "max_points_per_contour": profile["max_points_per_contour"],
            }

            contours, processing_time = time_conversion(mask, converter_kwargs)
            metrics = analyze_contour_quality(mask, contours, (1.25, 1.25))

            results[case_name] = {
                "success": True,
                "contours": contours,
                "processing_time_ms": processing_time,
                "error": None,
                **metrics,
            }

            print(
                f"  ✅ Success: {len(contours)} contours, {metrics['total_points']} points"
            )

        except Exception as e:
            results[case_name] = {
                "success": False,
                "contours": [],
                "processing_time_ms": 0,
                "error": str(e),
                "total_points": 0,
            }

            print(f"  ❌ Error: {e}")

    return test_cases, results


# Run error handling tests
print("Testing error handling and edge cases...")
edge_cases, edge_results = demonstrate_error_handling()
print("\n✅ Error handling tests complete")

# %%
# Visualize edge cases and their handling
fig, axes = plt.subplots(2, len(edge_cases), figsize=(20, 8))
if len(edge_cases) == 1:
    axes = axes.reshape(-1, 1)

for i, (case_name, mask) in enumerate(edge_cases.items()):
    result = edge_results[case_name]

    # Top row: Input masks
    axes[0, i].imshow(mask, cmap="gray", alpha=0.8)
    axes[0, i].set_title(
        f'{case_name.replace("_", " ").title()}\n{np.sum(mask)} pixels'
    )
    axes[0, i].set_axis_off()

    # Bottom row: Results with contours
    axes[1, i].imshow(mask, cmap="gray", alpha=0.6)

    if result["success"] and result["contours"]:
        colors = plt.cm.viridis(np.linspace(0, 1, len(result["contours"])))
        for j, contour in enumerate(result["contours"]):
            if len(contour) > 2:
                # Convert contour to numpy array for indexing
                contour_array = np.array(contour)
                contour_closed = np.vstack([contour_array, contour_array[0]])
                axes[1, i].plot(
                    contour_closed[:, 0],
                    contour_closed[:, 1],
                    color=colors[j],
                    linewidth=2,
                    alpha=0.9,
                )
                axes[1, i].scatter(
                    contour_array[:, 0],
                    contour_array[:, 1],
                    c=[colors[j]],
                    s=30,
                    alpha=0.8,
                    edgecolors="white",
                )

        status = (
            f"✅ {len(result['contours'])} contours\n{result['total_points']} points"
        )
        color = "green"
    else:
        if result["success"]:
            status = "⚠️ No contours\nEmpty result"
            color = "orange"
        else:
            status = f"❌ Error\n{result['error'][:20]}..."
            color = "red"

    axes[1, i].set_title(status, color=color, fontweight="bold")
    axes[1, i].set_axis_off()

plt.suptitle(
    "Edge Case Handling: Input Masks and Conversion Results",
    fontsize=16,
    fontweight="bold",
)
plt.tight_layout()
plt.show()

# Error handling summary
print("\n" + "=" * 80)
print("EDGE CASE HANDLING SUMMARY")
print("=" * 80)

success_count = sum(1 for r in edge_results.values() if r["success"])
total_count = len(edge_results)

print(
    f"\n📊 OVERALL RESULTS: {success_count}/{total_count} cases handled successfully ({100*success_count/total_count:.1f}%)\n"
)

for case_name, result in edge_results.items():
    print(f"🔸 {case_name.replace('_', ' ').title()}:")
    if result["success"]:
        if result["total_points"] > 0:
            print(
                f"   ✅ Successfully processed → {len(result['contours'])} contours, {result['total_points']} points"
            )
        else:
            print(
                f"   ⚠️  Processed but no contours generated (expected for empty/invalid masks)"
            )
    else:
        print(f"   ❌ Error: {result['error']}")
    print()

print("\n📋 ROBUST HANDLING GUIDELINES:")
print(
    """
🛡️ PREPROCESSING STRATEGIES:
   • Validate mask dimensions and content before conversion
   • Remove single-pixel artifacts with morphological operations
   • Check for minimum structure size requirements
   • Apply median filtering for noisy masks

⚡ PERFORMANCE OPTIMIZATION:
   • Skip conversion for empty masks (0 pixels)
   • Use faster settings for very large masks (>100K pixels)
   • Implement timeout for extremely complex structures
   • Cache results for repeated conversions

🔧 ERROR RECOVERY:
   • Graceful fallback to simpler algorithms for problematic cases
   • Provide meaningful error messages with suggested solutions
   • Log processing statistics for performance monitoring
   • Implement retry logic with relaxed parameters
"""
)


# %% [markdown]
# ## 6. Summary & Best Practices {#summary}
#
# Key takeaways and recommendations for optimal MaskToContourConverter usage in clinical workflows.


# %%
# Create comprehensive performance comparison chart
def create_performance_matrix():
    """Create a comprehensive performance matrix for all tested configurations."""

    # Compile data from all our analyses
    matrix_data = []

    # Clinical profiles on complex shape
    for profile_name, profile in CLINICAL_PROFILES.items():
        result = profile_test_results[profile_name]["Complex Shape"]
        matrix_data.append(
            {
                "Configuration": f"{profile['name']}",
                "Category": "Clinical Profile",
                "Accuracy (mm)": profile["accuracy_threshold"],
                "Max Points": profile["max_points_per_contour"],
                "Actual Points": result["total_points"],
                "Time (ms)": result["processing_time_ms"],
                "Size (KB)": result["estimated_size_kb"],
                "Efficiency": (
                    result["total_points"] / result["processing_time_ms"]
                    if result["processing_time_ms"] > 0
                    else 0
                ),
            }
        )

    # Coordinate configurations
    for result in coord_results:
        matrix_data.append(
            {
                "Configuration": result["name"],
                "Category": "Coordinate System",
                "Accuracy (mm)": 0.5,  # Fixed for this test
                "Max Points": 300,  # Fixed for this test
                "Actual Points": result["total_points"],
                "Time (ms)": result["processing_time_ms"],
                "Size (KB)": result["estimated_size_kb"],
                "Efficiency": (
                    result["total_points"] / result["processing_time_ms"]
                    if result["processing_time_ms"] > 0
                    else 0
                ),
            }
        )

    return matrix_data


performance_matrix = create_performance_matrix()

# Create performance visualization
fig, axes = plt.subplots(2, 2, figsize=(18, 14))

# Separate clinical profiles and coordinate systems
clinical_data = [d for d in performance_matrix if d["Category"] == "Clinical Profile"]
coord_data = [d for d in performance_matrix if d["Category"] == "Coordinate System"]

# Plot 1: Clinical Profiles - Points vs Time
clinical_points = [d["Actual Points"] for d in clinical_data]
clinical_times = [d["Time (ms)"] for d in clinical_data]
clinical_names = [d["Configuration"] for d in clinical_data]

scatter1 = axes[0, 0].scatter(
    clinical_times,
    clinical_points,
    s=150,
    alpha=0.7,
    c=range(len(clinical_data)),
    cmap="viridis",
    edgecolors="black",
)
for i, name in enumerate(clinical_names):
    axes[0, 0].annotate(
        name.replace(" ", "\n"),
        (clinical_times[i], clinical_points[i]),
        xytext=(5, 5),
        textcoords="offset points",
        fontsize=9,
        fontweight="bold",
    )
axes[0, 0].set_xlabel("Processing Time (ms)")
axes[0, 0].set_ylabel("Total Points")
axes[0, 0].set_title("Clinical Profiles: Points vs Processing Time", fontweight="bold")
axes[0, 0].grid(True, alpha=0.3)

# Plot 2: Clinical Profiles - Accuracy vs File Size
clinical_accuracy = [d["Accuracy (mm)"] for d in clinical_data]
clinical_sizes = [d["Size (KB)"] for d in clinical_data]

scatter2 = axes[0, 1].scatter(
    clinical_accuracy,
    clinical_sizes,
    s=150,
    alpha=0.7,
    c=range(len(clinical_data)),
    cmap="viridis",
    edgecolors="black",
)
for i, name in enumerate(clinical_names):
    axes[0, 1].annotate(
        name.replace(" ", "\n"),
        (clinical_accuracy[i], clinical_sizes[i]),
        xytext=(5, 5),
        textcoords="offset points",
        fontsize=9,
        fontweight="bold",
    )
axes[0, 1].set_xlabel("Accuracy Threshold (mm)")
axes[0, 1].set_ylabel("Estimated File Size (KB)")
axes[0, 1].set_title("Clinical Profiles: Accuracy vs File Size", fontweight="bold")
axes[0, 1].set_xscale("log")
axes[0, 1].grid(True, alpha=0.3)

# Plot 3: Coordinate Systems Performance
coord_efficiency = [d["Efficiency"] for d in coord_data]
coord_names = [d["Configuration"] for d in coord_data]
coord_points = [d["Actual Points"] for d in coord_data]

bars = axes[1, 0].bar(
    range(len(coord_data)),
    coord_efficiency,
    color=["#FF6B6B", "#4ECDC4", "#45B7D1", "#96CEB4"],
    alpha=0.8,
)
axes[1, 0].set_xticks(range(len(coord_data)))
axes[1, 0].set_xticklabels(coord_names, rotation=45, ha="right")
axes[1, 0].set_ylabel("Efficiency (Points/ms)")
axes[1, 0].set_title("Coordinate Systems: Processing Efficiency", fontweight="bold")
axes[1, 0].grid(True, alpha=0.3, axis="y")

# Add value labels on bars
for bar, value in zip(bars, coord_efficiency):
    axes[1, 0].text(
        bar.get_x() + bar.get_width() / 2,
        bar.get_height() * 1.02,
        f"{value:.1f}",
        ha="center",
        va="bottom",
        fontweight="bold",
    )

# Plot 4: Overall Performance Comparison
all_configs = [d["Configuration"] for d in performance_matrix]
all_efficiency = [d["Efficiency"] for d in performance_matrix]
categories = [d["Category"] for d in performance_matrix]

# Color by category
colors = ["#FF9999" if cat == "Clinical Profile" else "#9999FF" for cat in categories]

bars = axes[1, 1].barh(
    range(len(performance_matrix)), all_efficiency, color=colors, alpha=0.8
)
axes[1, 1].set_yticks(range(len(performance_matrix)))
axes[1, 1].set_yticklabels([c.replace(" ", "\n") for c in all_configs], fontsize=8)
axes[1, 1].set_xlabel("Efficiency (Points/ms)")
axes[1, 1].set_title("Overall Performance Comparison", fontweight="bold")
axes[1, 1].grid(True, alpha=0.3, axis="x")

# Add legend
from matplotlib.patches import Patch

legend_elements = [
    Patch(facecolor="#FF9999", alpha=0.8, label="Clinical Profile"),
    Patch(facecolor="#9999FF", alpha=0.8, label="Coordinate System"),
]
axes[1, 1].legend(handles=legend_elements, loc="lower right")

plt.tight_layout()
plt.show()

print("✅ Comprehensive performance analysis complete")

# %%
# Final recommendations and best practices summary
print("\n" + "=" * 100)
print("🎯 MASKTOCONTOURCONVERTER: PARAMETER OPTIMIZATION BEST PRACTICES")
print("=" * 100)

print(
    """
🏆 OPTIMAL PARAMETER SELECTION STRATEGY:

1️⃣ IDENTIFY STRUCTURE TYPE:
   • Critical organs requiring sub-mm precision → stereotactic_critical profile
   • Treatment targets needing high accuracy → stereotactic_target profile  
   • Standard clinical structures → conventional_critical or conventional_target
   • Large anatomical regions → large_organ profile
   • Research/batch processing → research_batch profile

2️⃣ CONSIDER IMAGING PARAMETERS:
   • High-resolution CT (≤1mm pixels) → Can use tighter accuracy thresholds
   • Standard CT (1-2mm pixels) → Balance accuracy with performance  
   • Low-resolution imaging (>2mm pixels) → Focus on efficiency over precision

3️⃣ BALANCE COMPETING PRIORITIES:
   • Accuracy ↔ File Size: Lower accuracy threshold = more points = larger files
   • Speed ↔ Precision: Higher max points = better accuracy = slower processing
   • Memory ↔ Quality: More points = higher memory usage during processing

"""
)

print("\n📊 EVIDENCE-BASED RECOMMENDATIONS:")
print("-" * 50)

# Calculate best performers in each category
clinical_profiles = [
    d for d in performance_matrix if d["Category"] == "Clinical Profile"
]

# Find best balance (efficiency vs accuracy)
best_balance = max(
    clinical_profiles, key=lambda x: x["Efficiency"] / x["Accuracy (mm)"]
)
fastest = max(clinical_profiles, key=lambda x: x["Efficiency"])
most_accurate = min(clinical_profiles, key=lambda x: x["Accuracy (mm)"])
smallest_files = min(clinical_profiles, key=lambda x: x["Size (KB)"])

print(
    f"""
🏅 TOP PERFORMERS:

⚖️  BEST OVERALL BALANCE: {best_balance['Configuration']}
   • Accuracy: {best_balance['Accuracy (mm)']} mm
   • Processing: {best_balance['Time (ms)']:.1f} ms
   • File size: {best_balance['Size (KB)']:.1f} KB
   • Efficiency: {best_balance['Efficiency']:.1f} points/ms

⚡ FASTEST PROCESSING: {fastest['Configuration']}
   • Efficiency: {fastest['Efficiency']:.1f} points/ms
   • Processing: {fastest['Time (ms)']:.1f} ms
   • Trade-off: {fastest['Accuracy (mm)']} mm accuracy

🎯 HIGHEST ACCURACY: {most_accurate['Configuration']}
   • Accuracy: {most_accurate['Accuracy (mm)']} mm
   • Points: {most_accurate['Actual Points']}
   • Trade-off: {most_accurate['Time (ms)']:.1f} ms processing

💾 SMALLEST FILES: {smallest_files['Configuration']}
   • File size: {smallest_files['Size (KB)']:.1f} KB
   • Points: {smallest_files['Actual Points']}
   • Trade-off: {smallest_files['Accuracy (mm)']} mm accuracy
"""
)

print("\n🛠️ IMPLEMENTATION CHECKLIST:")
print("-" * 30)

print(
    """
✅ PRE-PROCESSING:
   □ Validate mask dimensions and content
   □ Check pixel spacing and slice thickness values
   □ Remove single-pixel artifacts if needed
   □ Verify mask has sufficient resolution for accuracy requirements

✅ PARAMETER SELECTION:
   □ Choose clinical profile based on structure importance and size
   □ Adjust accuracy threshold based on imaging resolution
   □ Set max points based on file size constraints
   □ Consider processing time requirements for workflow

✅ QUALITY ASSURANCE:
   □ Verify contour closure for DICOM compliance
   □ Check geometric accuracy against known structures
   □ Validate volume preservation within acceptable limits
   □ Monitor processing performance and file sizes

✅ ERROR HANDLING:
   □ Implement graceful handling of empty masks
   □ Set up fallback parameters for problematic cases
   □ Log conversion statistics for monitoring
   □ Provide meaningful error messages to users
"""
)

print("\n" + "=" * 100)
print("📚 SUMMARY: PARAMETER TUNING & OPTIMIZATION MASTERY ACHIEVED")
print("=" * 100)

print(
    f"""
This notebook has provided comprehensive guidance for optimizing MaskToContourConverter 
parameters across diverse clinical scenarios. You now have:

📈 Evidence-based parameter selection strategies
🎛️ Interactive tools for real-time parameter exploration  
🏥 Clinical profiles for standardized workflows
⚙️ Advanced configuration options for specialized needs
🛡️ Robust error handling for production environments

The combination of accuracy analysis, point optimization, clinical guidelines, and 
advanced configuration provides a complete framework for achieving optimal 
mask-to-contour conversion performance in any radiotherapy workflow.
"""
)

# %% [markdown]
# ---
#
# ## Next Steps
#
# Continue your MaskToContourConverter learning journey:
#
# - **Complex Geometry & Edge Cases** → `04_complex_geometry_edge_cases.ipynb`
# - **Integration & Comparisons** → `05_integration_comparisons.ipynb`
# - **Return to Basics** → `01_basic_mask_to_contour_usage.ipynb`
# - **Clinical Applications** → `02_clinical_scenarios.ipynb`
#
# ---
#
# **📧 Questions or Issues?**
# - Check the pyrt-dicom documentation
# - Review the source code in `src/pyrt_dicom/utils/contour_processing.py`
# - Refer to the comprehensive test suite for additional examples
#
# **🎯 Key Takeaway:**
# Parameter optimization is about finding the right balance between accuracy, performance, and file size for your specific clinical workflow. Use the evidence-based profiles as starting points, then fine-tune based on your requirements and constraints.
