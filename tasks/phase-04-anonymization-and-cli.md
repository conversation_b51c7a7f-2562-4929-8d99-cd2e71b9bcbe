# Phase 4: Anonymization & Privacy Protection + CLI Implementation

**Objective**: Implement comprehensive DICOM anonymization capabilities for clinical data sharing and research, plus complete the CLI interface for batch operations.

**Status**: 🟡 **IN PROGRESS**  
**Priority**: **HIGH** - Essential for clinical adoption and data sharing  
**Estimated Effort**: 4-5 weeks  

## Background

With Phases 1-3 complete (CT, RTSTRUCT, RTDOSE, RTPLAN creation), Phase 4 focuses on two critical components for clinical adoption:

1. **DICOM Anonymization**: HIPAA-compliant de-identification following DICOM PS 3.15
2. **CLI Interface**: Complete the command-line interface for batch operations and clinical workflows

Both components are essential for clinical data sharing, research collaboration, and integration into existing medical physics workflows.

## Part A: DICOM Anonymization Implementation

### Objectives

**Primary Goals**:
- **Multi-level anonymization profiles** (basic, clinical, research, custom)
- **HIPAA-compliant de-identification** following DICOM PS 3.15 Application Level Confidentiality Profile
- **RT-specific anonymization** preserving clinical validity of dose and geometric data
- **Batch anonymization** for complete treatment plans
- **Audit logging** for regulatory compliance
- **Integration with PyMedPhys** anonymization patterns (learning from existing implementation)

### Technical Implementation

#### 1. Core Anonymization Framework

**Action**: Create anonymization system building on PyMedPhys patterns while adding RT-specific handling.

**Implementation**:
```python
# src/pyrt_dicom/anonymization/__init__.py
from .anonymizer import DicomAnonymizer, TreatmentPlanAnonymizer
from .profiles import AnonymizationProfiles
from .audit import AnonymizationAuditor

# src/pyrt_dicom/anonymization/anonymizer.py
class DicomAnonymizer:
    """
    DICOM anonymization with RT-specific handling.
    
    Builds on PyMedPhys anonymization patterns while adding:
    - RT-specific tag preservation for clinical validity
    - Consistent UID mapping across related RT objects
    - Treatment plan anonymization workflows
    """
    
    def __init__(self, profile='basic', auditor=None):
        self.profile = AnonymizationProfiles.get_profile(profile)
        self.auditor = auditor or AnonymizationAuditor()
        self._uid_mapping = {}  # Consistent UID anonymization
        
    def anonymize_dataset(self, dataset, profile=None):
        """
        Anonymize DICOM dataset with RT-specific handling.
        
        Args:
            dataset: pydicom.Dataset to anonymize
            profile: Override default anonymization profile
            
        Returns:
            Anonymized dataset with preserved RT relationships
        """
        # Use PyMedPhys anonymize_dataset as foundation
        from pymedphys._dicom.anonymise import anonymise_dataset
        
        # Apply base anonymization
        anon_dataset = anonymise_dataset(
            dataset,
            keywords_to_leave_unchanged=self.profile.get('preserve_tags', []),
            replacement_strategy=self._get_rt_replacement_strategy(),
            identifying_keywords=self.profile.get('identifying_keywords')
        )
        
        # Apply RT-specific post-processing
        self._preserve_rt_relationships(anon_dataset)
        self._validate_rt_clinical_integrity(anon_dataset)
        
        # Log anonymization operation
        self.auditor.log_anonymization(dataset, anon_dataset)
        
        return anon_dataset
        
    def _get_rt_replacement_strategy(self):
        """RT-specific replacement strategy preserving clinical data."""
        # Extend PyMedPhys strategy with RT-specific rules
        base_strategy = get_pymedphys_strategy()
        
        rt_strategy = {
            # Preserve dose scaling and units
            'DoseGridScaling': lambda x: x,  # Critical for dose accuracy
            'DoseUnits': lambda x: x,        # Must preserve for clinical validity
            
            # Preserve geometric relationships
            'PixelSpacing': lambda x: x,
            'SliceThickness': lambda x: x,
            'ImagePositionPatient': self._anonymize_position,
            
            # Anonymize but preserve structure names for analysis
            'ROIName': self._anonymize_structure_name,
            'BeamName': self._anonymize_beam_name,
        }
        
        return {**base_strategy, **rt_strategy}

class TreatmentPlanAnonymizer(DicomAnonymizer):
    """
    Specialized anonymizer for complete treatment plans.
    
    Handles anonymization of related RT objects (CT, STRUCT, DOSE, PLAN)
    while preserving all clinical relationships and references.
    """
    
    def anonymize_treatment_plan(self, plan_files, output_directory, **kwargs):
        """
        Anonymize complete treatment plan with consistent mapping.
        
        Args:
            plan_files: Dict with keys 'ct', 'struct', 'dose', 'plan'
            output_directory: Where to save anonymized files
            
        Returns:
            Dict of anonymized file paths with preserved relationships
        """
        # Create consistent anonymization mapping
        self._create_plan_anonymization_map(plan_files)
        
        # Anonymize each component with consistent UIDs
        anonymized_files = {}
        for modality, filepath in plan_files.items():
            anonymized_files[modality] = self._anonymize_with_mapping(
                filepath, output_directory, modality
            )
            
        # Validate all references are preserved
        self._validate_plan_relationships(anonymized_files)
        
        return anonymized_files
```

#### 2. Anonymization Profiles

**Action**: Define clinical anonymization profiles for different use cases.

**Implementation**:
```python
# src/pyrt_dicom/anonymization/profiles.py
class AnonymizationProfiles:
    """
    Predefined anonymization profiles for different clinical scenarios.
    """
    
    BASIC_PROFILE = {
        'name': 'basic',
        'description': 'Basic anonymization removing direct identifiers',
        'remove_patient_identifiers': True,
        'preserve_dates': False,
        'preserve_geometric_data': True,
        'preserve_dose_data': True,
        'preserve_structure_names': False,
        'preserve_tags': [
            'DoseGridScaling', 'DoseUnits', 'PixelSpacing', 
            'SliceThickness', 'ImageOrientationPatient'
        ]
    }
    
    CLINICAL_PROFILE = {
        'name': 'clinical',
        'description': 'Clinical anonymization preserving analysis capability',
        'remove_patient_identifiers': True,
        'preserve_dates': True,  # Preserve relative dates for temporal analysis
        'preserve_geometric_data': True,
        'preserve_dose_data': True,
        'preserve_structure_names': True,  # Keep ROI names for clinical analysis
        'preserve_physician_info': False,
        'date_shift_range': (30, 365),  # Random shift 30-365 days
        'preserve_tags': [
            'DoseGridScaling', 'DoseUnits', 'PixelSpacing',
            'SliceThickness', 'ImageOrientationPatient',
            'ROIName', 'BeamName', 'TreatmentMachineName'
        ]
    }
    
    RESEARCH_PROFILE = {
        'name': 'research',
        'description': 'Research anonymization for multi-institutional studies',
        'remove_patient_identifiers': True,
        'preserve_dates': True,
        'preserve_geometric_data': True,
        'preserve_dose_data': True,
        'preserve_structure_names': True,
        'preserve_institution_info': False,  # Remove for multi-site studies
        'anonymize_study_descriptions': True,
        'preserve_tags': [
            'DoseGridScaling', 'DoseUnits', 'PixelSpacing',
            'SliceThickness', 'ImageOrientationPatient',
            'ROIName', 'BeamName', 'Modality'
        ]
    }
    
    HIPAA_PROFILE = {
        'name': 'hipaa',
        'description': 'HIPAA Safe Harbor compliant anonymization',
        'remove_patient_identifiers': True,
        'remove_dates': True,  # HIPAA requires date removal
        'preserve_geometric_data': True,
        'preserve_dose_data': True,
        'remove_free_text': True,
        'age_threshold': 89,  # HIPAA age limit
        'preserve_tags': [
            'DoseGridScaling', 'DoseUnits', 'PixelSpacing',
            'SliceThickness', 'ImageOrientationPatient'
        ]
    }
```

#### 3. Audit Logging

**Action**: Implement comprehensive audit logging for regulatory compliance.

**Implementation**:
```python
# src/pyrt_dicom/anonymization/audit.py
class AnonymizationAuditor:
    """
    Audit logging for DICOM anonymization operations.
    
    Provides comprehensive logging for regulatory compliance
    and anonymization operation tracking.
    """
    
    def __init__(self, log_file=None, encryption_key=None):
        self.log_file = log_file or 'dicom_anonymization.log'
        self.encryption_key = encryption_key
        self._setup_logging()
        
    def log_anonymization(self, original_dataset, anonymized_dataset):
        """Log anonymization operation with before/after metadata."""
        audit_entry = {
            'timestamp': datetime.utcnow().isoformat(),
            'operation': 'anonymization',
            'log_id': str(uuid4()),
            'original_metadata': self._extract_audit_metadata(original_dataset),
            'anonymized_metadata': self._extract_audit_metadata(anonymized_dataset),
            'tags_modified': self._get_modified_tags(original_dataset, anonymized_dataset),
            'profile_used': self.profile_name,
            'compliance_level': 'HIPAA' if self.hipaa_compliant else 'Basic'
        }
        
        self._write_audit_entry(audit_entry)
        return audit_entry
        
    def generate_compliance_report(self, audit_entries):
        """Generate compliance report for regulatory review."""
        report = {
            'report_id': str(uuid4()),
            'generation_date': datetime.utcnow().isoformat(),
            'total_operations': len(audit_entries),
            'compliance_summary': self._analyze_compliance(audit_entries),
            'risk_assessment': self._assess_privacy_risk(audit_entries)
        }
        return report
```

## Part B: CLI Implementation

### Objectives

**Complete CLI Interface** from `src/pyrt_dicom/cli.py` with:
- **Batch anonymization** commands
- **DICOM creation** workflows
- **Validation** and quality assurance
- **Integration** with existing clinical tools

### Technical Implementation

#### 1. Complete CLI Commands

**Action**: Implement the planned CLI commands from the existing framework.

**Implementation**:
```python
# src/pyrt_dicom/cli.py (additions to existing file)

@app.command()
def anonymize(
    input_path: str = typer.Argument(..., help="Input DICOM file or directory"),
    output_path: str = typer.Option(None, "--output", "-o", help="Output path"),
    profile: str = typer.Option("basic", "--profile", "-p", help="Anonymization profile"),
    batch: bool = typer.Option(False, "--batch", help="Batch process directory"),
    audit_log: str = typer.Option(None, "--audit-log", help="Audit log file path"),
    preserve_structure_names: bool = typer.Option(True, "--preserve-structures", help="Keep ROI names"),
):
    """
    Anonymize DICOM files for clinical data sharing and research.
    
    Supports individual files or batch processing of complete treatment plans.
    Includes comprehensive audit logging for regulatory compliance.
    
    Examples:
        # Anonymize single RT Structure Set
        pyrt-dicom anonymize rtstruct.dcm --profile clinical
        
        # Batch anonymize complete treatment plan
        pyrt-dicom anonymize /path/to/patient/ --batch --profile research
        
        # HIPAA-compliant anonymization with audit logging
        pyrt-dicom anonymize patient_data/ --profile hipaa --audit-log audit.log
    """
    from pyrt_dicom.anonymization import DicomAnonymizer, TreatmentPlanAnonymizer
    
    if batch:
        anonymizer = TreatmentPlanAnonymizer(profile=profile)
        result = anonymizer.anonymize_directory(input_path, output_path)
    else:
        anonymizer = DicomAnonymizer(profile=profile)
        result = anonymizer.anonymize_file(input_path, output_path)
    
    console.print(f"✅ Anonymization complete: {result}")

@app.command()
def create(
    modality: str = typer.Argument(..., help="DICOM modality to create (ct, rtstruct, rtdose, rtplan)"),
    input_data: str = typer.Option(..., "--input", "-i", help="Input data file (numpy, masks, etc.)"),
    reference: str = typer.Option(None, "--reference", "-r", help="Reference DICOM file"),
    output: str = typer.Option(..., "--output", "-o", help="Output DICOM file"),
    validate: bool = typer.Option(True, "--validate/--no-validate", help="Validate before saving"),
):
    """
    Create DICOM RT files from various input formats.
    
    Supports creation of CT, RTSTRUCT, RTDOSE, and RTPLAN files from
    common medical physics data formats (NumPy arrays, masks, etc.).
    
    Examples:
        # Create RT Structure Set from masks
        pyrt-dicom create rtstruct --input masks.npz --reference ct.dcm --output struct.dcm
        
        # Create RT Dose from dose array
        pyrt-dicom create rtdose --input dose.npy --reference ct.dcm --output dose.dcm
        
        # Create CT series from array
        pyrt-dicom create ct --input ct_array.npy --output ct_series/
    """
    from pyrt_dicom import CTSeries, RTStructureSet, RTDose, RTPlan
    
    creators = {
        'ct': CTSeries,
        'rtstruct': RTStructureSet, 
        'rtdose': RTDose,
        'rtplan': RTPlan
    }
    
    if modality not in creators:
        console.print(f"❌ Unsupported modality: {modality}")
        raise typer.Exit(1)
        
    # Implementation for each modality type
    creator_class = creators[modality]
    # ... specific creation logic based on modality

@app.command()
def validate(
    input_path: str = typer.Argument(..., help="DICOM file or directory to validate"),
    clinical_checks: bool = typer.Option(False, "--clinical-checks", help="Perform clinical validation"),
    compliance_level: str = typer.Option("basic", "--compliance", help="DICOM compliance level"),
):
    """
    Validate DICOM files for clinical compliance and data integrity.
    
    Performs comprehensive validation including DICOM conformance,
    clinical parameter validation, and geometric consistency checks.
    
    Examples:
        # Basic DICOM validation
        pyrt-dicom validate rtdose.dcm
        
        # Clinical validation with safety checks
        pyrt-dicom validate treatment_plan/ --clinical-checks
        
        # Strict compliance validation
        pyrt-dicom validate files/ --compliance strict
    """
    from pyrt_dicom.validation import DicomValidator
    
    validator = DicomValidator(compliance_level=compliance_level)
    results = validator.validate_path(input_path, clinical_checks=clinical_checks)
    
    # Display validation results
    _display_validation_results(results)

@app.command()
def batch_process(
    config_file: str = typer.Argument(..., help="Batch processing configuration file"),
    dry_run: bool = typer.Option(False, "--dry-run", help="Show what would be processed"),
    parallel: bool = typer.Option(True, "--parallel/--sequential", help="Parallel processing"),
):
    """
    Batch process multiple patients with configuration file.
    
    Supports complex batch operations including anonymization,
    format conversion, and quality assurance workflows.
    
    Examples:
        # Batch anonymize research dataset
        pyrt-dicom batch-process research_config.yaml
        
        # Dry run to preview operations
        pyrt-dicom batch-process config.yaml --dry-run
    """
    from pyrt_dicom.batch import BatchProcessor
    
    processor = BatchProcessor(config_file)
    if dry_run:
        processor.preview_operations()
    else:
        results = processor.execute(parallel=parallel)
        _display_batch_results(results)
```

#### 2. Integration with Existing Tools

**Action**: Add CLI commands for integration with PyMedPhys and other tools.

**Implementation**:
```python
@app.command()
def convert(
    input_format: str = typer.Option(..., "--from", help="Input format (pymedphys, scikit-rt, etc.)"),
    output_format: str = typer.Option("dicom", "--to", help="Output format"),
    input_path: str = typer.Argument(..., help="Input file or directory"),
    output_path: str = typer.Option(..., "--output", "-o", help="Output path"),
):
    """
    Convert between different RT data formats.
    
    Supports conversion from PyMedPhys, scikit-rt, and other common
    medical physics data formats to DICOM RT files.
    
    Examples:
        # Convert PyMedPhys data to DICOM
        pyrt-dicom convert --from pymedphys --to dicom data.pkl --output dicom/
        
        # Convert scikit-rt objects to DICOM
        pyrt-dicom convert --from scikit-rt data/ --output dicom_files/
    """
    # Implementation for format conversion
```

## Deliverables

### Week 1-2: Anonymization Foundation
- [ ] Implement core `DicomAnonymizer` class building on PyMedPhys patterns
- [ ] Create anonymization profiles (basic, clinical, research, HIPAA)
- [ ] Implement RT-specific anonymization rules
- [ ] Add audit logging framework

### Week 3: Treatment Plan Anonymization
- [ ] Implement `TreatmentPlanAnonymizer` for complete plans
- [ ] Add batch anonymization capabilities
- [ ] Ensure UID consistency across related objects
- [ ] Validate clinical relationships are preserved

### Week 4: CLI Implementation
- [ ] Complete CLI commands (anonymize, create, validate, batch-process)
- [ ] Add integration commands for PyMedPhys/scikit-rt
- [ ] Implement batch processing with configuration files
- [ ] Add comprehensive help and examples

### Week 5: Testing and Documentation
- [ ] Comprehensive testing with clinical datasets
- [ ] Validate HIPAA compliance
- [ ] Performance optimization for large datasets
- [ ] CLI documentation and usage examples

## Success Criteria

### Anonymization
- [ ] HIPAA-compliant anonymization following DICOM PS 3.15
- [ ] Preserve clinical validity of RT data after anonymization
- [ ] Maintain DICOM reference relationships in anonymized datasets
- [ ] Support batch anonymization of complete treatment plans
- [ ] Comprehensive audit logging for compliance requirements

### CLI
- [ ] Complete command-line interface for all major operations
- [ ] Batch processing capabilities for clinical workflows
- [ ] Integration with existing medical physics tools
- [ ] Comprehensive help and documentation
- [ ] Performance suitable for clinical use (<30 seconds for typical operations)

## Dependencies

- **PyMedPhys anonymization module**: Foundation for anonymization patterns
- **Existing pyrt-dicom core**: CT, STRUCT, DOSE, PLAN creation capabilities
- **Typer CLI framework**: Already established in existing CLI
- **Clinical validation framework**: From Phase 3 implementation

## Future Enhancements

- **GUI interface**: Desktop application for non-technical users
- **Cloud integration**: Batch processing on cloud platforms
- **Advanced anonymization**: Machine learning-based de-identification
- **Compliance reporting**: Automated regulatory compliance reports